import os
import time
from langchain_openai import ChatOpenAI
from openai import RateLimitError

key = os.getenv("openai_key")

llm = ChatOpenAI(openai_api_key=key)

try:
    print(llm.invoke("你好"))
except RateLimitError:
    print("API请求超限，请检查你的OpenAI账户配额")
    # 或者添加自动重试逻辑
    # for _ in range(3):
    #     try:
    #         print(llm.invoke("你好"))
    #         break
    #     except RateLimitError:
    #         time.sleep(5)