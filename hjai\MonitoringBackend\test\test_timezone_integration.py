"""
时区处理集成测试

测试整个系统的时区处理一致性，包括数据库、API、监控脚本等
"""

import os
import sys
import asyncio
import json
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入相关模块
from config.timezone_utils import TZ, ModernJSONEncoder
from config.db import TORTOISE_ORM
from tortoise import Tortoise

class TestSystemTimezoneIntegration:
    """系统时区集成测试"""
    
    async def setup_database(self):
        """设置测试数据库"""
        # 跳过数据库测试，因为需要MySQL连接
        print("  ⚠️  跳过数据库测试（需要MySQL连接）")
        return False
    
    async def cleanup_database(self):
        """清理测试数据库"""
        await Tortoise.close_connections()
    
    async def test_model_timezone_handling(self):
        """测试模型的时区处理"""
        # 模拟模型的时区处理逻辑
        from datetime import datetime

        # 模拟创建时间戳
        current_utc = TZ.now_utc().replace(tzinfo=None)

        # 模拟显示格式转换
        display_time = TZ.to_display_format(current_utc, source_tz=TZ.UTC)

        # 验证格式
        assert "+08:00" in display_time

        print(f"✅ 模型时区处理测试通过（模拟）")
        print(f"   UTC时间戳: {current_utc}")
        print(f"   显示格式: {display_time}")
    
    def test_api_response_format(self):
        """测试API响应格式"""
        # 模拟API响应数据
        response_data = {
            "ip": "*************",
            "timestamp": datetime(2023, 12, 25, 6, 30, 0),  # UTC时间
            "usage_percent": 75.5
        }
        
        # 使用自定义JSON编码器
        json_str = json.dumps(response_data, cls=ModernJSONEncoder)
        
        # 验证时区转换
        assert "+08:00" in json_str
        assert "14:30:00" in json_str  # UTC 06:30 -> 上海时区 14:30
        
        print(f"✅ API响应格式测试通过")
        print(f"   JSON输出: {json_str}")
    
    def test_time_range_query_conversion(self):
        """测试时间范围查询的转换"""
        # 模拟前端发送的时间范围（上海时区）
        start_time_str = "2023-12-25T08:00:00+08:00"
        end_time_str = "2023-12-25T18:00:00+08:00"
        
        # 转换为UTC时间用于数据库查询
        start_time_utc = TZ.from_input(start_time_str)
        end_time_utc = TZ.from_input(end_time_str)
        
        # 验证转换结果
        assert start_time_utc.hour == 0  # 上海时区08:00 -> UTC 00:00
        assert end_time_utc.hour == 10   # 上海时区18:00 -> UTC 10:00
        assert start_time_utc.tzinfo == timezone.utc
        assert end_time_utc.tzinfo == timezone.utc
        
        print(f"✅ 时间范围查询转换测试通过")
        print(f"   输入范围: {start_time_str} ~ {end_time_str}")
        print(f"   UTC范围: {start_time_utc} ~ {end_time_utc}")
    
    def test_monitoring_script_time_generation(self):
        """测试监控脚本的时间生成"""
        # 模拟监控脚本生成时间戳
        current_utc = TZ.now_utc()
        current_shanghai = TZ.now_shanghai()
        
        # 验证时间差
        time_diff = (current_shanghai.hour - current_utc.hour) % 24
        assert time_diff == 8  # 上海时区比UTC快8小时
        
        # 测试存储格式（naive UTC时间）
        storage_time = current_utc.replace(tzinfo=None)
        assert storage_time.tzinfo is None
        
        print(f"✅ 监控脚本时间生成测试通过")
        print(f"   UTC时间: {current_utc}")
        print(f"   上海时间: {current_shanghai}")
        print(f"   存储格式: {storage_time}")
    
    def test_timezone_consistency_across_components(self):
        """测试各组件间的时区一致性"""
        # 创建一个基准UTC时间
        base_utc = datetime(2023, 12, 25, 6, 30, 0, tzinfo=timezone.utc)
        
        # 1. 数据库存储格式
        db_format = base_utc.replace(tzinfo=None)
        
        # 2. API显示格式
        api_format = TZ.to_display_format(base_utc)
        
        # 3. 监控脚本生成格式
        script_format = base_utc.replace(tzinfo=None)
        
        # 4. 前端输入解析
        parsed_time = TZ.from_input(api_format)
        
        # 验证一致性
        assert db_format == script_format  # 数据库和脚本使用相同格式
        assert parsed_time == base_utc     # 往返转换保持一致
        assert "+08:00" in api_format      # API显示包含时区信息
        
        print(f"✅ 组件间时区一致性测试通过")
        print(f"   基准UTC: {base_utc}")
        print(f"   数据库格式: {db_format}")
        print(f"   API格式: {api_format}")
        print(f"   解析回UTC: {parsed_time}")

class TestTimezoneEdgeCases:
    """时区边界情况测试"""
    
    def test_daylight_saving_time(self):
        """测试夏令时处理（中国不使用夏令时，但测试其他时区）"""
        # 创建一个美国东部时间（有夏令时）
        from zoneinfo import ZoneInfo
        
        # 夏令时期间
        summer_time = datetime(2023, 7, 15, 12, 0, 0, tzinfo=ZoneInfo("America/New_York"))
        # 标准时间期间
        winter_time = datetime(2023, 1, 15, 12, 0, 0, tzinfo=ZoneInfo("America/New_York"))
        
        # 转换为UTC
        summer_utc = TZ.to_utc(summer_time)
        winter_utc = TZ.to_utc(winter_time)
        
        # 验证时差不同（夏令时UTC-4，标准时间UTC-5）
        assert summer_utc.hour == 16  # 12:00 EDT -> 16:00 UTC
        assert winter_utc.hour == 17  # 12:00 EST -> 17:00 UTC
        
        print(f"✅ 夏令时处理测试通过")
    
    def test_year_boundary(self):
        """测试跨年边界"""
        # 创建跨年时间
        year_end_utc = datetime(2023, 12, 31, 20, 0, 0, tzinfo=timezone.utc)
        
        # 转换为上海时区（应该是第二年）
        shanghai_time = TZ.to_shanghai(year_end_utc)
        
        # 验证跨年
        assert shanghai_time.year == 2024
        assert shanghai_time.month == 1
        assert shanghai_time.day == 1
        assert shanghai_time.hour == 4  # UTC 20:00 -> 上海时区 04:00(+1天)
        
        print(f"✅ 跨年边界测试通过")
    
    def test_leap_year(self):
        """测试闰年处理"""
        # 创建闰年2月29日的时间
        leap_day = datetime(2024, 2, 29, 12, 0, 0, tzinfo=timezone.utc)
        
        # 转换为上海时区
        shanghai_leap = TZ.to_shanghai(leap_day)
        
        # 验证日期正确
        assert shanghai_leap.year == 2024
        assert shanghai_leap.month == 2
        assert shanghai_leap.day == 29
        assert shanghai_leap.hour == 20
        
        print(f"✅ 闰年处理测试通过")

async def run_integration_tests():
    """运行集成测试"""
    print("开始运行时区处理集成测试...")
    
    # 系统集成测试
    system_test = TestSystemTimezoneIntegration()
    
    try:
        # 设置数据库（跳过）
        db_available = await system_test.setup_database()

        # 运行测试
        await system_test.test_model_timezone_handling()
        system_test.test_api_response_format()
        system_test.test_time_range_query_conversion()
        system_test.test_monitoring_script_time_generation()
        system_test.test_timezone_consistency_across_components()

        print("\n🎉 系统集成测试全部通过！")

    except Exception as e:
        print(f"\n❌ 系统集成测试失败: {str(e)}")
        raise
    finally:
        # 清理数据库（如果需要）
        if hasattr(system_test, 'cleanup_database'):
            try:
                await system_test.cleanup_database()
            except:
                pass
    
    # 边界情况测试
    print("\n开始运行边界情况测试...")
    edge_test = TestTimezoneEdgeCases()
    
    try:
        edge_test.test_daylight_saving_time()
        edge_test.test_year_boundary()
        edge_test.test_leap_year()
        
        print("\n🎉 边界情况测试全部通过！")
        
    except Exception as e:
        print(f"\n❌ 边界情况测试失败: {str(e)}")
        raise

def main():
    """主函数"""
    try:
        asyncio.run(run_integration_tests())
        print("\n✅ 所有时区集成测试通过！")
        return True
    except Exception as e:
        print(f"\n❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
