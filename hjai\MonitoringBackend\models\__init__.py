# 导入时区处理模块
from config.timezone import now, utcnow, SHANGHAI_TZ

# 导入基础模型类
from models.base_model import BaseModel

# 导入所有模型类以确保它们被Tortoise ORM正确注册

# 用户模型
from models.ip_user import IPUser

# GPU相关模型
from models.gpu_stats import GPUStatsSummary, GPUStatsDetail

# 内存相关模型
from models.memory_stats import MemoryStats

# 网络相关模型
from models.network_stats import NetworkStatsSummary, NetworkStatsDetail

# CPU相关模型
from models.cpu_stats import CPUStats, CPUTopProcess

# 自定义监控模型
from models.custom_monitor import MonitorItem, MonitorItemIP, MonitorData 