:root {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  /* Color System */
  --color-primary: #3b82f6;
  --color-primary-light: #eff6ff;
  --color-primary-dark: #2563eb;
  
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-success-dark: #059669;
  
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-warning-dark: #d97706;
  
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  --color-error-dark: #dc2626;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Spacing System (8px base) */
  --spacing-1: 0.25rem; /* 4px */
  --spacing-2: 0.5rem;  /* 8px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-4: 1rem;    /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem;  /* 24px */
  --spacing-8: 2rem;    /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-12: 3rem;   /* 48px */
  --spacing-16: 4rem;   /* 64px */

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--color-gray-50);
  color: var(--color-gray-800);
}

#app {
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
  font-weight: 600;
  color: var(--color-gray-900);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  line-height: 1.6;
  color: var(--color-gray-700);
}

/* Code styling */
code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  background-color: var(--color-gray-100);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  color: var(--color-gray-800);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Element Plus customizations */
.el-card {
  border: none;
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-xl);
}

.el-card__header {
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-4) var(--spacing-6);
}

.el-card__body {
  padding: var(--spacing-6);
}

.el-button {
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.el-table th {
  background-color: var(--color-gray-50);
  color: var(--color-gray-700);
  font-weight: 600;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: var(--color-gray-50);
}

.el-input__wrapper {
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.el-input__wrapper:hover {
  box-shadow: var(--shadow-sm);
}

.el-select .el-input__wrapper {
  border-radius: var(--radius-lg);
}

.el-dialog {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

.el-dialog__header {
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-4);
}

.el-dialog__body {
  padding: 0 var(--spacing-6) var(--spacing-4);
}

.el-dialog__footer {
  padding: var(--spacing-4) var(--spacing-6) var(--spacing-6);
}

.el-tag {
  border-radius: var(--radius-md);
  font-weight: 500;
}

.el-alert {
  border-radius: var(--radius-lg);
  border: none;
}

.el-progress-bar__outer {
  border-radius: var(--radius-md);
}

.el-progress-bar__inner {
  border-radius: var(--radius-md);
}

/* Custom utility classes */
.text-primary { color: var(--color-primary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-primary { background-color: var(--color-primary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }

/* Animation classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-normal);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Responsive design helpers */
@media (max-width: 768px) {
  :root {
    --spacing-6: 1rem;
    --spacing-8: 1.5rem;
  }
  
  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  
  .el-card__body {
    padding: var(--spacing-4);
  }
  
  .el-dialog {
    margin: var(--spacing-4);
    width: calc(100% - 2rem) !important;
  }
}

/* Focus styles for accessibility */
.el-button:focus-visible,
.el-input__wrapper:focus-within,
.el-select:focus-within .el-input__wrapper {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Print styles */
@media print {
  .el-aside,
  .el-header,
  .el-button,
  .el-pagination {
    display: none !important;
  }
  
  .el-main {
    padding: 0 !important;
  }
  
  .el-card {
    box-shadow: none !important;
    border: 1px solid var(--color-gray-300) !important;
  }
}