@echo off
setlocal enabledelayedexpansion

:: 结果文件
set RESULT_FILE=bench_results.txt
set FINAL_TABLE=result.md

:: 清空旧文件
type nul > %RESULT_FILE%
type nul > %FINAL_TABLE%

:: 预定义测试类型顺序
set TEST_TYPES=FP8 INT8 FP16 BF16 TF32 FP32

echo 检查CUDA环境... | tee -a %RESULT_FILE%

:: 检查CUDA环境
set CUDA_PATH=

:: 检查常见CUDA安装路径
if exist "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA" (
    for /d %%i in ("C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\*") do (
        if exist "%%i\bin\cublas64_*.dll" (
            set CUDA_PATH=%%i
            echo 找到CUDA路径: !CUDA_PATH! | tee -a %RESULT_FILE%
        )
    )
)

:: 如果找到CUDA路径，设置环境变量
if defined CUDA_PATH (
    set PATH=!CUDA_PATH!\bin;!PATH!
    echo 已添加CUDA路径到系统PATH: !CUDA_PATH!\bin | tee -a %RESULT_FILE%
    
    :: 检查可执行文件是否存在
    if not exist cublasMatmulBench.exe (
        echo 错误: cublasMatmulBench.exe 可执行文件不存在 | tee -a %RESULT_FILE%
        goto :error
    )
    
    echo CUDA环境检查通过，开始运行测试... | tee -a %RESULT_FILE%
    
    :: 运行所有测试
    echo Running FP8 GEMM... | tee -a %RESULT_FILE%
    cublasMatmulBench.exe -P=qqssq -m=15360 -n=18176 -k=8192 -T=1000 -ta=1 -B=0 -p=0 | tee -a %RESULT_FILE%
    
    echo Running INT8 GEMM... | tee -a %RESULT_FILE%
    cublasMatmulBench.exe -P=bisb_imma -m=40960 -n=52548 -k=16384 -T=1000 -ta=1 -B=0 -p=0 | tee -a %RESULT_FILE%
    
    echo Running FP16 GEMM... | tee -a %RESULT_FILE%
    cublasMatmulBench.exe -P=hsh -m=15360 -n=18176 -k=8192 -T=1000 -tb=1 -B=0 -p=0 | tee -a %RESULT_FILE%
    
    echo Running BF16 GEMM... | tee -a %RESULT_FILE%
    cublasMatmulBench.exe -P=tst -m=15360 -n=18176 -k=16384 -T=1000 -tb=1 -B=0 -p=0 | tee -a %RESULT_FILE%
    
    echo Running TF32 GEMM... | tee -a %RESULT_FILE%
    cublasMatmulBench.exe -P=sss_fast_tf32 -m=15360 -n=18176 -k=4096 -T=1000 -tb=1 -B=0 -p=0 | tee -a %RESULT_FILE%
    
    echo Running FP32 GEMM... | tee -a %RESULT_FILE%
    cublasMatmulBench.exe -P=sss -m=15360 -n=18176 -k=4096 -T=1000 -tb=1 -B=0 -p=0 | tee -a %RESULT_FILE%
    
    :: 生成报告
    call :generate_report
) else (
    echo 错误: 找不到CUDA安装路径 | tee -a %RESULT_FILE%
    goto :error
)

goto :eof

:error
echo 错误: CUDA环境检查失败，无法运行测试。 | tee -a %RESULT_FILE%
echo 请确保CUDA工具包已正确安装，并且库文件路径已正确设置。 | tee -a %RESULT_FILE%

:: 生成错误报告
echo | 测试类型 | 矩阵尺寸 (M×N×K) | 实测性能 (TFLOPS) | > %FINAL_TABLE%
echo |----------|-------------------|--------------------| >> %FINAL_TABLE%

for %%t in (%TEST_TYPES%) do (
    echo | %%t       | 环境错误            | N/A                | >> %FINAL_TABLE%
)

type %FINAL_TABLE%
goto :eof

:generate_report
:: 生成表格头
echo | 测试类型 | 矩阵尺寸 (M×N×K) | 实测性能 (TFLOPS) | > %FINAL_TABLE%
echo |----------|-------------------|--------------------| >> %FINAL_TABLE%

:: 提取结果数据并填充表格
setlocal enabledelayedexpansion

for %%t in (%TEST_TYPES%) do (
    set "found=0"
    set "dimensions=N/A"
    set "perf=N/A"
    
    :: 根据测试类型设置矩阵尺寸
    if "%%t"=="FP8" set dimensions=15360×18176×8192
    if "%%t"=="INT8" set dimensions=40960×52548×16384
    if "%%t"=="FP16" set dimensions=15360×18176×8192
    if "%%t"=="BF16" set dimensions=15360×18176×16384
    if "%%t"=="TF32" set dimensions=15360×18176×4096
    if "%%t"=="FP32" set dimensions=15360×18176×4096
    
    :: 查找性能结果
    for /f "tokens=*" %%l in ('findstr /C:"Running %%t GEMM" %RESULT_FILE%') do (
        set "current_type=%%t"
    )
    
    for /f "tokens=*" %%l in ('findstr /C:"CUDA : elapsed" %RESULT_FILE%') do (
        if "!current_type!"=="%%t" (
            for /f "tokens=3 delims==" %%g in ('echo %%l ^| findstr /C:"Gflops = "') do (
                set "gflops=%%g"
                set /a "tflops=!gflops:~0,-3!/1000"
                set "perf=!tflops!.!gflops:~-3!"
                set "found=1"
            )
        )
    )
    
    echo | %%t       | !dimensions!      | !perf!             | >> %FINAL_TABLE%
)

echo 测试结果已保存到 %FINAL_TABLE%
type %FINAL_TABLE%

goto :eof