# 自定义监控项使用指南

## 概述

自定义监控项模块允许您创建和管理自定义的监控命令，实现对服务器的个性化监控。本指南将帮助您快速上手并充分利用这一功能。

## 快速开始

### 1. 创建第一个监控项

#### 步骤1：准备监控命令

首先，确定您要执行的监控命令。例如：
- 获取CPU使用率：`cat /proc/loadavg`
- 检查内存使用：`free -m`
- 查看磁盘空间：`df -h`

#### 步骤2：测试命令

在创建监控项之前，建议先测试命令：

```bash
curl -X POST http://your-server/monitor/test \
  -H "Content-Type: application/json" \
  -d '{
    "command": "cat /proc/loadavg",
    "ip": "*************",
    "username": "root",
    "password": "your-password",
    "timeout": 30,
    "data_type": "string"
  }'
```

#### 步骤3：创建监控项

测试成功后，创建监控项：

```bash
curl -X POST http://your-server/monitor/items \
  -H "Content-Type: application/json" \
  -d '{
    "name": "系统负载",
    "command": "cat /proc/loadavg",
    "description": "获取系统1分钟、5分钟、15分钟的平均负载",
    "data_type": "string",
    "timeout": 30,
    "retry_count": 2,
    "category": "system"
  }'
```

#### 步骤4：关联服务器

将监控项与目标服务器关联：

```bash
curl -X POST http://your-server/monitor/items/1/ips \
  -H "Content-Type: application/json" \
  -d '{
    "ip_address": "*************"
  }'
```

### 2. 监控数据查看

创建监控项后，系统会自动开始收集数据。您可以通过以下方式查看：

```bash
# 获取最近的监控数据
curl "http://your-server/monitor/data?item_id=1&limit=10"

# 获取特定时间范围的数据
curl "http://your-server/monitor/data?item_id=1&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z"
```

## 高级功能

### 1. 批量操作

#### 批量启用/禁用监控项

```bash
curl -X POST http://your-server/monitor/batch \
  -H "Content-Type: application/json" \
  -d '{
    "item_ids": [1, 2, 3],
    "operation": "enable"
  }'
```

#### 批量删除监控项

```bash
curl -X POST http://your-server/monitor/batch \
  -H "Content-Type: application/json" \
  -d '{
    "item_ids": [1, 2, 3],
    "operation": "delete"
  }'
```

### 2. 立即执行监控

如果需要立即获取监控数据，而不等待定时调度：

```bash
curl -X POST http://your-server/monitor/execute \
  -H "Content-Type: application/json" \
  -d '{
    "monitor_item_id": 1,
    "server_ips": ["*************", "*************"]
  }'
```

### 3. 监控状态查询

#### 查看整体监控状态

```bash
curl "http://your-server/monitor/status"
```

#### 查看特定服务器状态

```bash
curl "http://your-server/monitor/status/server/*************"
```

#### 查看监控统计信息

```bash
curl "http://your-server/monitor/stats"
```

## 最佳实践

### 1. 命令安全性

#### 使用安全的命令

✅ **推荐的安全命令：**
```bash
# 系统信息查询
cat /proc/cpuinfo
cat /proc/meminfo
cat /proc/loadavg
uptime
whoami
hostname

# 资源使用情况
free -m
df -h
ps aux
netstat -an

# 硬件信息
lscpu
lsblk
nvidia-smi
```

❌ **避免使用的危险命令：**
```bash
# 文件操作
rm -rf /path
mv /important/file /tmp
chmod 777 /etc/passwd

# 系统控制
shutdown -h now
reboot
systemctl stop service

# 网络操作
iptables -F
wget http://malicious-site.com/script
```

#### 命令验证

在创建监控项前，使用验证接口检查命令安全性：

```bash
curl -X POST http://your-server/monitor/validate \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试监控项",
    "command": "cat /proc/cpuinfo",
    "data_type": "string"
  }'
```

### 2. 数据类型选择

根据命令输出选择合适的数据类型：

#### string（字符串）
适用于大多数命令输出：
```bash
# 示例命令
"command": "uname -a"
"data_type": "string"
```

#### number（数字）
适用于返回数值的命令：
```bash
# 示例命令：获取进程数量
"command": "ps aux | wc -l"
"data_type": "number"
```

#### json（JSON）
适用于返回结构化数据的命令：
```bash
# 示例命令：使用jq格式化输出
"command": "free -b | awk 'NR==2{printf \"{\\\"total\\\":%s,\\\"used\\\":%s,\\\"free\\\":%s}\", $2,$3,$4}'"
"data_type": "json"
```

#### boolean（布尔）
适用于检查状态的命令：
```bash
# 示例命令：检查服务是否运行
"command": "systemctl is-active nginx >/dev/null && echo 'true' || echo 'false'"
"data_type": "boolean"
```

### 3. 超时和重试设置

#### 超时时间建议

- **快速命令**（如 `whoami`, `date`）：5-10秒
- **常规命令**（如 `ps aux`, `free -m`）：15-30秒
- **复杂命令**（如 `find`, 复杂的统计）：60-120秒

#### 重试次数建议

- **网络相关命令**：3次重试
- **本地命令**：1-2次重试
- **关键监控项**：3次重试

### 4. 分类管理

使用分类来组织监控项：

```bash
# 系统监控
"category": "system"

# 应用监控
"category": "application"

# 网络监控
"category": "network"

# 自定义监控
"category": "custom"
```

### 5. 监控频率优化

#### 调整监控间隔

根据监控项的重要性和资源消耗调整执行频率：

- **关键系统指标**：每5分钟
- **应用状态检查**：每10分钟
- **详细统计信息**：每30分钟
- **历史数据收集**：每小时

## 故障排查

### 1. 常见问题

#### 问题1：命令执行失败

**症状：** 监控项状态显示为错误，错误信息为"命令执行失败"

**解决方案：**
1. 检查命令语法是否正确
2. 确认目标服务器上是否有相应的命令
3. 验证用户权限是否足够
4. 检查命令路径是否正确

```bash
# 测试命令
curl -X POST http://your-server/monitor/test \
  -H "Content-Type: application/json" \
  -d '{
    "command": "your-command",
    "ip": "target-ip",
    "username": "username",
    "password": "password"
  }'
```

#### 问题2：SSH连接失败

**症状：** 错误信息为"SSH连接失败"

**解决方案：**
1. 检查目标服务器是否在线
2. 验证SSH服务是否运行
3. 确认用户名和密码是否正确
4. 检查防火墙设置
5. 验证SSH密钥配置（如果使用密钥认证）

#### 问题3：数据解析失败

**症状：** 错误信息为"数据解析失败"

**解决方案：**
1. 检查命令输出格式是否符合预期
2. 验证数据类型设置是否正确
3. 确认输出中没有额外的错误信息

### 2. 日志查看

#### 查看监控执行日志

```bash
# 查看最近的错误日志
curl "http://your-server/monitor/stats" | jq '.error_stats'

# 查看特定服务器的问题
curl "http://your-server/monitor/status/server/*************" | jq '.problematic_items'
```

#### 查看系统日志

```bash
# 查看应用日志
tail -f /var/log/monitoring/custom_monitor.log

# 查看错误日志
grep "ERROR" /var/log/monitoring/custom_monitor.log | tail -20
```

### 3. 性能优化

#### 监控性能指标

```bash
# 查看执行统计
curl "http://your-server/monitor/stats" | jq '.scheduler_stats'

# 查看连接池状态
curl "http://your-server/monitor/stats" | jq '.scheduler_stats.connection_pool_stats'
```

#### 优化建议

1. **减少并发执行数量**：如果服务器性能有限
2. **增加超时时间**：对于执行时间较长的命令
3. **优化命令**：使用更高效的命令替代
4. **分批执行**：将大量监控项分批执行

## 安全配置

### 1. 访问控制

#### API密钥管理

```bash
# 生成新的API密钥
curl -X POST http://your-server/auth/api-key \
  -H "Authorization: Bearer your-token"

# 使用API密钥访问
curl -H "X-API-Key: your-api-key" \
  "http://your-server/monitor/items"
```

#### 权限控制

确保用户具有适当的权限：
- `monitor:create` - 创建监控项
- `monitor:read` - 查看监控项和数据
- `monitor:update` - 更新监控项
- `monitor:delete` - 删除监控项
- `monitor:execute` - 执行监控项

### 2. 网络安全

#### SSH密钥认证

推荐使用SSH密钥而不是密码：

```json
{
  "username": "monitor-user",
  "use_ssh_key": true,
  "ssh_key_path": "/path/to/private/key"
}
```

#### 防火墙配置

确保监控服务器能够访问目标服务器的SSH端口（通常是22）。

## 扩展功能

### 1. 自定义数据处理

对于复杂的数据处理需求，可以创建自定义脚本：

```bash
# 创建数据处理脚本
cat > /usr/local/bin/custom_monitor.sh << 'EOF'
#!/bin/bash
# 获取CPU使用率百分比
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'
EOF

chmod +x /usr/local/bin/custom_monitor.sh

# 在监控项中使用
"command": "/usr/local/bin/custom_monitor.sh"
```

### 2. 告警集成

监控系统会自动检测异常情况并记录。您可以通过状态API获取告警信息：

```bash
# 获取有问题的服务器
curl "http://your-server/monitor/status" | jq '.problematic_servers'

# 获取特定监控项的统计
curl "http://your-server/monitor/items/1/statistics?hours=24"
```

### 3. 数据导出

```bash
# 导出监控数据
curl "http://your-server/monitor/data?item_id=1&limit=1000" > monitor_data.json

# 导出特定时间范围的数据
curl "http://your-server/monitor/data?start_time=2024-01-01T00:00:00Z&end_time=2024-01-02T00:00:00Z" > daily_data.json
```

## 总结

自定义监控项功能为您提供了灵活而强大的监控能力。通过遵循本指南中的最佳实践，您可以：

1. 安全地创建和管理监控项
2. 有效地收集和分析监控数据
3. 快速识别和解决问题
4. 优化监控性能和资源使用

如果您在使用过程中遇到问题，请参考故障排查部分或联系技术支持。
