from docx import Document
from docx.shared import Inches, Pt, RGBColor, Cm
from docx.enum.table import WD_TABLE_ALIGNMENT, WD_ALIGN_VERTICAL
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from datetime import datetime
import os

def set_cell_border(cell, **kwargs):
    """设置单元格边框"""
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()
    
    for edge in ('top', 'left', 'bottom', 'right'):
        edge_data = kwargs.get(edge, kwargs.get('all'))
        if edge_data:
            tag = 'w:{}'.format(edge)
            element = tcPr.find(qn(tag))
            if element is None:
                element = OxmlElement(tag)
                tcPr.append(element)
            element.set(qn('w:val'), 'single')
            element.set(qn('w:sz'), str(edge_data.get('sz', 4)))
            element.set(qn('w:color'), edge_data.get('color', '000000'))

def set_cell_background(cell, color):
    """设置单元格背景色"""
    shading_elm = OxmlElement('w:shd')
    shading_elm.set(qn('w:fill'), color)
    cell._tc.get_or_add_tcPr().append(shading_elm)

def add_checkbox(paragraph, text):
    """添加可编辑的复选框（使用方框符号）"""
    # 添加方框符号和文本
    checkbox_run = paragraph.add_run('□ ')  # 使用方框符号
    checkbox_run.font.size = Pt(14)  # 增大复选框大小
    checkbox_run.font.name = 'Calibri'  # 确保字体支持该符号
    
    # 添加文本
    text_run = paragraph.add_run(text + '    ')
    text_run.font.size = Pt(11)
    
    return paragraph

def create_fault_report():
    """创建单个故障报告模板"""
    doc = Document()
    
    # 设置页边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(2)
        section.bottom_margin = Cm(2)
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
    
    # 标题
    title = doc.add_heading('IT基础设施故障报告单', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 报告编号和日期
    header_table = doc.add_table(rows=1, cols=2)
    header_table.style = 'Table Grid'
    header_cells = header_table.rows[0].cells
    header_cells[0].text = '报告编号：FR-'
    header_cells[1].text = f'填报日期：{datetime.now().strftime("%Y年%m月%d日")}'
    for cell in header_cells:
        cell.paragraphs[0].runs[0].font.size = Pt(11)
    
    doc.add_paragraph()  # 空行
    
    # 基本信息表格
    basic_info = doc.add_heading('一、基本信息', level=2)
    basic_table = doc.add_table(rows=4, cols=4)
    basic_table.style = 'Table Grid'
    
    # 填充基本信息
    basic_data = [
        ['故障类型', '', '严重程度', ''],
        ['报告人', '', '联系方式', ''],
        ['所属部门', '', '影响范围', ''],
        ['发生时间', '', '恢复时间', '']
    ]
    
    for i, row_data in enumerate(basic_data):
        cells = basic_table.rows[i].cells
        for j, data in enumerate(row_data):
            cells[j].text = data
            if j % 2 == 0:  # 标签列
                cells[j].paragraphs[0].runs[0].font.bold = True
                set_cell_background(cells[j], 'E8F0FE')
            cells[j].width = Inches(1.8)
    
    # 故障类型选择
    doc.add_paragraph()
    type_para = doc.add_paragraph('故障类型选择（请在方框内打√）：')
    type_para.runs[0].font.bold = True
    
    type_table = doc.add_table(rows=2, cols=3)
    fault_types = ['GPU', '网络', '存储', '管理服务器', 'VPN', '带宽']
    for i, fault_type in enumerate(fault_types):
        row = i // 3
        col = i % 3
        cell = type_table.rows[row].cells[col]
        add_checkbox(cell.paragraphs[0], fault_type)
    
    # 严重程度
    doc.add_paragraph()
    severity_para = doc.add_paragraph('严重程度（请在方框内打√）：')
    severity_para.runs[0].font.bold = True
    severity_options = doc.add_paragraph()
    add_checkbox(severity_options, '紧急（系统完全瘫痪）')
    add_checkbox(severity_options, '高（严重影响业务）')
    add_checkbox(severity_options, '中（部分功能受限）')
    add_checkbox(severity_options, '低（轻微影响）')
    
    # 故障描述
    doc.add_paragraph()
    desc_heading = doc.add_heading('二、故障描述', level=2)
    
    # 故障现象
    phenomenon_para = doc.add_paragraph('1. 故障现象：')
    phenomenon_para.runs[0].font.bold = True
    phenomenon_para.runs[0].font.size = Pt(12)
    
    # 添加描述框
    desc_table = doc.add_table(rows=1, cols=1)
    desc_table.style = 'Table Grid'
    desc_cell = desc_table.rows[0].cells[0]
    desc_cell.height = Inches(2.5)
    desc_para = desc_cell.add_paragraph()
    desc_para.add_run('请详细描述故障现象，包括但不限于：\n'
                      '• 故障发生的具体时间和频率\n'
                      '• 故障的具体表现和错误信息\n'
                      '• 受影响的系统或服务\n'
                      '• 影响的用户数量和范围').font.color.rgb = RGBColor(128, 128, 128)
    
    # 故障发现过程
    doc.add_paragraph()
    discovery_para = doc.add_paragraph('2. 故障发现过程：')
    discovery_para.runs[0].font.bold = True
    discovery_para.runs[0].font.size = Pt(12)
    
    discovery_table = doc.add_table(rows=1, cols=1)
    discovery_table.style = 'Table Grid'
    discovery_cell = discovery_table.rows[0].cells[0]
    discovery_cell.height = Inches(1.5)
    
    # 初步分析
    doc.add_paragraph()
    analysis_para = doc.add_paragraph('3. 初步分析：')
    analysis_para.runs[0].font.bold = True
    analysis_para.runs[0].font.size = Pt(12)
    
    analysis_table = doc.add_table(rows=1, cols=1)
    analysis_table.style = 'Table Grid'
    analysis_cell = analysis_table.rows[0].cells[0]
    analysis_cell.height = Inches(1.5)
    
    # 解决方案
    doc.add_page_break()  # 新页
    solution_heading = doc.add_heading('三、解决方案', level=2)
    
    # 应急处理
    emergency_para = doc.add_paragraph('1. 应急处理措施：')
    emergency_para.runs[0].font.bold = True
    emergency_para.runs[0].font.size = Pt(12)
    
    emergency_table = doc.add_table(rows=1, cols=1)
    emergency_table.style = 'Table Grid'
    emergency_cell = emergency_table.rows[0].cells[0]
    emergency_cell.height = Inches(2)
    
    # 根本解决方案
    doc.add_paragraph()
    root_para = doc.add_paragraph('2. 根本解决方案：')
    root_para.runs[0].font.bold = True
    root_para.runs[0].font.size = Pt(12)
    
    root_table = doc.add_table(rows=1, cols=1)
    root_table.style = 'Table Grid'
    root_cell = root_table.rows[0].cells[0]
    root_cell.height = Inches(2.5)
    root_para = root_cell.add_paragraph()
    root_para.add_run('请详细描述解决步骤：\n'
                      '• 具体的操作步骤\n'
                      '• 使用的工具和命令\n'
                      '• 配置的修改内容\n'
                      '• 验证解决的方法').font.color.rgb = RGBColor(128, 128, 128)
    
    # 预防措施
    doc.add_paragraph()
    prevent_para = doc.add_paragraph('3. 预防措施建议：')
    prevent_para.runs[0].font.bold = True
    prevent_para.runs[0].font.size = Pt(12)
    
    prevent_table = doc.add_table(rows=1, cols=1)
    prevent_table.style = 'Table Grid'
    prevent_cell = prevent_table.rows[0].cells[0]
    prevent_cell.height = Inches(1.5)
    
    # 处理时间线
    doc.add_paragraph()
    timeline_heading = doc.add_heading('四、处理时间线', level=2)
    
    timeline_table = doc.add_table(rows=5, cols=3)
    timeline_table.style = 'Table Grid'
    
    timeline_data = [
        ['时间节点', '具体时间', '备注'],
        ['故障发生时间', '', ''],
        ['故障发现时间', '', ''],
        ['开始处理时间', '', ''],
        ['故障恢复时间', '', '']
    ]
    
    for i, row_data in enumerate(timeline_data):
        cells = timeline_table.rows[i].cells
        for j, data in enumerate(row_data):
            cells[j].text = data
            if i == 0:  # 表头
                cells[j].paragraphs[0].runs[0].font.bold = True
                set_cell_background(cells[j], '4472C4')
                cells[j].paragraphs[0].runs[0].font.color.rgb = RGBColor(255, 255, 255)
            elif j == 0:  # 第一列
                cells[j].paragraphs[0].runs[0].font.bold = True
                set_cell_background(cells[j], 'E8F0FE')
    
    # 影响评估
    doc.add_paragraph()
    impact_heading = doc.add_heading('五、影响评估', level=2)
    
    impact_table = doc.add_table(rows=4, cols=2)
    impact_table.style = 'Table Grid'
    
    impact_data = [
        ['影响系统/服务', ''],
        ['影响用户数', ''],
        ['业务损失评估', ''],
        ['数据丢失情况', '']
    ]
    
    for i, row_data in enumerate(impact_data):
        cells = impact_table.rows[i].cells
        cells[0].text = row_data[0]
        cells[0].paragraphs[0].runs[0].font.bold = True
        set_cell_background(cells[0], 'E8F0FE')
        cells[0].width = Inches(2)
        cells[1].width = Inches(4.5)
    
    # 经验总结
    doc.add_paragraph()
    summary_heading = doc.add_heading('六、经验总结', level=2)
    
    summary_table = doc.add_table(rows=1, cols=1)
    summary_table.style = 'Table Grid'
    summary_cell = summary_table.rows[0].cells[0]
    summary_cell.height = Inches(2)
    
    # 附件清单
    doc.add_paragraph()
    attachment_heading = doc.add_heading('七、附件清单', level=2)
    
    attachment_para = doc.add_paragraph('请在方框内打√：')
    attachment_para.runs[0].font.bold = True
    doc.add_paragraph()
    
    attachment_list = doc.add_paragraph()
    add_checkbox(attachment_list, '故障截图')
    add_checkbox(attachment_list, '日志文件')
    add_checkbox(attachment_list, '配置文件')
    add_checkbox(attachment_list, '其他：_______________')
    
    # 签字确认
    doc.add_paragraph()
    doc.add_paragraph()
    
    sign_table = doc.add_table(rows=2, cols=3)
    sign_table.style = 'Table Grid'
    
    sign_data = [
        ['处理人员', '审核人员', '部门主管'],
        ['签字：\n\n日期：', '签字：\n\n日期：', '签字：\n\n日期：']
    ]
    
    for i, row_data in enumerate(sign_data):
        cells = sign_table.rows[i].cells
        for j, data in enumerate(row_data):
            cells[j].text = data
            cells[j].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            if i == 0:
                cells[j].paragraphs[0].runs[0].font.bold = True
                set_cell_background(cells[j], 'E8F0FE')
            else:
                cells[j].height = Inches(1)
    
    # 保存文档
    doc.save('故障报告单_模板.docx')
    print("故障报告单模板已生成：故障报告单_模板.docx")

def create_fault_summary():
    """创建故障汇总表"""
    doc = Document()
    
    # 标题
    title = doc.add_heading('IT基础设施故障汇总表', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 统计信息
    stat_para = doc.add_paragraph()
    stat_para.add_run(f'统计周期：{datetime.now().strftime("%Y年%m月")}').font.size = Pt(12)
    
    # 创建汇总表
    summary_table = doc.add_table(rows=1, cols=8)
    summary_table.style = 'Table Grid'
    
    headers = ['编号', '故障类型', '发生日期', '严重程度', '影响时长', '简要描述', '处理状态', '详细报告']
    header_cells = summary_table.rows[0].cells
    
    for i, header in enumerate(headers):
        cell = header_cells[i]
        cell.text = header
        paragraph = cell.paragraphs[0]
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = paragraph.runs[0]
        run.font.bold = True
        run.font.size = Pt(11)
        run.font.color.rgb = RGBColor(255, 255, 255)
        set_cell_background(cell, '4472C4')
    
    # 设置列宽
    widths = [0.8, 1.2, 1.2, 1.0, 1.0, 2.5, 1.0, 1.0]
    for i, width in enumerate(widths):
        for cell in summary_table.columns[i].cells:
            cell.width = Inches(width)
    
    # 添加示例数据行
    for i in range(10):
        row_cells = summary_table.add_row().cells
        row_cells[0].text = f'FR-2024{str(i+1).zfill(3)}'
        row_cells[0].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 设置行高
        for cell in row_cells:
            cell.height = Inches(0.5)
    
    # 统计图表区域
    doc.add_paragraph()
    doc.add_heading('故障统计分析', level=2)
    
    stat_table = doc.add_table(rows=3, cols=2)
    stat_table.style = 'Table Grid'
    
    stat_data = [
        ['本月故障总数：', ''],
        ['平均修复时间：', ''],
        ['重大故障次数：', '']
    ]
    
    for i, row_data in enumerate(stat_data):
        cells = stat_table.rows[i].cells
        cells[0].text = row_data[0]
        cells[0].paragraphs[0].runs[0].font.bold = True
        set_cell_background(cells[0], 'E8F0FE')
    
    doc.save('故障汇总表.docx')
    print("故障汇总表已生成：故障汇总表.docx")

def create_monthly_analysis():
    """创建月度分析报告模板"""
    doc = Document()
    
    # 标题
    title = doc.add_heading('IT基础设施故障月度分析报告', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 报告信息
    month = datetime.now().strftime("%Y年%m月")
    info_para = doc.add_paragraph()
    info_para.add_run(f'报告周期：{month}').font.size = Pt(12)
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 概述
    doc.add_heading('一、月度故障概述', level=2)
    overview_table = doc.add_table(rows=1, cols=1)
    overview_table.style = 'Table Grid'
    overview_cell = overview_table.rows[0].cells[0]
    overview_cell.height = Inches(1.5)
    
    # 故障分类统计
    doc.add_heading('二、故障分类统计', level=2)
    
    category_table = doc.add_table(rows=7, cols=5)
    category_table.style = 'Table Grid'
    
    category_headers = ['故障类型', '发生次数', '占比', '平均修复时间', '影响程度']
    category_data = ['GPU', '网络', '存储', '管理服务器', 'VPN', '带宽']
    
    # 表头
    header_cells = category_table.rows[0].cells
    for i, header in enumerate(category_headers):
        header_cells[i].text = header
        header_cells[i].paragraphs[0].runs[0].font.bold = True
        set_cell_background(header_cells[i], '4472C4')
        header_cells[i].paragraphs[0].runs[0].font.color.rgb = RGBColor(255, 255, 255)
        header_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 数据行
    for i, fault_type in enumerate(category_data, 1):
        cells = category_table.rows[i].cells
        cells[0].text = fault_type
        cells[0].paragraphs[0].runs[0].font.bold = True
        set_cell_background(cells[0], 'E8F0FE')
        
        for j in range(1, 5):
            cells[j].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 趋势分析
    doc.add_heading('三、故障趋势分析', level=2)
    trend_para = doc.add_paragraph('1. 环比分析：')
    trend_para.runs[0].font.bold = True
    
    trend_table = doc.add_table(rows=1, cols=1)
    trend_table.style = 'Table Grid'
    trend_cell = trend_table.rows[0].cells[0]
    trend_cell.height = Inches(1.5)
    
    doc.add_paragraph()
    peak_para = doc.add_paragraph('2. 故障高峰时段分析：')
    peak_para.runs[0].font.bold = True
    
    peak_table = doc.add_table(rows=1, cols=1)
    peak_table.style = 'Table Grid'
    peak_cell = peak_table.rows[0].cells[0]
    peak_cell.height = Inches(1.5)
    
    # 重大故障分析
    doc.add_heading('四、重大故障分析', level=2)
    major_table = doc.add_table(rows=1, cols=1)
    major_table.style = 'Table Grid'
    major_cell = major_table.rows[0].cells[0]
    major_cell.height = Inches(2)
    
    # 改进建议
    doc.add_heading('五、改进建议', level=2)
    
    suggestions = [
        '1. 预防性维护建议：',
        '2. 系统优化建议：',
        '3. 流程改进建议：',
        '4. 资源配置建议：'
    ]
    
    for suggestion in suggestions:
        sug_para = doc.add_paragraph(suggestion)
        sug_para.runs[0].font.bold = True
        
        sug_table = doc.add_table(rows=1, cols=1)
        sug_table.style = 'Table Grid'
        sug_cell = sug_table.rows[0].cells[0]
        sug_cell.height = Inches(1)
        doc.add_paragraph()
    
    # 下月重点关注
    doc.add_heading('六、下月重点关注事项', level=2)
    focus_table = doc.add_table(rows=1, cols=1)
    focus_table.style = 'Table Grid'
    focus_cell = focus_table.rows[0].cells[0]
    focus_cell.height = Inches(1.5)
    
    doc.save('月度分析报告.docx')
    print("月度分析报告已生成：月度分析报告.docx")

def create_all_documents():
    """生成所有文档"""
    print("开始生成故障管理文档套件...")
    print("-" * 50)
    
    # 创建输出目录
    output_dir = "故障管理文档"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 切换到输出目录
    original_dir = os.getcwd()
    os.chdir(output_dir)
    
    try:
        # 生成各种文档
        create_fault_report()
        create_fault_summary()
        create_monthly_analysis()
        
        print("\n" + "="*50)
        print("所有文档已生成完成！")
        print(f"文档保存在：{os.path.abspath('.')}")
        print("\n文档说明：")
        print("1. 故障报告单_模板.docx - 单个故障的详细记录模板")
        print("2. 故障汇总表.docx - 故障列表汇总")
        print("3. 月度分析报告.docx - 月度故障分析报告模板")
        print("\n使用提示：")
        print("- 在方框 □ 内直接输入 √ 或 × 来标记选项")
        print("- 也可以删除 □ 并输入 ☑ 或 ☒")
        print("="*50)
        
    finally:
        # 切换回原目录
        os.chdir(original_dir)

if __name__ == "__main__":
    # 检查并安装依赖
    try:
        import docx
    except ImportError:
        print("正在安装python-docx库...")
        os.system("pip install python-docx")
        import docx
    
    create_all_documents()