import Scene from './scene.js';
import Button from '../ui/button.js';
import { GAME_MODES, AI_DIFFICULTY, ASSETS } from '../config/gameConfig.js';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';
import Sprite from '../base/sprite.js';

/**
 * 大厅场景类
 * 实现游戏模式选择、设置和个人中心
 */
export default class HallScene extends Scene {
  // 当前视图
  currentView = 'main'; // main, settings, stats
  // 游戏标题
  title = '飞行棋';
  // 背景图片
  background = null;
  // 开始游戏回调
  onStartGame = null;
  // 按钮列表
  buttons = {
    main: [],
    settings: [],
    stats: []
  };
  // 设置选项
  settings = {
    playerCount: 4,
    aiDifficulty: AI_DIFFICULTY.MEDIUM,
    soundEnabled: true,
    vibrationEnabled: true
  };

  /**
   * 构造函数
   * @param {Object} options - 场景选项
   */
  constructor(options = {}) {
    super('hall');
    
    this.onStartGame = options.onStartGame || null;
    
    // 加载背景图片
    try {
      this.background = new Sprite(
        ASSETS.boardBackground,
        SCREEN_WIDTH,
        SCREEN_HEIGHT,
        0,
        0
      );
      
      // 设置背景颜色为浅蓝色
      this.background.setColor('#E6F3FF');
    } catch (e) {
      console.log('无法加载背景图片，将使用默认背景', e);
      this.background = new Sprite(
        null,
        SCREEN_WIDTH,
        SCREEN_HEIGHT,
        0,
        0
      );
      
      // 设置背景颜色为浅蓝色
      this.background.setColor('#E6F3FF');
    }
  }
  
  /**
   * 初始化场景
   */
  init() {
    super.init();
    
    // 添加背景
    this.addElement(this.background);
    
    // 创建主菜单按钮
    this.createMainButtons();
    
    // 创建设置菜单按钮
    this.createSettingsButtons();
    
    // 创建统计菜单按钮
    this.createStatsButtons();
    
    // 默认显示主菜单
    this.showView('main');
    
    // 从数据总线加载设置
    this.loadSettings();
  }
  
  /**
   * 创建主菜单按钮
   */
  createMainButtons() {
    const buttonWidth = 200;
    const buttonHeight = 60;
    const buttonSpacing = 20;
    const startY = SCREEN_HEIGHT / 2 - 100;
    
    // 单人游戏按钮
    const singlePlayerButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY,
      width: buttonWidth,
      height: buttonHeight,
      text: '单人游戏',
      style: {
        backgroundColor: '#FF6B6B',
        hoverColor: '#FF5252',
        pressedColor: '#E53935',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#ffffff',
        shadowColor: 'rgba(255, 107, 107, 0.4)',
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowOffsetY: 8,
        gradient: true,
        glowEffect: true
      },
      onClick: () => {
        this.startGame(GAME_MODES.SINGLE_PLAYER);
      }
    });
    this.buttons.main.push(singlePlayerButton);
    this.addElement(singlePlayerButton);
    
    // 本地多人按钮
    const localMultiplayerButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + buttonHeight + buttonSpacing,
      width: buttonWidth,
      height: buttonHeight,
      text: '本地多人',
      style: {
        backgroundColor: '#4ECDC4',
        hoverColor: '#26C6DA',
        pressedColor: '#00ACC1',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#ffffff',
        shadowColor: 'rgba(78, 205, 196, 0.4)',
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowOffsetY: 8,
        gradient: true,
        glowEffect: true
      },
      onClick: () => {
        this.startGame(GAME_MODES.LOCAL_MULTIPLAYER);
      }
    });
    this.buttons.main.push(localMultiplayerButton);
    this.addElement(localMultiplayerButton);
    
    // 在线对战按钮 (暂不可用)
    const onlineMultiplayerButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + 2 * (buttonHeight + buttonSpacing),
      width: buttonWidth,
      height: buttonHeight,
      text: '在线对战',
      style: {
        backgroundColor: '#95A5A6',
        hoverColor: '#7F8C8D',
        pressedColor: '#6C7B7D',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#BDC3C7',
        shadowColor: 'rgba(149, 165, 166, 0.3)',
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowOffsetY: 5,
        gradient: false,
        glowEffect: false
      },
      onClick: () => {
        this.startGame(GAME_MODES.ONLINE_MULTIPLAYER);
      }
    });
    onlineMultiplayerButton.setEnabled(false); // 暂时禁用
    this.buttons.main.push(onlineMultiplayerButton);
    this.addElement(onlineMultiplayerButton);
    
    // 设置按钮
    const settingsButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + 3 * (buttonHeight + buttonSpacing),
      width: buttonWidth,
      height: buttonHeight,
      text: '设置',
      style: {
        backgroundColor: '#9B59B6',
        hoverColor: '#8E44AD',
        pressedColor: '#7D3C98',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#ffffff',
        shadowColor: 'rgba(155, 89, 182, 0.4)',
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowOffsetY: 8,
        gradient: true,
        glowEffect: true
      },
      onClick: () => {
        this.showView('settings');
      }
    });
    this.buttons.main.push(settingsButton);
    this.addElement(settingsButton);
    
    // 游戏统计按钮
    const statsButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + 4 * (buttonHeight + buttonSpacing),
      width: buttonWidth,
      height: buttonHeight,
      text: '游戏统计',
      style: {
        backgroundColor: '#F39C12',
        hoverColor: '#E67E22',
        pressedColor: '#D35400',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#ffffff',
        shadowColor: 'rgba(243, 156, 18, 0.4)',
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowOffsetY: 8,
        gradient: true,
        glowEffect: true
      },
      onClick: () => {
        this.showView('stats');
      }
    });
    this.buttons.main.push(statsButton);
    this.addElement(statsButton);
  }
  
  /**
   * 创建设置菜单按钮
   */
  createSettingsButtons() {
    const buttonWidth = 200;
    const buttonHeight = 60;
    const buttonSpacing = 20;
    const startY = SCREEN_HEIGHT / 2 - 150;
    
    // 返回按钮
    const backButton = new Button({
      x: 20,
      y: 20,
      width: 80,
      height: 40,
      text: '返回',
      style: {
        backgroundColor: '#95A5A6',
        hoverColor: '#7F8C8D',
        pressedColor: '#6C7B7D',
        textColor: '#FFFFFF',
        fontSize: 16,
        fontWeight: 'bold',
        borderRadius: 15,
        borderWidth: 2,
        borderColor: '#BDC3C7',
        shadowColor: 'rgba(149, 165, 166, 0.3)',
        shadowBlur: 8,
        shadowOffsetX: 0,
        shadowOffsetY: 4,
        gradient: true,
        glowEffect: false
      },
      onClick: () => {
        this.showView('main');
      }
    });
    this.buttons.settings.push(backButton);
    this.addElement(backButton);
    
    // 玩家人数按钮
    const playerCountButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY,
      width: buttonWidth,
      height: buttonHeight,
      text: `玩家人数: ${this.settings.playerCount}`,
      onClick: () => {
        // 循环切换玩家人数 (2-4)
        this.settings.playerCount = this.settings.playerCount % 3 + 2;
        playerCountButton.setText(`玩家人数: ${this.settings.playerCount}`);
        this.saveSettings();
      }
    });
    this.buttons.settings.push(playerCountButton);
    this.addElement(playerCountButton);
    
    // AI难度按钮
    const aiDifficultyButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + buttonHeight + buttonSpacing,
      width: buttonWidth,
      height: buttonHeight,
      text: `AI难度: ${this.getDifficultyText(this.settings.aiDifficulty)}`,
      onClick: () => {
        // 循环切换AI难度
        switch (this.settings.aiDifficulty) {
          case AI_DIFFICULTY.EASY:
            this.settings.aiDifficulty = AI_DIFFICULTY.MEDIUM;
            break;
          case AI_DIFFICULTY.MEDIUM:
            this.settings.aiDifficulty = AI_DIFFICULTY.HARD;
            break;
          case AI_DIFFICULTY.HARD:
            this.settings.aiDifficulty = AI_DIFFICULTY.EASY;
            break;
        }
        aiDifficultyButton.setText(`AI难度: ${this.getDifficultyText(this.settings.aiDifficulty)}`);
        this.saveSettings();
      }
    });
    this.buttons.settings.push(aiDifficultyButton);
    this.addElement(aiDifficultyButton);
    
    // 音效开关按钮
    const soundButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + 2 * (buttonHeight + buttonSpacing),
      width: buttonWidth,
      height: buttonHeight,
      text: `音效: ${this.settings.soundEnabled ? '开' : '关'}`,
      onClick: () => {
        this.settings.soundEnabled = !this.settings.soundEnabled;
        soundButton.setText(`音效: ${this.settings.soundEnabled ? '开' : '关'}`);
        this.saveSettings();
      }
    });
    this.buttons.settings.push(soundButton);
    this.addElement(soundButton);
    
    // 震动开关按钮
    const vibrationButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + 3 * (buttonHeight + buttonSpacing),
      width: buttonWidth,
      height: buttonHeight,
      text: `震动: ${this.settings.vibrationEnabled ? '开' : '关'}`,
      onClick: () => {
        this.settings.vibrationEnabled = !this.settings.vibrationEnabled;
        vibrationButton.setText(`震动: ${this.settings.vibrationEnabled ? '开' : '关'}`);
        this.saveSettings();
      }
    });
    this.buttons.settings.push(vibrationButton);
    this.addElement(vibrationButton);
    
    // 保存按钮
    const saveButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY + 4 * (buttonHeight + buttonSpacing),
      width: buttonWidth,
      height: buttonHeight,
      text: '保存设置',
      onClick: () => {
        this.saveSettings();
        this.showView('main');
      }
    });
    this.buttons.settings.push(saveButton);
    this.addElement(saveButton);
  }
  
  /**
   * 创建统计菜单按钮
   */
  createStatsButtons() {
    const buttonWidth = 200;
    const buttonHeight = 60;
    const startY = SCREEN_HEIGHT / 2 + 150;
    
    // 返回按钮
    const backButton = new Button({
      x: 20,
      y: 20,
      width: 80,
      height: 40,
      text: '返回',
      onClick: () => {
        this.showView('main');
      }
    });
    this.buttons.stats.push(backButton);
    this.addElement(backButton);
    
    // 返回主菜单按钮
    const mainMenuButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth / 2,
      y: startY,
      width: buttonWidth,
      height: buttonHeight,
      text: '返回主菜单',
      onClick: () => {
        this.showView('main');
      }
    });
    this.buttons.stats.push(mainMenuButton);
    this.addElement(mainMenuButton);
  }
  
  /**
   * 显示指定视图
   * @param {string} view - 视图名称
   */
  showView(view) {
    this.currentView = view;
    
    // 隐藏所有按钮
    Object.values(this.buttons).flat().forEach(button => {
      button.setVisible(false);
    });
    
    // 显示当前视图的按钮
    this.buttons[view].forEach(button => {
      button.setVisible(true);
    });
  }
  
  /**
   * 开始游戏
   * @param {string} gameMode - 游戏模式
   */
  startGame(gameMode) {
    if (this.onStartGame) {
      // 传递游戏模式和玩家人数
      this.onStartGame(gameMode, this.settings.playerCount);
    }
  }
  
  /**
   * 获取难度文本
   * @param {string} difficulty - 难度级别
   * @returns {string} 难度文本
   */
  getDifficultyText(difficulty) {
    switch (difficulty) {
      case AI_DIFFICULTY.EASY:
        return '简单';
      case AI_DIFFICULTY.MEDIUM:
        return '中等';
      case AI_DIFFICULTY.HARD:
        return '困难';
      default:
        return '未知';
    }
  }
  
  /**
   * 加载设置
   */
  loadSettings() {
    // 从数据总线加载设置
    this.settings.soundEnabled = GameGlobal.databus.soundEnabled;
    this.settings.vibrationEnabled = GameGlobal.databus.vibrationEnabled;
    this.settings.aiDifficulty = GameGlobal.databus.aiDifficulty;
    
    // 更新按钮文本
    this.buttons.settings.forEach(button => {
      if (button.text.startsWith('音效:')) {
        button.setText(`音效: ${this.settings.soundEnabled ? '开' : '关'}`);
      } else if (button.text.startsWith('震动:')) {
        button.setText(`震动: ${this.settings.vibrationEnabled ? '开' : '关'}`);
      } else if (button.text.startsWith('AI难度:')) {
        button.setText(`AI难度: ${this.getDifficultyText(this.settings.aiDifficulty)}`);
      } else if (button.text.startsWith('玩家人数:')) {
        button.setText(`玩家人数: ${this.settings.playerCount}`);
      }
    });
  }
  
  /**
   * 保存设置
   */
  saveSettings() {
    // 保存设置到数据总线
    GameGlobal.databus.soundEnabled = this.settings.soundEnabled;
    GameGlobal.databus.vibrationEnabled = this.settings.vibrationEnabled;
    GameGlobal.databus.aiDifficulty = this.settings.aiDifficulty;
    GameGlobal.databus.saveSettings();
  }
  
  /**
   * 渲染场景
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    // 渲染场景元素
    super.render(ctx);
    
    // 渲染标题
    this.renderTitle(ctx);
    
    // 根据当前视图渲染不同内容
    switch (this.currentView) {
      case 'main':
        this.renderMainView(ctx);
        break;
      case 'settings':
        this.renderSettingsView(ctx);
        break;
      case 'stats':
        this.renderStatsView(ctx);
        break;
    }
  }
  
  /**
   * 渲染标题
   * @param {Object} ctx - Canvas上下文
   */
  renderTitle(ctx) {
    ctx.fillStyle = '#000000';
    ctx.font = 'bold 40px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      this.title,
      SCREEN_WIDTH / 2,
      100
    );
  }
  
  /**
   * 渲染主视图
   * @param {Object} ctx - Canvas上下文
   */
  renderMainView(ctx) {
    // 渲染游戏说明
    ctx.fillStyle = '#333333';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      '选择游戏模式开始游戏',
      SCREEN_WIDTH / 2,
      150
    );
  }
  
  /**
   * 渲染设置视图
   * @param {Object} ctx - Canvas上下文
   */
  renderSettingsView(ctx) {
    ctx.fillStyle = '#333333';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      '游戏设置',
      SCREEN_WIDTH / 2,
      100
    );
  }
  
  /**
   * 渲染统计视图
   * @param {Object} ctx - Canvas上下文
   */
  renderStatsView(ctx) {
    ctx.fillStyle = '#333333';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      '游戏统计',
      SCREEN_WIDTH / 2,
      100
    );
    
    // 渲染统计数据
    const stats = GameGlobal.databus.statistics;
    const startY = 180;
    const lineHeight = 40;
    
    ctx.font = '18px Arial';
    ctx.textAlign = 'left';
    
    // 总局数
    ctx.fillText(
      `总局数: ${stats.totalGames}`,
      SCREEN_WIDTH / 2 - 100,
      startY
    );
    
    // 胜利局数
    ctx.fillText(
      `胜利局数: ${stats.wins}`,
      SCREEN_WIDTH / 2 - 100,
      startY + lineHeight
    );
    
    // 胜率
    const winRate = stats.totalGames > 0 ? (stats.wins / stats.totalGames * 100).toFixed(1) : '0.0';
    ctx.fillText(
      `胜率: ${winRate}%`,
      SCREEN_WIDTH / 2 - 100,
      startY + 2 * lineHeight
    );
    
    // 最快获胜时间
    const fastestWin = stats.fastestWin ? this.formatTime(stats.fastestWin) : '无记录';
    ctx.fillText(
      `最快获胜: ${fastestWin}`,
      SCREEN_WIDTH / 2 - 100,
      startY + 3 * lineHeight
    );
    
    // 最长游戏时间
    const longestGame = stats.longestGame ? this.formatTime(stats.longestGame) : '无记录';
    ctx.fillText(
      `最长游戏: ${longestGame}`,
      SCREEN_WIDTH / 2 - 100,
      startY + 4 * lineHeight
    );
  }
  
  /**
   * 格式化时间
   * @param {number} ms - 毫秒数
   * @returns {string} 格式化后的时间
   */
  formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  }
}