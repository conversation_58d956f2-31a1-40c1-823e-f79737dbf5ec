from tortoise import fields
from datetime import datetime
import pytz
from models.base_model import BaseModel, SHANGHAI_TZ, get_shanghai_now
from config.timezone import add_tz

class CPUStats(BaseModel):
    """服务器CPU监控数据"""
    
    ip = fields.CharField(max_length=50, description="服务器IP地址")
    model = fields.CharField(max_length=255, description="CPU型号", null=True)
    cores = fields.IntField(description="CPU核心数", null=True)
    frequency = fields.FloatField(description="CPU频率(MHz)", null=True)
    usage_percent = fields.FloatField(description="CPU总使用率(%)")
    user_percent = fields.FloatField(description="用户空间使用率(%)", null=True)
    system_percent = fields.FloatField(description="系统空间使用率(%)", null=True)
    iowait_percent = fields.FloatField(description="IO等待率(%)", null=True)
    load_1min = fields.FloatField(description="1分钟负载", null=True)
    load_5min = fields.FloatField(description="5分钟负载", null=True)
    load_15min = fields.FloatField(description="15分钟负载", null=True)
    process_count = fields.IntField(description="进程数", null=True)
    timestamp = fields.DatetimeField(description="记录时间")
    
    class Meta:
        table = "cpu_stats"
        description = "CPU监控数据"
    
    def __str__(self):
        return f"{self.ip} - {self.timestamp} - 使用率:{self.usage_percent}%"
        
    async def save(self, *args, **kwargs):
        """
        重写保存方法，使用新的时区处理策略

        修复策略：
        1. timestamp字段使用带时区信息的UTC时间
        2. 让BaseModel处理created_at和updated_at
        3. 消除RuntimeWarning警告
        """
        # 导入新的时区工具
        from config.timezone_utils import TZ

        # 如果timestamp未设置，使用当前UTC时间（带时区信息）
        if not self.timestamp:
            self.timestamp = TZ.now_utc()  # 保持UTC时区信息

        # 调用父类的save方法继续处理
        return await super().save(*args, **kwargs)

    def get_timestamp_display(self) -> str:
        """获取时间戳的显示格式（上海时区）"""
        if not self.timestamp:
            return None
        from config.timezone_utils import TZ
        # 如果timestamp已经有时区信息，直接转换；否则假设为UTC
        if self.timestamp.tzinfo is None:
            return TZ.to_display_format(self.timestamp, source_tz=TZ.UTC)
        else:
            return TZ.to_display_format(self.timestamp)


class CPUTopProcess(BaseModel):
    """CPU占用最高的进程"""
    
    cpu_stats = fields.ForeignKeyField(
        'models.CPUStats',
        related_name='top_processes',
        description="关联的CPU统计",
        on_delete=fields.CASCADE  # 当CPU统计记录被删除时，级联删除进程记录
    )
    pid = fields.CharField(max_length=20, description="进程ID")
    user = fields.CharField(max_length=50, description="进程所属用户")
    cpu_percent = fields.FloatField(description="CPU使用率(%)")
    command = fields.TextField(description="进程命令")
    
    class Meta:
        table = "cpu_top_processes"
        description = "CPU占用最高的进程"
    
    def __str__(self):
        return f"PID:{self.pid} - {self.cpu_percent}% - {self.command[:30]}" 