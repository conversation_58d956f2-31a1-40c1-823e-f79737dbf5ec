"""
时区处理测试用例

测试新的时区处理工具类和相关功能的正确性
"""

import os
import sys
import asyncio
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入时区处理工具
from config.timezone_utils import TZ, ModernJSONEncoder, DatabaseTimeUtils
from config.timezone import now, utcnow, add_tz  # 向后兼容测试

class TestTimezoneUtils:
    """测试新的时区工具类"""
    
    def test_utc_now(self):
        """测试UTC时间获取"""
        utc_time = TZ.now_utc()
        assert utc_time.tzinfo == timezone.utc
        assert isinstance(utc_time, datetime)
    
    def test_shanghai_now(self):
        """测试上海时区时间获取"""
        shanghai_time = TZ.now_shanghai()
        assert shanghai_time.tzinfo == ZoneInfo("Asia/Shanghai")
        assert isinstance(shanghai_time, datetime)
    
    def test_timezone_conversion(self):
        """测试时区转换"""
        # 创建一个UTC时间
        utc_time = datetime(2023, 12, 25, 14, 30, 0, tzinfo=timezone.utc)
        
        # 转换为上海时区
        shanghai_time = TZ.to_shanghai(utc_time)
        
        # 验证转换结果
        assert shanghai_time.hour == 22  # UTC+8
        assert shanghai_time.tzinfo == ZoneInfo("Asia/Shanghai")
    
    def test_naive_datetime_handling(self):
        """测试naive datetime的处理"""
        # 创建一个naive datetime
        naive_dt = datetime(2023, 12, 25, 14, 30, 0)
        
        # 转换为UTC（假设输入是上海时区）
        utc_dt = TZ.to_utc(naive_dt, source_tz=ZoneInfo("Asia/Shanghai"))
        
        # 验证转换结果
        assert utc_dt.hour == 6  # 上海时区14:30 -> UTC 06:30
        assert utc_dt.tzinfo == timezone.utc
    
    def test_display_format(self):
        """测试显示格式转换"""
        # 创建一个UTC时间
        utc_time = datetime(2023, 12, 25, 6, 30, 0, tzinfo=timezone.utc)
        
        # 转换为显示格式
        display_str = TZ.to_display_format(utc_time)
        
        # 验证格式
        assert "+08:00" in display_str
        assert "2023-12-25T14:30:00" in display_str
    
    def test_input_parsing(self):
        """测试输入解析"""
        # 测试ISO格式输入
        iso_str = "2023-12-25T14:30:00+08:00"
        parsed_dt = TZ.from_input(iso_str)
        
        # 验证解析结果
        assert parsed_dt.tzinfo == timezone.utc
        assert parsed_dt.hour == 6  # 上海时区14:30 -> UTC 06:30
    
    def test_safe_localize(self):
        """测试安全时区添加"""
        naive_dt = datetime(2023, 12, 25, 14, 30, 0)
        
        # 添加时区信息
        localized_dt = TZ.safe_localize(naive_dt)
        
        # 验证结果
        assert localized_dt.tzinfo == ZoneInfo("Asia/Shanghai")
        assert localized_dt.hour == 14

class TestBackwardCompatibility:
    """测试向后兼容性"""
    
    def test_old_functions_still_work(self):
        """测试旧的函数仍然可用"""
        # 测试旧的now()函数
        old_now = now()
        assert isinstance(old_now, datetime)
        
        # 测试旧的utcnow()函数
        old_utc = utcnow()
        assert isinstance(old_utc, datetime)
        
        # 测试旧的add_tz()函数
        naive_dt = datetime(2023, 12, 25, 14, 30, 0)
        with_tz = add_tz(naive_dt)
        assert with_tz.tzinfo is not None

class TestJSONEncoder:
    """测试JSON编码器"""
    
    def test_modern_json_encoder(self):
        """测试现代JSON编码器"""
        import json
        
        # 创建一个UTC时间
        utc_time = datetime(2023, 12, 25, 6, 30, 0, tzinfo=timezone.utc)
        
        # 使用编码器序列化
        json_str = json.dumps({"time": utc_time}, cls=ModernJSONEncoder)
        
        # 验证结果
        assert "+08:00" in json_str
        assert "14:30:00" in json_str

class TestDatabaseTimeUtils:
    """测试数据库时间工具"""
    
    def test_prepare_for_save(self):
        """测试保存前的时间准备"""
        # 创建一个上海时区时间
        shanghai_time = datetime(2023, 12, 25, 14, 30, 0, tzinfo=ZoneInfo("Asia/Shanghai"))
        
        # 准备保存
        prepared_time = DatabaseTimeUtils.prepare_for_save(shanghai_time)
        
        # 验证转换为UTC
        assert prepared_time.tzinfo == timezone.utc
        assert prepared_time.hour == 6  # 上海时区14:30 -> UTC 06:30
    
    def test_prepare_for_display(self):
        """测试显示前的时间准备"""
        # 创建一个UTC时间（模拟从数据库读取）
        utc_time = datetime(2023, 12, 25, 6, 30, 0, tzinfo=timezone.utc)
        
        # 准备显示
        display_str = DatabaseTimeUtils.prepare_for_display(utc_time)
        
        # 验证转换为上海时区显示格式
        assert "+08:00" in display_str
        assert "14:30:00" in display_str

class TestTimezoneBoundaryConditions:
    """测试时区边界条件"""
    
    def test_none_handling(self):
        """测试None值处理"""
        assert TZ.to_utc(None) is None
        assert TZ.to_shanghai(None) is None
        assert TZ.to_display_format(None) is None
        assert TZ.from_input(None) is None
    
    def test_already_with_timezone(self):
        """测试已有时区信息的datetime"""
        # 创建一个已有时区的datetime
        shanghai_time = datetime(2023, 12, 25, 14, 30, 0, tzinfo=ZoneInfo("Asia/Shanghai"))
        
        # 转换为UTC
        utc_time = TZ.to_utc(shanghai_time)
        
        # 验证转换正确
        assert utc_time.hour == 6
        assert utc_time.tzinfo == timezone.utc
    
    def test_invalid_input_format(self):
        """测试无效输入格式"""
        try:
            TZ.from_input("invalid-date-format")
            assert False, "应该抛出ValueError异常"
        except ValueError:
            pass  # 预期的异常

class TestTimezoneConsistency:
    """测试时区一致性"""
    
    def test_round_trip_conversion(self):
        """测试往返转换的一致性"""
        # 创建一个原始时间
        original_utc = datetime(2023, 12, 25, 6, 30, 0, tzinfo=timezone.utc)
        
        # UTC -> 上海 -> UTC
        shanghai_time = TZ.to_shanghai(original_utc)
        back_to_utc = TZ.to_utc(shanghai_time)
        
        # 验证往返转换的一致性
        assert original_utc == back_to_utc
    
    def test_display_format_consistency(self):
        """测试显示格式的一致性"""
        # 创建多个不同的时间
        times = [
            datetime(2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 6, 15, 12, 30, 45, tzinfo=timezone.utc),
            datetime(2023, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
        ]
        
        for time in times:
            display_str = TZ.to_display_format(time)
            # 验证所有显示格式都包含+08:00时区标识
            assert "+08:00" in display_str
            # 验证格式符合ISO标准
            assert "T" in display_str

def run_all_tests():
    """运行所有测试"""
    print("开始运行时区处理测试...")
    
    # 创建测试实例
    test_classes = [
        TestTimezoneUtils(),
        TestBackwardCompatibility(),
        TestJSONEncoder(),
        TestDatabaseTimeUtils(),
        TestTimezoneBoundaryConditions(),
        TestTimezoneConsistency(),
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        class_name = test_class.__class__.__name__
        print(f"\n运行 {class_name} 测试...")
        
        # 获取所有测试方法
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]
        
        for method_name in test_methods:
            total_tests += 1
            try:
                method = getattr(test_class, method_name)
                method()
                print(f"  ✅ {method_name}")
                passed_tests += 1
            except Exception as e:
                print(f"  ❌ {method_name}: {str(e)}")
    
    print(f"\n测试完成: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有时区处理测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查时区处理逻辑")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
