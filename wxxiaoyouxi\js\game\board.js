import { BOARD_CONFIG, BOARD_LAYOUT, PLAYER_COLORS, ASSETS } from '../config/gameConfig';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';
import Sprite from '../base/sprite';

/**
 * 棋盘类
 * 负责绘制棋盘和管理格子位置
 */
export default class Board {
  // 棋盘格子
  cells = [];
  // 基地区域
  baseAreas = {};
  // 安全跑道
  safeRunways = {};
  // 终点区域
  finishArea = null;
  // 缩放比例
  scale = 1;
  // 棋盘背景
  background = null;
  // 格子大小
  cellSize = BOARD_LAYOUT.cellSize;
  // 是否使用Canvas绘制
  useCanvas = false;

  constructor() {
    // 计算缩放比例
    this.calculateScale();
    
    // 加载棋盘背景
    this.loadBackground();
    
    // 初始化棋盘格子
    this.initCells();
    
    // 初始化基地区域
    this.initBaseAreas();
    
    // 初始化安全跑道
    this.initSafeRunways();
    
    // 初始化终点区域
    this.initFinishArea();
  }
  
  /**
   * 计算缩放比例
   */
  calculateScale() {
    const designWidth = 750; // 设计稿宽度
    const designHeight = 1334; // 设计稿高度
    
    const scaleX = SCREEN_WIDTH / designWidth;
    const scaleY = SCREEN_HEIGHT / designHeight;
    
    // 使用较小的缩放比例，确保棋盘完整显示
    this.scale = Math.min(scaleX, scaleY, 1);
    
    // 调整格子大小
    this.cellSize = BOARD_LAYOUT.cellSize * this.scale;
  }
  
  /**
   * 加载棋盘背景
   */
  loadBackground() {
    try {
      if (ASSETS.boardBackground && ASSETS.boardBackground.trim() !== '') {
        this.background = new Sprite(
          ASSETS.boardBackground,
          SCREEN_WIDTH,
          SCREEN_HEIGHT,
          0,
          0
        );
      } else {
        this.useCanvas = true;
      }
    } catch (error) {
      console.warn('棋盘背景加载失败，使用Canvas绘制:', error);
      this.useCanvas = true;
    }
  }
  
  /**
   * 初始化棋盘格子
   */
  initCells() {
    this.cells = [];
    
    // 计算棋盘中心点
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;
    
    // 棋盘是十字形，52个格子围成一圈
    const radius = 200 * this.scale;
    
    for (let i = 0; i < BOARD_CONFIG.totalCells; i++) {
      const angle = (i / BOARD_CONFIG.totalCells) * Math.PI * 2 - Math.PI / 2;
      
      const cell = {
        id: i,
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * radius,
        width: this.cellSize,
        height: this.cellSize,
        color: this.getCellColor(i),
        isSpecial: this.isSpecialCell(i)
      };
      
      this.cells.push(cell);
    }
  }
  
  /**
   * 获取格子颜色
   */
  getCellColor(cellIndex) {
    // 检查是否是特殊颜色格子
    for (const [color, positions] of Object.entries(BOARD_CONFIG.coloredCells)) {
      if (positions.includes(cellIndex)) {
        return color;
      }
    }
    
    return 'white'; // 默认白色
  }
  
  /**
   * 检查是否是特殊格子
   */
  isSpecialCell(cellIndex) {
    // 检查是否是飞行格子
    for (const positions of Object.values(BOARD_CONFIG.flyingCells)) {
      if (positions.includes(cellIndex)) {
        return true;
      }
    }
    
    // 检查是否是起点
    for (const startPoint of Object.values(BOARD_CONFIG.startPoints)) {
      if (startPoint === cellIndex) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 初始化基地区域
   */
  initBaseAreas() {
    this.baseAreas = {};
    
    for (const [color, config] of Object.entries(BOARD_LAYOUT.baseAreas)) {
      this.baseAreas[color] = {
        x: config.x * this.scale,
        y: config.y * this.scale,
        radius: config.radius * this.scale,
        positions: this.calculateBasePositions(color)
      };
    }
  }
  
  /**
   * 计算基地内棋子位置
   */
  calculateBasePositions(color) {
    const basePositions = BOARD_LAYOUT.basePositions[color];
    return basePositions.map(pos => ({
      x: pos.x * this.scale,
      y: pos.y * this.scale
    }));
  }
  
  /**
   * 初始化安全跑道
   */
  initSafeRunways() {
    this.safeRunways = {};
    
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;
    
    // 为每种颜色创建安全跑道
    const colors = Object.keys(PLAYER_COLORS);
    colors.forEach((colorKey, index) => {
      const color = PLAYER_COLORS[colorKey];
      const positions = [];
      
      // 计算安全跑道位置（从外圈指向中心）
      const angle = (index / colors.length) * Math.PI * 2;
      const startRadius = 150 * this.scale;
      const endRadius = 50 * this.scale;
      
      for (let i = 0; i < BOARD_CONFIG.safeRunwayLength; i++) {
        const progress = i / (BOARD_CONFIG.safeRunwayLength - 1);
        const radius = startRadius - (startRadius - endRadius) * progress;
        
        positions.push({
          x: centerX + Math.cos(angle) * radius,
          y: centerY + Math.sin(angle) * radius,
          width: this.cellSize,
          height: this.cellSize
        });
      }
      
      this.safeRunways[color] = positions;
    });
  }
  
  /**
   * 初始化终点区域
   */
  initFinishArea() {
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;
    
    this.finishArea = {
      x: centerX,
      y: centerY,
      radius: 30 * this.scale
    };
  }
  
  /**
   * 获取棋子在棋盘上的位置
   * @param {Object} piece - 棋子对象
   * @returns {Object} 位置坐标
   */
  getPiecePosition(piece) {
    // 在基地
    if (piece.status === PIECE_STATUS.BASE) {
      const basePositions = this.baseAreas[piece.color].positions;
      return basePositions[piece.index] || { x: 0, y: 0 };
    }
    
    // 在跑道上
    if (piece.status === PIECE_STATUS.RUNWAY) {
      const cell = this.cells[piece.position];
      return cell ? { x: cell.x, y: cell.y } : { x: 0, y: 0 };
    }
    
    // 在安全跑道
    if (piece.status === PIECE_STATUS.SAFE) {
      const safeIndex = piece.position - 52;
      const safePositions = this.safeRunways[piece.color];
      return safePositions[safeIndex] || { x: 0, y: 0 };
    }
    
    // 已完成
    if (piece.status === PIECE_STATUS.FINISHED) {
      return {
        x: this.finishArea.x,
        y: this.finishArea.y
      };
    }
    
    return { x: 0, y: 0 };
  }
  
  /**
   * 检查点击位置
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {Object} 点击信息
   */
  checkClick(x, y) {
    // 检查是否点击了棋盘格子
    for (let i = 0; i < this.cells.length; i++) {
      const cell = this.cells[i];
      if (this.isPointInCell(x, y, cell)) {
        return {
          type: 'cell',
          cellIndex: i,
          cell: cell
        };
      }
    }
    
    // 检查是否点击了基地区域
    for (const [color, baseArea] of Object.entries(this.baseAreas)) {
      if (this.isPointInCircle(x, y, baseArea.x, baseArea.y, baseArea.radius)) {
        return {
          type: 'base',
          color: color,
          baseArea: baseArea
        };
      }
    }
    
    return { type: 'none' };
  }
  
  /**
   * 检查点是否在格子内
   */
  isPointInCell(x, y, cell) {
    return x >= cell.x - cell.width / 2 &&
           x <= cell.x + cell.width / 2 &&
           y >= cell.y - cell.height / 2 &&
           y <= cell.y + cell.height / 2;
  }
  
  /**
   * 检查点是否在圆形区域内
   */
  isPointInCircle(x, y, centerX, centerY, radius) {
    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
    return distance <= radius;
  }
  
  /**
   * 渲染棋盘
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (this.useCanvas) {
      this.renderWithCanvas(ctx);
    } else if (this.background) {
      this.background.render(ctx);
    }
  }
  
  /**
   * 使用Canvas绘制棋盘
   */
  renderWithCanvas(ctx) {
    // 绘制背景
    ctx.fillStyle = '#F5F5DC'; // 米色背景
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 绘制棋盘格子
    this.renderCells(ctx);
    
    // 绘制基地区域
    this.renderBaseAreas(ctx);
    
    // 绘制安全跑道
    this.renderSafeRunways(ctx);
    
    // 绘制终点区域
    this.renderFinishArea(ctx);
  }
  
  /**
   * 绘制棋盘格子
   */
  renderCells(ctx) {
    this.cells.forEach((cell, index) => {
      // 绘制格子背景
      ctx.fillStyle = this.getCellBackgroundColor(cell.color);
      ctx.fillRect(
        cell.x - cell.width / 2,
        cell.y - cell.height / 2,
        cell.width,
        cell.height
      );
      
      // 绘制格子边框
      ctx.strokeStyle = '#333333';
      ctx.lineWidth = 1;
      ctx.strokeRect(
        cell.x - cell.width / 2,
        cell.y - cell.height / 2,
        cell.width,
        cell.height
      );
      
      // 绘制特殊标记
      if (cell.isSpecial) {
        this.renderSpecialMarker(ctx, cell);
      }
    });
  }
  
  /**
   * 获取格子背景颜色
   */
  getCellBackgroundColor(color) {
    const colorMap = {
      'red': '#FFB6C1',
      'yellow': '#FFFFE0',
      'blue': '#ADD8E6',
      'green': '#90EE90',
      'white': '#FFFFFF'
    };
    
    return colorMap[color] || '#FFFFFF';
  }
  
  /**
   * 绘制特殊标记
   */
  renderSpecialMarker(ctx, cell) {
    ctx.fillStyle = '#FF6B6B';
    ctx.beginPath();
    ctx.arc(cell.x, cell.y, 5, 0, Math.PI * 2);
    ctx.fill();
  }
  
  /**
   * 绘制基地区域
   */
  renderBaseAreas(ctx) {
    for (const [color, baseArea] of Object.entries(this.baseAreas)) {
      // 绘制基地圆形区域
      ctx.fillStyle = this.getCellBackgroundColor(color);
      ctx.beginPath();
      ctx.arc(baseArea.x, baseArea.y, baseArea.radius, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.strokeStyle = '#333333';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 绘制基地内的棋子位置标记
      baseArea.positions.forEach(pos => {
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(pos.x, pos.y, this.cellSize / 3, 0, Math.PI * 2);
        ctx.stroke();
      });
    }
  }
  
  /**
   * 绘制安全跑道
   */
  renderSafeRunways(ctx) {
    for (const [color, positions] of Object.entries(this.safeRunways)) {
      positions.forEach((pos, index) => {
        ctx.fillStyle = this.getCellBackgroundColor(color);
        ctx.fillRect(
          pos.x - pos.width / 2,
          pos.y - pos.height / 2,
          pos.width,
          pos.height
        );
        
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 1;
        ctx.strokeRect(
          pos.x - pos.width / 2,
          pos.y - pos.height / 2,
          pos.width,
          pos.height
        );
      });
    }
  }
  
  /**
   * 绘制终点区域
   */
  renderFinishArea(ctx) {
    ctx.fillStyle = '#FFD700'; // 金色
    ctx.beginPath();
    ctx.arc(this.finishArea.x, this.finishArea.y, this.finishArea.radius, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 绘制终点标记
    ctx.fillStyle = '#333333';
    ctx.font = `${16 * this.scale}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('终点', this.finishArea.x, this.finishArea.y);
  }
  
  /**
   * 加载棋盘背景
   */
  loadBackground() {
    try {
      this.background = new Sprite(
        ASSETS.boardBackground,
        SCREEN_WIDTH,
        SCREEN_HEIGHT,
        0,
        0
      );
    } catch (e) {
      console.log('无法加载棋盘背景图片，将使用Canvas绘制', e);
      this.useCanvas = true;
    }
  }
  
  /**
   * 初始化棋盘格子
   */
  initCells() {
    this.cells = [];
    
    // 计算棋盘中心点
    const centerX = BOARD_LAYOUT.centerX * this.scale;
    const centerY = BOARD_LAYOUT.centerY * this.scale;
    
    // 棋盘半径 (从中心到最外层格子的距离)
    const boardRadius = 6 * this.cellSize;
    
    // 创建主跑道格子 (共52个)
    for (let i = 0; i < BOARD_CONFIG.totalCells; i++) {
      // 计算格子在圆上的角度 (逆时针方向)
      const angle = (i / BOARD_CONFIG.totalCells) * 2 * Math.PI;
      
      // 计算格子坐标
      const x = centerX + boardRadius * Math.cos(angle) - this.cellSize / 2;
      const y = centerY + boardRadius * Math.sin(angle) - this.cellSize / 2;
      
      // 确定格子类型
      let type = 'normal';
      let color = null;
      
      // 检查是否为彩色格子
      Object.entries(BOARD_CONFIG.coloredCells).forEach(([colorKey, positions]) => {
        if (positions.includes(i)) {
          type = 'colored';
          color = colorKey;
        }
      });
      
      // 检查是否为飞行格子
      Object.entries(BOARD_CONFIG.flyingCells).forEach(([colorKey, positions]) => {
        if (positions.includes(i)) {
          type = 'flying';
          color = colorKey;
        }
      });
      
      // 检查是否为起点
      Object.entries(BOARD_CONFIG.startPoints).forEach(([colorKey, position]) => {
        if (position === i) {
          type = 'start';
          color = colorKey;
        }
      });
      
      // 添加格子
      this.cells.push({
        index: i,
        x,
        y,
        type,
        color,
        pieces: [] // 当前格子上的棋子
      });
    }
  }
  
  /**
   * 初始化基地区域
   */
  initBaseAreas() {
    this.baseAreas = {};
    
    // 为每种颜色创建基地区域
    Object.entries(BOARD_LAYOUT.baseAreas).forEach(([color, area]) => {
      this.baseAreas[color] = {
        x: area.x * this.scale,
        y: area.y * this.scale,
        radius: area.radius * this.scale,
        color
      };
    });
  }
  
  /**
   * 初始化安全跑道
   */
  initSafeRunways() {
    this.safeRunways = {};
    
    // 计算棋盘中心点
    const centerX = BOARD_LAYOUT.centerX * this.scale;
    const centerY = BOARD_LAYOUT.centerY * this.scale;
    
    // 为每种颜色创建安全跑道
    Object.entries(BOARD_CONFIG.startPoints).forEach(([color, startPoint]) => {
      const runway = [];
      
      // 获取起点格子的角度
      const startCell = this.cells[startPoint];
      const startAngle = Math.atan2(
        startCell.y + this.cellSize / 2 - centerY,
        startCell.x + this.cellSize / 2 - centerX
      );
      
      // 创建6个安全跑道格子
      for (let i = 0; i < BOARD_CONFIG.safeRunwayLength; i++) {
        // 计算格子到中心的距离 (从外向内)
        const distance = (5 - i) * this.cellSize;
        
        // 计算格子坐标
        const x = centerX + distance * Math.cos(startAngle) - this.cellSize / 2;
        const y = centerY + distance * Math.sin(startAngle) - this.cellSize / 2;
        
        // 添加安全跑道格子
        runway.push({
          index: i,
          x,
          y,
          type: 'safe',
          color,
          pieces: [] // 当前格子上的棋子
        });
      }
      
      this.safeRunways[color] = runway;
    });
  }
  
  /**
   * 初始化终点区域
   */
  initFinishArea() {
    // 计算棋盘中心点
    const centerX = BOARD_LAYOUT.centerX * this.scale;
    const centerY = BOARD_LAYOUT.centerY * this.scale;
    
    // 创建终点区域
    this.finishArea = {
      x: centerX - this.cellSize / 2,
      y: centerY - this.cellSize / 2,
      width: this.cellSize,
      height: this.cellSize,
      pieces: [] // 到达终点的棋子
    };
  }
  
  /**
   * 获取棋子在基地的初始位置
   * @param {string} color - 棋子颜色
   * @param {number} index - 棋子索引
   * @returns {Object} 位置坐标
   */
  getPieceBasePosition(color, index) {
    const basePositions = BOARD_LAYOUT.basePositions[color];
    if (!basePositions || index >= basePositions.length) {
      return null;
    }
    
    return {
      x: basePositions[index].x * this.scale,
      y: basePositions[index].y * this.scale
    };
  }
  
  /**
   * 获取指定位置的格子
   * @param {number} position - 格子位置
   * @returns {Object} 格子对象
   */
  getCellByPosition(position) {
    if (position < 0 || position >= this.cells.length) {
      return null;
    }
    return this.cells[position];
  }
  
  /**
   * 获取安全跑道格子
   * @param {string} color - 棋子颜色
   * @param {number} index - 安全跑道索引
   * @returns {Object} 格子对象
   */
  getSafeRunwayCell(color, index) {
    const runway = this.safeRunways[color];
    if (!runway || index < 0 || index >= runway.length) {
      return null;
    }
    return runway[index];
  }
  
  /**
   * 添加棋子到格子
   * @param {Object} piece - 棋子对象
   * @param {number} position - 格子位置
   */
  addPieceToCell(piece, position) {
    const cell = this.getCellByPosition(position);
    if (cell) {
      cell.pieces.push(piece);
    }
  }
  
  /**
   * 从格子移除棋子
   * @param {Object} piece - 棋子对象
   * @param {number} position - 格子位置
   */
  removePieceFromCell(piece, position) {
    const cell = this.getCellByPosition(position);
    if (cell) {
      cell.pieces = cell.pieces.filter(p => p.id !== piece.id);
    }
  }
  
  /**
   * 添加棋子到安全跑道
   * @param {Object} piece - 棋子对象
   * @param {string} color - 棋子颜色
   * @param {number} index - 安全跑道索引
   */
  addPieceToSafeRunway(piece, color, index) {
    const cell = this.getSafeRunwayCell(color, index);
    if (cell) {
      cell.pieces.push(piece);
    }
  }
  
  /**
   * 从安全跑道移除棋子
   * @param {Object} piece - 棋子对象
   * @param {string} color - 棋子颜色
   * @param {number} index - 安全跑道索引
   */
  removePieceFromSafeRunway(piece, color, index) {
    const cell = this.getSafeRunwayCell(color, index);
    if (cell) {
      cell.pieces = cell.pieces.filter(p => p.id !== piece.id);
    }
  }
  
  /**
   * 添加棋子到终点
   * @param {Object} piece - 棋子对象
   */
  addPieceToFinish(piece) {
    this.finishArea.pieces.push(piece);
  }
  
  /**
   * 使用Canvas绘制棋盘背景
   * @param {Object} ctx - Canvas上下文
   */
  drawBackground(ctx) {
    // 保存上下文
    ctx.save();
    
    // 绘制背景
    ctx.fillStyle = '#F0F0F0';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 绘制边框
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.strokeRect(10, 10, SCREEN_WIDTH - 20, SCREEN_HEIGHT - 20);
    
    // 绘制标题
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('飞行棋', SCREEN_WIDTH / 2, 40);
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 渲染棋盘
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    // 绘制背景
    if (this.useCanvas) {
      this.drawBackground(ctx);
    } else if (this.background) {
      this.background.render(ctx);
    }
    
    // 绘制基地区域
    this.renderBaseAreas(ctx);
    
    // 绘制主跑道格子
    this.renderCells(ctx);
    
    // 绘制安全跑道
    this.renderSafeRunways(ctx);
    
    // 绘制终点区域
    this.renderFinishArea(ctx);
  }
  
  /**
   * 渲染基地区域
   * @param {Object} ctx - Canvas上下文
   */
  renderBaseAreas(ctx) {
    Object.values(this.baseAreas).forEach(base => {
      ctx.beginPath();
      ctx.arc(base.x, base.y, base.radius, 0, 2 * Math.PI);
      ctx.fillStyle = base.color;
      ctx.globalAlpha = 0.2;
      ctx.fill();
      ctx.globalAlpha = 1;
      ctx.strokeStyle = base.color;
      ctx.lineWidth = 2;
      ctx.stroke();
    });
  }
  
  /**
   * 渲染主跑道格子
   * @param {Object} ctx - Canvas上下文
   */
  renderCells(ctx) {
    this.cells.forEach(cell => {
      // 绘制格子
      ctx.beginPath();
      ctx.rect(cell.x, cell.y, this.cellSize, this.cellSize);
      
      // 根据格子类型设置颜色
      if (cell.type === 'normal') {
        ctx.fillStyle = '#FFFFFF';
      } else if (cell.type === 'colored' || cell.type === 'start') {
        ctx.fillStyle = cell.color;
      } else if (cell.type === 'flying') {
        // 飞行格子使用渐变色
        const gradient = ctx.createLinearGradient(
          cell.x, cell.y,
          cell.x + this.cellSize, cell.y + this.cellSize
        );
        gradient.addColorStop(0, cell.color);
        gradient.addColorStop(1, '#FFFFFF');
        ctx.fillStyle = gradient;
      }
      
      ctx.fill();
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 1;
      ctx.stroke();
      
      // 如果是起点，绘制箭头
      if (cell.type === 'start') {
        this.drawStartArrow(ctx, cell);
      }
      
      // 如果是飞行格子，绘制飞机图标
      if (cell.type === 'flying') {
        this.drawFlyingIcon(ctx, cell);
      }
    });
  }
  
  /**
   * 绘制起点箭头
   * @param {Object} ctx - Canvas上下文
   * @param {Object} cell - 格子对象
   */
  drawStartArrow(ctx, cell) {
    const centerX = cell.x + this.cellSize / 2;
    const centerY = cell.y + this.cellSize / 2;
    const arrowSize = this.cellSize * 0.4;
    
    ctx.beginPath();
    ctx.moveTo(centerX - arrowSize / 2, centerY - arrowSize / 2);
    ctx.lineTo(centerX + arrowSize / 2, centerY);
    ctx.lineTo(centerX - arrowSize / 2, centerY + arrowSize / 2);
    ctx.closePath();
    
    ctx.fillStyle = '#FFFFFF';
    ctx.fill();
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.stroke();
  }
  
  /**
   * 绘制飞行图标
   * @param {Object} ctx - Canvas上下文
   * @param {Object} cell - 格子对象
   */
  drawFlyingIcon(ctx, cell) {
    const centerX = cell.x + this.cellSize / 2;
    const centerY = cell.y + this.cellSize / 2;
    const iconSize = this.cellSize * 0.5;
    
    // 简单绘制飞机图标
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - iconSize / 2);
    ctx.lineTo(centerX + iconSize / 3, centerY);
    ctx.lineTo(centerX, centerY + iconSize / 2);
    ctx.lineTo(centerX - iconSize / 3, centerY);
    ctx.closePath();
    
    ctx.fillStyle = '#000000';
    ctx.fill();
  }
  
  /**
   * 渲染安全跑道
   * @param {Object} ctx - Canvas上下文
   */
  renderSafeRunways(ctx) {
    Object.entries(this.safeRunways).forEach(([color, runway]) => {
      runway.forEach(cell => {
        // 绘制格子
        ctx.beginPath();
        ctx.rect(cell.x, cell.y, this.cellSize, this.cellSize);
        
        // 安全跑道使用对应颜色，但透明度较低
        ctx.fillStyle = color;
        ctx.globalAlpha = 0.4;
        ctx.fill();
        ctx.globalAlpha = 1;
        
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.stroke();
      });
    });
  }
  
  /**
   * 渲染终点区域
   * @param {Object} ctx - Canvas上下文
   */
  renderFinishArea(ctx) {
    // 绘制终点区域
    ctx.beginPath();
    ctx.rect(
      this.finishArea.x,
      this.finishArea.y,
      this.finishArea.width,
      this.finishArea.height
    );
    
    // 使用彩色渐变
    const gradient = ctx.createRadialGradient(
      this.finishArea.x + this.cellSize / 2,
      this.finishArea.y + this.cellSize / 2,
      0,
      this.finishArea.x + this.cellSize / 2,
      this.finishArea.y + this.cellSize / 2,
      this.cellSize / 2
    );
    
    gradient.addColorStop(0, '#FFFFFF');
    gradient.addColorStop(0.25, PLAYER_COLORS.RED);
    gradient.addColorStop(0.5, PLAYER_COLORS.YELLOW);
    gradient.addColorStop(0.75, PLAYER_COLORS.BLUE);
    gradient.addColorStop(1, PLAYER_COLORS.GREEN);
    
    ctx.fillStyle = gradient;
    ctx.fill();
    
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 绘制"终点"文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `bold ${this.cellSize * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      '终点',
      this.finishArea.x + this.cellSize / 2,
      this.finishArea.y + this.cellSize / 2
    );
  }
  
  /**
   * 检查点击位置是否在格子内
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {Object} 点击的格子
   */
  checkCellClick(x, y) {
    // 检查主跑道格子
    for (const cell of this.cells) {
      if (
        x >= cell.x &&
        x <= cell.x + this.cellSize &&
        y >= cell.y &&
        y <= cell.y + this.cellSize
      ) {
        return { type: 'main', cell };
      }
    }
    
    // 检查安全跑道格子
    for (const [color, runway] of Object.entries(this.safeRunways)) {
      for (const cell of runway) {
        if (
          x >= cell.x &&
          x <= cell.x + this.cellSize &&
          y >= cell.y &&
          y <= cell.y + this.cellSize
        ) {
          return { type: 'safe', color, cell };
        }
      }
    }
    
    // 检查终点区域
    if (
      x >= this.finishArea.x &&
      x <= this.finishArea.x + this.finishArea.width &&
      y >= this.finishArea.y &&
      y <= this.finishArea.y + this.finishArea.height
    ) {
      return { type: 'finish', cell: this.finishArea };
    }
    
    // 检查基地区域
    for (const [color, base] of Object.entries(this.baseAreas)) {
      const distance = Math.sqrt(
        Math.pow(x - base.x, 2) + Math.pow(y - base.y, 2)
      );
      if (distance <= base.radius) {
        return { type: 'base', color, cell: base };
      }
    }
    
    return null;
  }
}