from tortoise import fields
from datetime import datetime
import pytz
from models.base_model import BaseModel, SHANGHAI_TZ, get_shanghai_now
from config.timezone import add_tz

class NetworkStatsSummary(BaseModel):
    """服务器网卡监控数据总体统计"""
    
    ip = fields.CharField(max_length=50, description="服务器IP地址")
    total_interfaces = fields.IntField(description="网卡总数量")
    active_interfaces = fields.IntField(description="活动网卡数量")
    timestamp = fields.DatetimeField(description="记录时间")
    
    class Meta:
        table = "network_stats_summary"
        description = "网卡监控数据总体统计"
    
    def __str__(self):
        return f"{self.ip} - {self.timestamp} - 活动网卡:{self.active_interfaces}/{self.total_interfaces}"
        
    async def save(self, *args, **kwargs):
        """
        重写保存方法，使用新的时区处理策略
        """
        # 导入新的时区工具
        from config.timezone_utils import TZ

        # 如果timestamp未设置，使用当前UTC时间（带时区信息）
        if not self.timestamp:
            self.timestamp = TZ.now_utc()  # 保持UTC时区信息

        # 调用父类的save方法继续处理
        return await super().save(*args, **kwargs)

    def get_timestamp_display(self) -> str:
        """获取时间戳的显示格式（上海时区）"""
        if not self.timestamp:
            return None
        from config.timezone_utils import TZ
        # 如果timestamp已经有时区信息，直接转换；否则假设为UTC
        if self.timestamp.tzinfo is None:
            return TZ.to_display_format(self.timestamp, source_tz=TZ.UTC)
        else:
            return TZ.to_display_format(self.timestamp)


class NetworkStatsDetail(BaseModel):
    """单个网卡详细监控数据"""
    
    summary = fields.ForeignKeyField(
        'models.NetworkStatsSummary',
        related_name='details',
        description="关联的总体统计",
        on_delete=fields.CASCADE  # 当摘要记录被删除时，级联删除详细记录
    )
    interface_name = fields.CharField(max_length=50, description="网卡名称")
    state = fields.CharField(max_length=20, description="网卡状态(up/down)")
    mac_address = fields.CharField(max_length=100, description="MAC地址")
    speed = fields.CharField(max_length=50, description="网卡速率")
    ip_address = fields.CharField(max_length=50, description="IP地址", null=True)
    rx_bytes = fields.BigIntField(description="接收字节数")
    rx_mb = fields.FloatField(description="接收MB数")
    rx_packets = fields.BigIntField(description="接收数据包数")
    tx_bytes = fields.BigIntField(description="发送字节数")
    tx_mb = fields.FloatField(description="发送MB数")
    tx_packets = fields.BigIntField(description="发送数据包数")
    current_rx_speed = fields.FloatField(description="当前接收速率(Mbps)", null=True, default=0)
    current_tx_speed = fields.FloatField(description="当前发送速率(Mbps)", null=True, default=0)
    
    class Meta:
        table = "network_stats_detail"
        description = "网卡详细监控数据"
    
    def __str__(self):
        return f"{self.interface_name} - {self.state} - {self.ip_address or '无IP'}" 