<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from 'echarts'
import { getTheme, THEME_TYPE } from '@/utils/theme'

export default {
  name: 'LineChart',
  props: {
    chartData: {
      type: Array,
      required: true
    },
    chartTitle: {
      type: String,
      required: true
    },
    yAxisName: {
      type: String,
      default: ''
    },
    chartColor: {
      type: String,
      default: '#409EFF'
    },
    yAxisMin: {
      type: Number,
      default: null
    },
    yAxisMax: {
      type: Number,
      default: null
    },
    showMockData: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      chart: null,
      resizeObserver: null,
      currentTheme: getTheme()
    }
  },
  computed: {
    isDarkMode () {
      return this.currentTheme === THEME_TYPE.DARK
    },
    textColor () {
      return this.isDarkMode ? '#e0e0e0' : '#333'
    },
    axisLineColor () {
      return this.isDarkMode ? '#444444' : '#eee'
    },
    noDataTextColor () {
      return this.isDarkMode ? '#aaaaaa' : '#999'
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler () {
        this.updateChart()
      }
    }
  },
  mounted () {
    this.initChart()

    // 监听容器大小变化，自适应调整图表大小
    this.resizeObserver = new ResizeObserver(() => {
      this.chart && this.chart.resize()
    })
    this.resizeObserver.observe(this.$refs.chartContainer)

    // 监听窗口大小变化
    window.addEventListener('resize', this.resizeChart)

    // 监听主题变化
    window.addEventListener('storage', this.handleStorageChange)
    document.addEventListener('themeChange', this.handleThemeChange)
  },
  beforeDestroy () {
    // 清理chart实例和事件监听器
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }

    window.removeEventListener('resize', this.resizeChart)
    window.removeEventListener('storage', this.handleStorageChange)
    document.removeEventListener('themeChange', this.handleThemeChange)
  },
  methods: {
    initChart () {
      // 初始化chart实例，根据当前主题设置
      const theme = this.isDarkMode ? 'dark' : ''
      this.chart = echarts.init(this.$refs.chartContainer, theme)
      this.updateChart()
    },

    updateChart () {
      if (!this.chart) return

      // 如果没有数据，显示无数据信息
      if ((!this.chartData || this.chartData.length === 0) && !this.showMockData) {
        this.chart.setOption({
          title: {
            text: this.chartTitle,
            left: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: this.textColor
            }
          },
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 16,
              fontWeight: 'normal',
              fill: this.noDataTextColor
            }
          }
        })
        return
      }

      // 使用实际数据或模拟数据
      let xAxisData = []
      let seriesData = []

      if (this.showMockData && (!this.chartData || this.chartData.length === 0)) {
        // 生成模拟时间数据，从当前时间往前推24小时
        const now = new Date()
        for (let i = 23; i >= 0; i--) {
          const time = new Date(now)
          time.setHours(now.getHours() - i)
          const hours = String(time.getHours()).padStart(2, '0')
          const minutes = String(time.getMinutes()).padStart(2, '0')
          xAxisData.push(`${hours}:${minutes}`)
          seriesData.push(0) // 填充0值，只显示轴线不显示数据
        }
      } else {
        // 使用实际数据
        xAxisData = this.chartData.map(item => item.time)
        seriesData = this.chartData.map(item => item.value)
      }

      // 计算数据的最大值和最小值，用于自动计算Y轴范围
      const dataMax = Math.max(...seriesData)

      // 对于小数据（如使用率<1%的情况），固定Y轴的最大值为更合理的范围
      let yAxisMaxValue
      if (dataMax < 1 || this.showMockData) { // 小于1%或使用模拟数据
        yAxisMaxValue = 2 // 固定为2%
      } else if (dataMax < 5) { // 小于5%
        yAxisMaxValue = 8 // 固定为8%
      } else {
        // 默认逻辑，确保有足够的上边距
        yAxisMaxValue = this.yAxisMax !== null ? this.yAxisMax : Math.ceil(dataMax * 1.5)
      }

      // 确保最小值是0或者指定的最小值
      const yAxisMinValue = this.yAxisMin !== null ? this.yAxisMin : 0

      // 准备系列配置
      let symbolType = 'circle'
      let lineWidth = 3
      let areaStyleConfig = {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: this.chartColor + '50' // 50表示透明度0.31
          }, {
            offset: 1,
            color: this.chartColor + '10' // 10表示透明度0.06
          }]
        }
      }
      let markPointConfig = {
        symbolSize: 40,
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ],
        label: {
          formatter: param => {
            return param.value.toFixed(2) + '%'
          }
        }
      }

      // 模拟数据模式下的配置
      if (this.showMockData) {
        symbolType = 'none'
        lineWidth = 0
        areaStyleConfig = undefined
        markPointConfig = undefined
      }

      const option = {
        title: {
          text: this.chartTitle,
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: this.textColor
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: params => {
            const param = params[0]
            return `${param.name}<br>${param.seriesName}: ${param.value.toFixed(2)}${this.yAxisName}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLine: {
            lineStyle: {
              color: this.axisLineColor
            }
          },
          axisLabel: {
            color: this.textColor
          }
        },
        yAxis: {
          type: 'value',
          name: this.yAxisName,
          min: yAxisMinValue,
          max: yAxisMaxValue,
          axisLine: {
            lineStyle: {
              color: this.axisLineColor
            }
          },
          axisLabel: {
            color: this.textColor,
            formatter: value => {
              return value + this.yAxisName
            }
          },
          splitLine: {
            lineStyle: {
              color: this.isDarkMode ? '#333' : '#eee'
            }
          }
        },
        series: [
          {
            name: this.chartTitle || '数值',
            type: 'line',
            symbol: symbolType,
            symbolSize: 8,
            lineStyle: {
              width: lineWidth,
              color: this.chartColor
            },
            itemStyle: {
              color: this.chartColor
            },
            areaStyle: areaStyleConfig,
            data: seriesData,
            markPoint: markPointConfig,
            smooth: true
          }
        ]
      }

      this.chart.setOption(option)
    },

    resizeChart () {
      this.chart && this.chart.resize()
    },

    // 处理主题变化
    handleThemeChange (event) {
      this.currentTheme = event.detail.theme
      this.recreateChart()
    },

    // 处理localStorage变化，检测主题变化
    handleStorageChange (event) {
      if (event.key === 'monitor_theme') {
        this.currentTheme = event.newValue
        this.recreateChart()
      }
    },

    // 重新创建图表以应用新主题
    recreateChart () {
      if (this.chart) {
        const theme = this.isDarkMode ? 'dark' : ''
        this.chart.dispose()
        this.chart = echarts.init(this.$refs.chartContainer, theme)
        this.updateChart()
      }
    }
  }
}
</script>

<style scoped lang="less">
.chart-container {
  width: 100%;
  height: 300px;
}
</style>
