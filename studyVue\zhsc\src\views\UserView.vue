<template>
  <div>
    <h2>User Page</h2>
    <p>这里是用户页面。</p>
    <div v-if="userInfo">
      <p>用户ID: {{ userInfo.userId || 'N/A' }}</p>
    </div>
    <button class="logout-btn" @click="handleLogout">退出登录</button>
  </div>
</template>

<script>
export default {
  data () {
    return {
      userInfo: null
    }
  },
  created () {
    // 获取登录状态
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('userId')

    if (token && userId) {
      this.userInfo = { token, userId }
    }
  },
  methods: {
    handleLogout () {
      // 清除本地存储的登录信息
      localStorage.removeItem('token')
      localStorage.removeItem('userId')

      // 提示用户
      this.$toast && this.$toast('已退出登录')

      // 跳转到登录页
      this.$router.push('/login')
    }
  }
}
</script>

<style>
.logout-btn {
  margin-top: 20px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
.logout-btn:hover {
  background-color: #ff7875;
}
</style>
