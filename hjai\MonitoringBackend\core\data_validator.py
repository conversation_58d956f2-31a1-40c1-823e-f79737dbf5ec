"""
数据验证器

提供输入数据验证、输出数据格式验证、敏感信息过滤等功能
"""

import re
import json
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
import ipaddress
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        # 敏感信息模式
        self.sensitive_patterns = [
            r'password\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'passwd\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'secret\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'token\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'key\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'api_key\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'private_key\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
            r'-----BEGIN\s+(?:RSA\s+)?PRIVATE\s+KEY-----',
            r'-----BEGIN\s+CERTIFICATE-----',
            # 信用卡号模式
            r'\b(?:\d{4}[-\s]?){3}\d{4}\b',
            # 身份证号模式
            r'\b\d{17}[\dXx]\b',
            # 手机号模式
            r'\b1[3-9]\d{9}\b',
            # 邮箱模式（可能包含敏感信息）
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        ]
        
        # 数据类型验证规则
        self.validation_rules = {
            'string': self._validate_string,
            'number': self._validate_number,
            'integer': self._validate_integer,
            'float': self._validate_float,
            'boolean': self._validate_boolean,
            'json': self._validate_json,
            'array': self._validate_array,
            'object': self._validate_object,
            'ip_address': self._validate_ip_address,
            'url': self._validate_url,
            'email': self._validate_email,
            'date': self._validate_date,
            'datetime': self._validate_datetime
        }
    
    def validate_input_data(
        self,
        data: Any,
        data_type: str,
        constraints: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[str], Any]:
        """
        验证输入数据
        
        Args:
            data: 要验证的数据
            data_type: 数据类型
            constraints: 约束条件
            
        Returns:
            Tuple[bool, Optional[str], Any]: (是否有效, 错误信息, 清理后的数据)
        """
        try:
            # 基本类型验证
            if data_type not in self.validation_rules:
                return False, f"不支持的数据类型: {data_type}", None
            
            is_valid, error, cleaned_data = self.validation_rules[data_type](data)
            if not is_valid:
                return False, error, None
            
            # 应用约束条件
            if constraints:
                is_valid, error = self._apply_constraints(cleaned_data, constraints)
                if not is_valid:
                    return False, error, None
            
            return True, None, cleaned_data
            
        except Exception as e:
            return False, f"数据验证异常: {str(e)}", None
    
    def validate_output_data(
        self,
        data: str,
        expected_type: str,
        sanitize: bool = True
    ) -> Tuple[bool, Optional[str], Any]:
        """
        验证输出数据
        
        Args:
            data: 输出数据字符串
            expected_type: 期望的数据类型
            sanitize: 是否清理敏感信息
            
        Returns:
            Tuple[bool, Optional[str], Any]: (是否有效, 错误信息, 解析后的数据)
        """
        try:
            if not data:
                return True, None, None
            
            # 清理敏感信息
            if sanitize:
                data = self.sanitize_sensitive_data(data)
            
            # 根据期望类型解析数据
            if expected_type == 'string':
                return True, None, data
            elif expected_type == 'number':
                try:
                    if '.' in data:
                        return True, None, float(data.strip())
                    else:
                        return True, None, int(data.strip())
                except ValueError:
                    return True, None, data  # 如果无法解析为数字，返回原始字符串
            elif expected_type == 'json':
                try:
                    parsed_data = json.loads(data)
                    return True, None, parsed_data
                except json.JSONDecodeError as e:
                    return False, f"JSON解析失败: {str(e)}", None
            elif expected_type == 'boolean':
                lower_data = data.lower().strip()
                if lower_data in ['true', '1', 'yes', 'on', 'enabled']:
                    return True, None, True
                elif lower_data in ['false', '0', 'no', 'off', 'disabled']:
                    return True, None, False
                else:
                    return True, None, data  # 返回原始值
            else:
                return True, None, data
                
        except Exception as e:
            return False, f"输出数据验证异常: {str(e)}", None
    
    def sanitize_sensitive_data(self, data: str) -> str:
        """
        清理敏感信息
        
        Args:
            data: 原始数据
            
        Returns:
            str: 清理后的数据
        """
        sanitized_data = data
        
        for pattern in self.sensitive_patterns:
            # 替换敏感信息为 ***
            sanitized_data = re.sub(pattern, r'***REDACTED***', sanitized_data, flags=re.IGNORECASE)
        
        return sanitized_data
    
    def detect_sensitive_data(self, data: str) -> List[str]:
        """
        检测敏感信息
        
        Args:
            data: 要检测的数据
            
        Returns:
            List[str]: 检测到的敏感信息类型列表
        """
        detected_types = []
        
        pattern_types = {
            r'password\s*[:=]': 'password',
            r'secret\s*[:=]': 'secret',
            r'token\s*[:=]': 'token',
            r'key\s*[:=]': 'key',
            r'-----BEGIN\s+(?:RSA\s+)?PRIVATE\s+KEY-----': 'private_key',
            r'\b(?:\d{4}[-\s]?){3}\d{4}\b': 'credit_card',
            r'\b\d{17}[\dXx]\b': 'id_card',
            r'\b1[3-9]\d{9}\b': 'phone_number',
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b': 'email'
        }
        
        for pattern, data_type in pattern_types.items():
            if re.search(pattern, data, re.IGNORECASE):
                detected_types.append(data_type)
        
        return detected_types
    
    def _validate_string(self, data: Any) -> Tuple[bool, Optional[str], str]:
        """验证字符串"""
        if not isinstance(data, str):
            data = str(data)
        
        # 检查长度限制
        if len(data) > 10000:
            return False, "字符串长度超过限制(10000字符)", None
        
        return True, None, data
    
    def _validate_number(self, data: Any) -> Tuple[bool, Optional[str], Union[int, float]]:
        """验证数字"""
        try:
            if isinstance(data, (int, float)):
                return True, None, data
            
            if isinstance(data, str):
                data = data.strip()
                if '.' in data:
                    return True, None, float(data)
                else:
                    return True, None, int(data)
            
            return False, "无法转换为数字", None
            
        except (ValueError, TypeError):
            return False, "数字格式无效", None
    
    def _validate_integer(self, data: Any) -> Tuple[bool, Optional[str], int]:
        """验证整数"""
        try:
            if isinstance(data, int):
                return True, None, data
            
            if isinstance(data, str):
                return True, None, int(data.strip())
            
            if isinstance(data, float) and data.is_integer():
                return True, None, int(data)
            
            return False, "无法转换为整数", None
            
        except (ValueError, TypeError):
            return False, "整数格式无效", None
    
    def _validate_float(self, data: Any) -> Tuple[bool, Optional[str], float]:
        """验证浮点数"""
        try:
            if isinstance(data, float):
                return True, None, data
            
            if isinstance(data, (int, str)):
                return True, None, float(str(data).strip())
            
            return False, "无法转换为浮点数", None
            
        except (ValueError, TypeError):
            return False, "浮点数格式无效", None
    
    def _validate_boolean(self, data: Any) -> Tuple[bool, Optional[str], bool]:
        """验证布尔值"""
        if isinstance(data, bool):
            return True, None, data
        
        if isinstance(data, str):
            lower_data = data.lower().strip()
            if lower_data in ['true', '1', 'yes', 'on', 'enabled']:
                return True, None, True
            elif lower_data in ['false', '0', 'no', 'off', 'disabled']:
                return True, None, False
        
        if isinstance(data, int):
            return True, None, bool(data)
        
        return False, "无法转换为布尔值", None
    
    def _validate_json(self, data: Any) -> Tuple[bool, Optional[str], Any]:
        """验证JSON"""
        try:
            if isinstance(data, str):
                parsed_data = json.loads(data)
                return True, None, parsed_data
            elif isinstance(data, (dict, list)):
                return True, None, data
            else:
                return False, "不是有效的JSON格式", None
                
        except json.JSONDecodeError as e:
            return False, f"JSON解析失败: {str(e)}", None
    
    def _validate_array(self, data: Any) -> Tuple[bool, Optional[str], List]:
        """验证数组"""
        if isinstance(data, list):
            return True, None, data
        
        if isinstance(data, str):
            try:
                parsed_data = json.loads(data)
                if isinstance(parsed_data, list):
                    return True, None, parsed_data
                else:
                    return False, "不是有效的数组格式", None
            except json.JSONDecodeError:
                return False, "数组格式无效", None
        
        return False, "无法转换为数组", None
    
    def _validate_object(self, data: Any) -> Tuple[bool, Optional[str], Dict]:
        """验证对象"""
        if isinstance(data, dict):
            return True, None, data
        
        if isinstance(data, str):
            try:
                parsed_data = json.loads(data)
                if isinstance(parsed_data, dict):
                    return True, None, parsed_data
                else:
                    return False, "不是有效的对象格式", None
            except json.JSONDecodeError:
                return False, "对象格式无效", None
        
        return False, "无法转换为对象", None
    
    def _validate_ip_address(self, data: Any) -> Tuple[bool, Optional[str], str]:
        """验证IP地址"""
        try:
            if isinstance(data, str):
                ipaddress.ip_address(data.strip())
                return True, None, data.strip()
            else:
                return False, "IP地址必须是字符串", None
                
        except ValueError:
            return False, "无效的IP地址格式", None
    
    def _validate_url(self, data: Any) -> Tuple[bool, Optional[str], str]:
        """验证URL"""
        try:
            if isinstance(data, str):
                result = urlparse(data.strip())
                if result.scheme and result.netloc:
                    return True, None, data.strip()
                else:
                    return False, "无效的URL格式", None
            else:
                return False, "URL必须是字符串", None
                
        except Exception:
            return False, "URL格式验证失败", None
    
    def _validate_email(self, data: Any) -> Tuple[bool, Optional[str], str]:
        """验证邮箱"""
        if isinstance(data, str):
            email_pattern = r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'
            if re.match(email_pattern, data.strip()):
                return True, None, data.strip()
            else:
                return False, "无效的邮箱格式", None
        else:
            return False, "邮箱必须是字符串", None
    
    def _validate_date(self, data: Any) -> Tuple[bool, Optional[str], str]:
        """验证日期"""
        try:
            if isinstance(data, str):
                # 尝试解析常见的日期格式
                date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%d-%m-%Y']
                for fmt in date_formats:
                    try:
                        datetime.strptime(data.strip(), fmt)
                        return True, None, data.strip()
                    except ValueError:
                        continue
                return False, "无效的日期格式", None
            else:
                return False, "日期必须是字符串", None
                
        except Exception:
            return False, "日期格式验证失败", None
    
    def _validate_datetime(self, data: Any) -> Tuple[bool, Optional[str], str]:
        """验证日期时间"""
        try:
            if isinstance(data, str):
                # 尝试解析常见的日期时间格式
                datetime_formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%S.%f'
                ]
                for fmt in datetime_formats:
                    try:
                        datetime.strptime(data.strip(), fmt)
                        return True, None, data.strip()
                    except ValueError:
                        continue
                return False, "无效的日期时间格式", None
            else:
                return False, "日期时间必须是字符串", None
                
        except Exception:
            return False, "日期时间格式验证失败", None
    
    def _apply_constraints(self, data: Any, constraints: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """应用约束条件"""
        try:
            # 长度约束
            if 'min_length' in constraints and hasattr(data, '__len__'):
                if len(data) < constraints['min_length']:
                    return False, f"长度不能小于 {constraints['min_length']}"
            
            if 'max_length' in constraints and hasattr(data, '__len__'):
                if len(data) > constraints['max_length']:
                    return False, f"长度不能大于 {constraints['max_length']}"
            
            # 数值约束
            if 'min_value' in constraints and isinstance(data, (int, float)):
                if data < constraints['min_value']:
                    return False, f"值不能小于 {constraints['min_value']}"
            
            if 'max_value' in constraints and isinstance(data, (int, float)):
                if data > constraints['max_value']:
                    return False, f"值不能大于 {constraints['max_value']}"
            
            # 正则表达式约束
            if 'pattern' in constraints and isinstance(data, str):
                if not re.match(constraints['pattern'], data):
                    return False, f"不匹配模式: {constraints['pattern']}"
            
            # 枚举约束
            if 'enum' in constraints:
                if data not in constraints['enum']:
                    return False, f"值必须是以下之一: {constraints['enum']}"
            
            return True, None
            
        except Exception as e:
            return False, f"约束验证异常: {str(e)}"


# 全局数据验证器实例
_data_validator: Optional[DataValidator] = None


def get_data_validator() -> DataValidator:
    """获取全局数据验证器实例"""
    global _data_validator
    if _data_validator is None:
        _data_validator = DataValidator()
    return _data_validator
