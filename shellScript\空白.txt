#!/usr/bin/bash

NUM_SMs=92
RUN_ITERS=1000

run_tests () {
  local ZEROGEMM=""
  if [ $1 -eq 0 ]; then
    echo "All 0 INPUT"
    ZEROGEMM="-zeroGemm"
  elif [ $1 -eq 1 ]; then
    echo "RANDOM INPUT"
  fi 
  echo "INT8"
  ./peakTOPS -mma imma16832_i8i8i32 -ctas ${NUM_SMs} -mainloopIter 64 -mmaIter 64 -noneMmaIter 0 -warps 8 -ldgNum 0 -stsNum 0 -ldsmNum 0 -runIter ${RUN_ITERS} ${ZEROGEMM}
  echo "FP8"
  ./peakTOPS -mma qmma16832_fp16e4m3e4m3fp16 -ctas ${NUM_SMs} -mainloopIter 64 -mmaIter 64 -noneMmaIter 0 -warps 8 -ldgNum 0 -stsNum 0 -ldsmNum 0 -runIter ${RUN_ITERS} ${ZEROGEMM}
  echo "FP16"
  ./peakTOPS  -mma hmma16816_fp16fp16fp16 -ctas ${NUM_SMs} -mainloopIter 64 -mmaIter 64 -noneMmaIter 0 -warps 8 -ldgNum 0 -stsNum 0 -ldsmNum 0 -runIter ${RUN_ITERS} ${ZEROGEMM} 
  echo "BF16"
  ./peakTOPS  -mma hmma16816_e8m7e8m7fp32 -ctas ${NUM_SMs} -mainloopIter 64 -mmaIter 64 -noneMmaIter 0 -warps 8 -ldgNum 0 -stsNum 0 -ldsmNum 0 -runIter ${RUN_ITERS} ${ZEROGEMM}
  echo "TF32"
  ./peakTOPS  -mma hmma1688_e8m10e8m10fp32 -ctas ${NUM_SMs} -mainloopIter 64 -mmaIter 64 -noneMmaIter 0 -warps 8 -ldgNum 0 -stsNum 0 -ldsmNum 0 -runIter ${RUN_ITERS} ${ZEROGEMM}
  echo "FP32 FFMA"
  ./peakTOPS -mma ffma_fp32fp32fp32 -ctas ${NUM_SMs} -mainloopIter 64 -mmaIter 64 -noneMmaIter 0 -warps 8 -ldgNum 0 -stsNum 0 -ldsmNum 0 -runIter ${RUN_ITERS} ${ZEROGEMM}
}

main () {
if [ -f peakTOPS ]; then
  run_tests 0
  #run_tests 1
else
  echo "Cannot find peakTOPS binary"
fi
}

main
