<template>
  <div class="admin-page">
    <el-container class="admin-container">
      <!-- 侧边栏 -->
      <el-aside width="240px" class="sidebar">
        <div class="logo-container">
          <h2 class="logo-text">监控系统</h2>
        </div>
        <el-menu
          default-active="1"
          class="sidebar-menu"
        >
          <el-menu-item index="1">
            <i class="el-icon-monitor"></i>
            <span>IP监控管理</span>
          </el-menu-item>
          <el-menu-item index="2">
            <i class="el-icon-s-operation"></i>
            <span>监控项管理</span>
          </el-menu-item>
          <el-menu-item index="3">
            <i class="el-icon-connection"></i>
            <span>IP关联监控项</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主要内容区域 -->
      <el-container>
        <el-header class="header">
          <span class="header-title">监控系统管理后台</span>
        </el-header>

        <el-main class="main-content">
          <!-- IP监控管理 -->
          <div v-show="activeIndex === '1'" class="content-section">
            <IPManagement />
          </div>

          <!-- 监控项管理 -->
          <div v-show="activeIndex === '2'" class="content-section">
            <div class="section-header">
              <h2>监控项管理</h2>
              <div class="action-group">
                <el-input
                  v-model="searchIp"
                  placeholder="输入IP搜索监控项"
                  class="search-input"
                  clearable
                  prefix-icon="el-icon-search"
                ></el-input>
                <el-button type="primary" @click="searchMonitorItems" size="small" round>搜索</el-button>
                <el-button type="success" @click="showAddMonitorItemDialog" size="small" icon="el-icon-plus" round>添加监控项</el-button>
              </div>
            </div>

            <el-table :data="monitorItems" class="custom-table">
              <el-table-column label="名称" prop="name"></el-table-column>
              <el-table-column label="描述" prop="description"></el-table-column>
              <el-table-column label="数据类型" prop="data_type"></el-table-column>
              <el-table-column label="命令" width="100">
                <template slot-scope="scope">
                  <el-tooltip :content="scope.row.command" placement="top" effect="light">
                    <el-button size="mini" round type="info" plain>查看命令</el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template slot-scope="scope">
                  <el-button type="primary" size="mini" plain round @click="editMonitorItem(scope.row)">编辑</el-button>
                  <el-button type="danger" size="mini" plain round @click="deleteMonitorItem(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- IP关联监控项 -->
          <div v-show="activeIndex === '3'" class="content-section">
            <div class="section-header">
              <h2>IP关联监控项</h2>
              <div class="action-group">
                <el-select v-model="selectedIp" placeholder="请选择IP" class="select-input">
                  <el-option
                    v-for="item in ipList"
                    :key="item.ip"
                    :label="item.ip"
                    :value="item.ip">
                  </el-option>
                </el-select>
                <el-button type="primary" @click="searchIpMonitorRelations" size="small" round>查询</el-button>
                <el-button type="success" @click="showBindMonitorDialog" size="small" icon="el-icon-plus" round>绑定监控项</el-button>
              </div>
            </div>

            <el-table :data="ipMonitorRelations" class="custom-table">
              <el-table-column label="监控项名称" prop="name"></el-table-column>
              <el-table-column label="描述" prop="description"></el-table-column>
              <el-table-column label="数据类型" prop="data_type"></el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="danger" size="mini" plain round @click="unbindMonitor(scope.row)">解绑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-main>
      </el-container>
    </el-container>

    <!-- 添加IP弹窗 -->
    <el-dialog title="添加监控IP" :visible.sync="addIpDialogVisible" width="30%" custom-class="custom-dialog">
      <el-form :model="newIp" label-width="80px" :rules="addIpRules" ref="addIpForm">
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="newIp.ip" placeholder="请输入IP地址"></el-input>
        </el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input v-model="newIp.username" placeholder="请输入登录账号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="newIp.password" type="password" placeholder="请输入登录密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addIpDialogVisible = false" round>取消</el-button>
        <el-button type="primary" @click="submitAddIp" round>提交</el-button>
      </span>
    </el-dialog>

    <!-- 添加监控项弹窗 -->
    <el-dialog title="添加监控项" :visible.sync="addMonitorItemDialogVisible" width="50%" custom-class="custom-dialog">
      <el-form :model="newMonitorItem" label-width="100px" :rules="addMonitorItemRules" ref="addMonitorItemForm">
        <el-form-item label="名称" prop="name">
          <el-input v-model="newMonitorItem.name" placeholder="请输入监控项名称"></el-input>
        </el-form-item>
        <el-form-item label="命令" prop="command">
          <el-input
            type="textarea"
            :rows="4"
            v-model="newMonitorItem.command"
            placeholder="请输入执行的命令"
          ></el-input>
          <div class="command-hint">注意: 特殊字符(如 " ' |)会被自动转义</div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="newMonitorItem.description" placeholder="请输入监控项描述"></el-input>
        </el-form-item>
        <el-form-item label="数据类型" prop="data_type">
          <el-select v-model="newMonitorItem.data_type" placeholder="请选择数据类型">
            <el-option label="字符串" value="string"></el-option>
            <el-option label="数值" value="number"></el-option>
            <el-option label="布尔值" value="boolean"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addMonitorItemDialogVisible = false" round>取消</el-button>
        <el-button type="primary" @click="submitAddMonitorItem" round>提交</el-button>
      </span>
    </el-dialog>

    <!-- 绑定监控项弹窗 -->
    <el-dialog title="绑定监控项" :visible.sync="bindMonitorDialogVisible" width="60%" custom-class="custom-dialog bind-monitor-dialog">
      <div v-if="!selectedIp" class="no-ip-warning">请先在列表中选择一个IP</div>
      <div v-else>
        <div class="selected-ip-info">
          <span>已选择IP: </span>
          <el-tag type="primary" effect="light">{{ selectedIp }}</el-tag>
        </div>

        <el-transfer
          v-model="selectedMonitorItems"
          :data="allMonitorItems"
          :titles="['可用监控项', '已选监控项']"
          :button-texts="['移除', '添加']"
          :props="{
            key: 'id',
            label: 'name'
          }"
          class="custom-transfer"
          filterable
        ></el-transfer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="bindMonitorDialogVisible = false" round>取消</el-button>
        <el-button type="primary" @click="submitBindMonitors" round>提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import IPManagement from '@/components/ip/IPManagement.vue'

export default {
  name: 'AdminPage',
  components: {
    IPManagement
  },
  data () {
    return {
      activeIndex: '1',

      // IP列表数据
      ipList: [],
      ipListLoading: false,
      currentPage: 1,
      pageSize: 10,
      totalIps: 0,
      addIpDialogVisible: false,
      newIp: {
        ip: '',
        username: '',
        password: ''
      },
      addIpRules: {
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP格式不正确', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入登录账号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入登录密码', trigger: 'blur' }
        ]
      },

      // 监控项数据
      searchIp: '',
      monitorItems: [],
      addMonitorItemDialogVisible: false,
      newMonitorItem: {
        name: '',
        command: '',
        description: '',
        data_type: 'string'
      },
      addMonitorItemRules: {
        name: [
          { required: true, message: '请输入监控项名称', trigger: 'blur' }
        ],
        command: [
          { required: true, message: '请输入执行命令', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ],
        data_type: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ]
      },

      // IP关联监控项数据
      selectedIp: '',
      ipMonitorRelations: [],
      bindMonitorDialogVisible: false,
      allMonitorItems: [],
      selectedMonitorItems: []
    }
  },
  created () {
    console.log('Admin 组件已创建')

    // 监听菜单点击
    this.$nextTick(() => {
      console.log('Admin 组件已挂载到 DOM')
      const menuItems = document.querySelectorAll('.el-menu-item')
      console.log('找到菜单项:', menuItems.length)
      menuItems.forEach((item, index) => {
        item.addEventListener('click', () => {
          console.log('点击菜单项:', index + 1)
          this.activeIndex = (index + 1).toString()
        })
      })
    })

    // 初始化IP列表数据
    this.fetchIpList()

    // 初始化其他模拟数据
    this.mockOtherData()
  },
  mounted () {
    console.log('Admin 组件已挂载完成')
    // 强制初始化一次菜单点击事件
    setTimeout(() => {
      this.activeIndex = '1'
      console.log('强制设置 activeIndex =', this.activeIndex)
    }, 500)
  },
  methods: {
    // 从API获取IP列表数据
    fetchIpList () {
      this.ipListLoading = true

      // API请求参数，根据API文档正确设置
      const params = {
        page: this.currentPage,
        page_size: this.pageSize,
        is_deleted: 1,
        is_connectable: null // 不筛选连接状态，获取所有IP
      }

      console.log('请求第', this.currentPage, '页数据，参数:', params)

      // 发起API请求
      axios.post('http://*************:8000/ip/user-list', params)
        .then(response => {
          // API返回格式为: { data: [...], total: 4, page: 1, page_size: 10, pages: 1 }
          const responseData = response.data
          console.log('API返回数据:', responseData)

          if (responseData && typeof responseData === 'object') {
            // 从返回的对象中获取数据和分页信息
            const data = responseData.data || []
            this.totalIps = responseData.total || 0

            // 处理返回的数据
            this.ipList = data.map(item => ({
              id: item.id,
              ip: item.ip,
              username: item.username || item.user || '',
              status: item.is_connectable === 1 ? '在线' : '离线'
            }))

            console.log('处理后的IP列表数据:', this.ipList)
          } else {
            console.error('API返回格式异常:', responseData)
            this.$message.error('数据格式异常')
            this.ipList = []
            this.totalIps = 0
          }
        })
        .catch(error => {
          console.error('获取IP列表失败:', error)
          this.$message.error('获取IP列表失败')
          this.ipList = [] // 出错时清空数据
          this.totalIps = 0
        })
        .finally(() => {
          this.ipListLoading = false
        })
    },

    // 处理分页变化
    handleCurrentChange (page) {
      console.log('页码变更为:', page)
      // 确保先更新当前页码，再获取数据
      this.currentPage = page
      // 重新获取当前页的数据
      this.$nextTick(() => {
        this.fetchIpList()
      })
    },

    // 模拟其他数据（监控项等）
    mockOtherData () {
      console.log('生成模拟数据')

      // 模拟监控项列表
      this.allMonitorItems = [
        { id: 1, name: '系统运行时间', command: 'uptime | awk -F\'( |,|:)+\' \'{if ($7=="min") print $6" 分钟"; else if ($7~"[0-9]") print $6" 天 "$7" 小时 "$8" 分钟"; else print $6" 小时 "$7" 分钟"; }\'', description: '系统运行时间', data_type: 'string' },
        { id: 2, name: 'CPU使用率', command: 'top -bn1 | grep "Cpu(s)" | awk \'{print $2 + $4}\'', description: 'CPU使用百分比', data_type: 'number' },
        { id: 3, name: '内存使用率', command: 'free -m | awk \'NR==2{printf "%.2f", $3*100/$2}\'', description: '内存使用百分比', data_type: 'number' }
      ]

      // 初始化监控项
      this.monitorItems = [...this.allMonitorItems]
    },

    // IP管理相关方法
    showAddIpDialog () {
      this.addIpDialogVisible = true
      this.newIp = { ip: '', username: '', password: '' }
    },

    submitAddIp () {
      this.$refs.addIpForm.validate(valid => {
        if (valid) {
          // 显示加载状态
          this.$message({
            message: '正在添加IP，请稍候...',
            type: 'info',
            duration: 2000
          })

          // 构建API请求参数
          const params = {
            ip: this.newIp.ip,
            user: this.newIp.username, // 注意：API使用user而不是username
            password: this.newIp.password
          }

          // 调用添加IP的API
          axios.post('http://*************:8000/ip/add', params)
            .then(response => {
              console.log('添加IP成功:', response.data)
              this.$message.success('IP添加成功')
              this.addIpDialogVisible = false

              // 重新获取IP列表
              this.fetchIpList()
            })
            .catch(error => {
              console.error('添加IP失败:', error)
              this.$message.error('添加IP失败: ' + (error.response?.data?.message || error.message || '未知错误'))
            })
        } else {
          this.$message.error('请正确填写表单')
          return false
        }
      })
    },

    viewDetail (row) {
      // 查看详情功能，目前只是占位
      console.log('查看详情', row)
    },

    deleteIp (row) {
      // 删除IP功能
      this.$confirm('确认删除该IP记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用删除IP的API
        // axios.post('http://*************:8000/ip/delete', { id: row.id })
        //   .then(() => {
        //     this.$message.success('删除成功')
        //     this.fetchIpList()
        //   })
        //   .catch(error => {
        //     this.$message.error('删除失败: ' + error.message)
        //   })

        // 示例代码
        this.$message.success('删除成功')

        // 重新获取IP列表
        this.fetchIpList()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 监控项管理相关方法
    showAddMonitorItemDialog () {
      this.addMonitorItemDialogVisible = true
      this.newMonitorItem = { name: '', command: '', description: '', data_type: 'string' }
    },
    submitAddMonitorItem () {
      this.$refs.addMonitorItemForm.validate(valid => {
        if (valid) {
          // 模拟提交成功
          this.$message.success('监控项添加成功')
          const newId = this.allMonitorItems.length + 1
          const newItem = {
            id: newId,
            name: this.newMonitorItem.name,
            command: this.newMonitorItem.command,
            description: this.newMonitorItem.description,
            data_type: this.newMonitorItem.data_type
          }
          this.allMonitorItems.push(newItem)
          this.monitorItems.push(newItem)
          this.addMonitorItemDialogVisible = false
        } else {
          this.$message.error('请正确填写表单')
          return false
        }
      })
    },
    editMonitorItem (row) {
      // 编辑监控项功能，目前只是占位
      console.log('编辑监控项', row)
    },
    deleteMonitorItem (row) {
      // 删除监控项功能，目前只是占位
      console.log('删除监控项', row)
    },
    searchMonitorItems () {
      if (this.searchIp) {
        this.monitorItems = this.allMonitorItems.filter(item =>
          Math.random() > 0.5 // 模拟查询结果，实际应该根据IP过滤
        )
        this.$message.success(`已查询到${this.monitorItems.length}条监控项`)
      } else {
        this.monitorItems = [...this.allMonitorItems]
      }
    },

    // IP关联监控项相关方法
    searchIpMonitorRelations () {
      if (this.selectedIp) {
        // 模拟查询结果
        this.ipMonitorRelations = this.allMonitorItems.filter(() =>
          Math.random() > 0.5 // 模拟查询结果，实际应该根据IP过滤
        )
        this.$message.success(`已查询到${this.ipMonitorRelations.length}条关联监控项`)
      } else {
        this.$message.warning('请先选择一个IP')
      }
    },
    showBindMonitorDialog () {
      this.bindMonitorDialogVisible = true
      this.selectedMonitorItems = [] // 清空已选
    },
    submitBindMonitors () {
      if (!this.selectedIp) {
        this.$message.warning('请先选择一个IP')
        return
      }

      if (this.selectedMonitorItems.length === 0) {
        this.$message.warning('请选择至少一个监控项')
        return
      }

      // 模拟绑定成功
      this.$message.success(`成功将${this.selectedMonitorItems.length}个监控项绑定到IP: ${this.selectedIp}`)
      this.bindMonitorDialogVisible = false

      // 更新关联表格
      this.searchIpMonitorRelations()
    },
    unbindMonitor (row) {
      // 解绑监控项功能，目前只是占位
      console.log('解绑监控项', row)
    }
  }
}
</script>

<style scoped>
.admin-page {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
}

.admin-container {
  height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
  background-color: #304156;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100vh;
  overflow: hidden;
  transition: width 0.3s;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #263445;
}

.logo-text {
  color: #ffffff;
  font-size: 18px;
  margin: 0;
  padding: 0;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
  background-color: #304156;
}

.el-menu-item {
  color: #bfcbd9;
  height: 56px;
  line-height: 56px;
}

.el-menu-item:hover, .el-menu-item:focus {
  background-color: #263445;
}

.el-menu-item.is-active {
  color: #ffffff;
  background-color: #1890ff !important;
}

/* 头部样式 */
.header {
  background-color: #ffffff;
  color: #333;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  height: 60px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

/* 主内容区域 */
.main-content {
  padding: 20px;
  background-color: #f5f7fa;
  overflow: auto;
  height: calc(100vh - 60px);
}

/* 自定义滚动条 */
.main-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 菜单提示区 */
.current-menu-hint {
  background-color: #e6f7ff;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #91d5ff;
  color: #1890ff;
  font-size: 14px;
}

/* 内容区段 */
.content-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
  font-size: 18px;
  color: #303133;
  margin: 0;
  font-weight: 500;
}

/* 表格样式 */
.custom-table {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.custom-table >>> .el-table__header-wrapper th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.custom-table >>> td {
  padding: 12px 0;
}

.custom-table >>> .el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 表单元素 */
.action-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 240px;
  border-radius: 20px;
}

.select-input {
  width: 240px;
}

/* 弹窗样式 */
.custom-dialog >>> .el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.custom-dialog >>> .el-dialog__header {
  background-color: #f5f7fa;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.custom-dialog >>> .el-dialog__title {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.custom-dialog >>> .el-dialog__body {
  padding: 24px 20px;
}

.command-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.no-ip-warning {
  color: #E6A23C;
  text-align: center;
  margin: 20px 0;
  font-size: 14px;
  padding: 10px;
  background-color: #fdf6ec;
  border-radius: 4px;
}

.selected-ip-info {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 穿梭框样式 */
.custom-transfer {
  margin: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.custom-transfer >>> .el-transfer-panel {
  border-radius: 8px;
  overflow: hidden;
  border-color: #ebeef5;
  width: 48%;
  min-height: 350px;
}

.custom-transfer >>> .el-transfer-panel__header {
  background-color: #f5f7fa;
  height: 40px;
  line-height: 40px;
}

.custom-transfer >>> .el-transfer-panel__header .el-checkbox__label {
  font-size: 13px;
}

.custom-transfer >>> .el-transfer__buttons {
  padding: 0 20px;
}

.custom-transfer >>> .el-transfer__button {
  border-radius: 20px;
  padding: 10px 15px;
  margin: 5px 0;
}

.custom-transfer >>> .el-transfer-panel__item {
  font-size: 13px;
  height: 32px;
  line-height: 32px;
  padding-left: 10px;
}

.custom-transfer >>> .el-transfer-panel__filter {
  margin: 10px;
}

.custom-transfer >>> .el-transfer-panel__filter .el-input__inner {
  height: 32px;
  font-size: 13px;
}

.custom-transfer >>> .el-transfer-panel__body {
  height: 270px;
}

.bind-monitor-dialog >>> .el-dialog__body {
  padding: 15px 20px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 调试信息样式 */
.debug-info {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 15px;
  font-size: 12px;
  color: #67c23a;
}
</style>
