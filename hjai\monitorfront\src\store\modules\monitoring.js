// 导入时区工具函数
import { getChineseTime, formatDateTime } from '@/utils/timezone'
// 导入GPU统计API
import { getGpuStats, getGpuCount } from '@/api/monitoring/gpu'

// 生成过去24小时的模拟数据
function generateMockData (baseValue, fluctuation, startTime, endTime) {
  // 如果没有提供时间范围，默认使用过去24小时
  if (!startTime || !endTime) {
    const now = getChineseTime() // 使用中国标准时间
    const data = []

    for (let i = 23; i >= 0; i--) {
      const time = new Date(now)
      time.setHours(now.getHours() - i)

      // 生成在baseValue附近波动的随机值
      const value = Math.max(0, Math.min(100, baseValue + ((Math.random() * 2 - 1) * fluctuation)))

      data.push({
        time: formatDateTime(time, 'yyyy-MM-dd HH:mm'), // 使用格式化函数
        value: Number(value.toFixed(2))
      })
    }

    return data
  } else {
    // 使用提供的时间范围生成数据
    const start = new Date(startTime)
    const end = new Date(endTime)
    const duration = end.getTime() - start.getTime()
    // 计算时间步长，确保最多生成24个数据点
    const hourStep = Math.max(1, Math.floor(duration / (24 * 3600000)))
    const data = []

    // 生成固定数量的数据点（最多24个）
    const numPoints = Math.min(24, Math.ceil(duration / (hourStep * 3600000)))

    for (let i = 0; i < numPoints; i++) {
      const pointTime = new Date(start.getTime())
      pointTime.setHours(pointTime.getHours() + i * hourStep)

      if (pointTime > end) break

      // 生成在baseValue附近波动的随机值
      const value = Math.max(0, Math.min(100, baseValue + ((Math.random() * 2 - 1) * fluctuation)))

      data.push({
        time: formatDateTime(pointTime, 'yyyy-MM-dd HH:mm'), // 使用格式化函数
        value: Number(value.toFixed(2))
      })
    }

    return data
  }
}

// 初始状态
const state = {
  gpuUsage: generateMockData(60, 20), // GPU使用率基准60%，波动±20%
  memory: generateMockData(70, 15), // 内存使用率基准70%，波动±15%
  cpu: generateMockData(50, 25), // CPU使用率基准50%，波动±25%
  network: generateMockData(40, 30), // 网络使用率基准40%，波动±30%
  timeRange: '24h', // 默认显示24小时数据
  lastUpdated: getChineseTime().toISOString(), // 最后更新时间，使用中国标准时间
  startTime: (() => {
    const date = getChineseTime() // 使用中国标准时间
    date.setHours(date.getHours() - 24)
    return date.toISOString()
  })(), // 默认开始时间：24小时前
  endTime: getChineseTime().toISOString(), // 默认结束时间：当前时间，使用中国标准时间
  loading: false, // 加载状态
  error: null, // 错误信息
  gpuList: {}, // GPU列表，格式: { "0": "gpu0", "1": "gpu1", ... }
  selectedGpuId: 'all' // 当前选择的GPU ID，默认为"all"（所有）
}

// getter函数
const getters = {
  gpuUsageData: state => state.gpuUsage,
  memoryData: state => state.memory,
  cpuData: state => state.cpu,
  networkData: state => state.network,
  timeRange: state => state.timeRange,
  lastUpdated: state => state.lastUpdated,
  startTime: state => state.startTime,
  endTime: state => state.endTime,
  loading: state => state.loading,
  error: state => state.error,
  gpuList: state => state.gpuList,
  selectedGpuId: state => state.selectedGpuId,
  // 将GPU列表转换为下拉菜单选项
  gpuOptions: state => {
    const options = [
      { label: '全部GPU', value: 'all' }
    ]

    // 添加各个GPU选项
    Object.entries(state.gpuList).forEach(([id, name]) => {
      options.push({
        label: name,
        value: id
      })
    })

    return options
  }
}

// mutations
const mutations = {
  UPDATE_GPU_USAGE (state, data) {
    state.gpuUsage = data
  },
  UPDATE_MEMORY (state, data) {
    state.memory = data
  },
  UPDATE_CPU (state, data) {
    state.cpu = data
  },
  UPDATE_NETWORK (state, data) {
    state.network = data
  },
  SET_TIME_RANGE (state, range) {
    state.timeRange = range
  },
  UPDATE_TIMESTAMP (state) {
    state.lastUpdated = getChineseTime().toISOString() // 使用中国标准时间
  },
  SET_START_TIME (state, time) {
    state.startTime = time
  },
  SET_END_TIME (state, time) {
    state.endTime = time
  },
  SET_LOADING (state, status) {
    state.loading = status
  },
  SET_ERROR (state, error) {
    state.error = error
  },
  SET_GPU_LIST (state, gpuList) {
    state.gpuList = gpuList
  },
  SET_SELECTED_GPU_ID (state, gpuId) {
    state.selectedGpuId = gpuId
  }
}

// 转换后端GPU数据格式为前端图表格式
function transformGpuData (data, selectedGpuId = 'all') {
  // 如果选择的是特定GPU，过滤数据
  // 注意：即使API已经传递了gpu_index参数，这里保留前端过滤以兼容旧版API
  if (selectedGpuId !== 'all' && Array.isArray(data)) {
    // 从数据中筛选出对应GPU ID的数据点
    data = data.filter(item => {
      // 优先使用gpu_index字段，兼容同时使用gpu_id的旧数据
      const itemGpuId = item.gpu_index !== undefined ? item.gpu_index : item.gpu_id
      return itemGpuId?.toString() === selectedGpuId
    })
  }

  // 数据清洗和转换
  return data.map(item => {
    // 创建日期对象，自动处理时区
    const timestamp = new Date(item.timestamp)

    // 数据清洗：确保avg_usage是有效的数字，并且在合理范围内
    let usage = 0
    if (item.avg_usage !== undefined && item.avg_usage !== null) {
      usage = Number(parseFloat(item.avg_usage).toFixed(4))
      // 根据API响应格式，avg_usage值已经是百分比值（1表示1%）
      // 不需要额外转换，直接使用原始值
      // 限制在0-100范围内
      usage = Math.max(0, Math.min(100, usage))
    }

    return {
      // 直接格式化日期，保持本地时区显示
      time: formatDateTime(timestamp, 'yyyy-MM-dd HH:mm'),
      // 使用清洗后的值
      value: usage
    }
  })
}

// actions
const actions = {
  // 获取GPU列表
  async fetchGpuList ({ commit, rootGetters }) {
    const currentIp = rootGetters['ip/currentIp']
    if (!currentIp) {
      commit('SET_ERROR', '请先选择IP地址')
      return
    }

    commit('SET_LOADING', true)
    commit('SET_ERROR', null)

    try {
      const response = await getGpuCount({ ip: currentIp })
      if (response && response.gpu_counts) {
        commit('SET_GPU_LIST', response.gpu_counts)
      } else {
        commit('SET_GPU_LIST', {})
      }
    } catch (error) {
      console.error('获取GPU列表失败:', error)
      // 检查是否有响应数据
      if (error.response && error.response.data) {
        const errorData = error.response.data
        // 处理后端特定的错误格式
        if (errorData.code && errorData.message) {
          commit('SET_ERROR', `错误 ${errorData.code}: ${errorData.message}`)
        } else {
          commit('SET_ERROR', error.message || '获取GPU列表失败')
        }
      } else {
        commit('SET_ERROR', error.message || '获取GPU列表失败')
      }
      commit('SET_GPU_LIST', {})
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 设置选中的GPU ID
  setSelectedGpuId ({ commit, dispatch }, gpuId) {
    commit('SET_SELECTED_GPU_ID', gpuId)
    dispatch('refreshMonitoringData')
  },

  // 刷新所有监控数据
  async refreshMonitoringData ({ commit, state, rootGetters }) {
    const currentIp = rootGetters['ip/currentIp']
    if (!currentIp) {
      commit('SET_ERROR', '请先选择IP地址')
      return
    }

    if (!state.startTime || !state.endTime) {
      commit('SET_ERROR', '请选择时间范围')
      return
    }

    commit('SET_LOADING', true)
    commit('SET_ERROR', null)

    try {
      // 获取真实的GPU统计数据
      const gpuStatsResponse = await getGpuStats({
        ip: currentIp,
        start_time: state.startTime,
        end_time: state.endTime,
        gpu_index: state.selectedGpuId
      })

      // 更新GPU使用数据
      const gpuUsageData = transformGpuData(gpuStatsResponse, state.selectedGpuId)
      commit('UPDATE_GPU_USAGE', gpuUsageData)

      // 暂时保留其他图表的模拟数据
      commit('UPDATE_MEMORY', generateMockData(70, 15, state.startTime, state.endTime))
      commit('UPDATE_CPU', generateMockData(50, 25, state.startTime, state.endTime))
      commit('UPDATE_NETWORK', generateMockData(40, 30, state.startTime, state.endTime))

      commit('UPDATE_TIMESTAMP')
    } catch (error) {
      console.error('获取GPU统计数据失败:', error)
      // 检查是否有响应数据
      if (error.response && error.response.data) {
        const errorData = error.response.data
        // 处理后端特定的错误格式
        if (errorData.code && errorData.message) {
          commit('SET_ERROR', `错误 ${errorData.code}: ${errorData.message}`)
        } else {
          commit('SET_ERROR', error.message || '获取数据失败')
        }
      } else {
        commit('SET_ERROR', error.message || '获取数据失败')
      }
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 更改时间范围
  changeTimeRange ({ commit }, range) {
    commit('SET_TIME_RANGE', range)
    // 在实际应用中，这里会根据新的时间范围获取对应数据
  },

  // 设置开始和结束时间
  setTimeRange ({ commit, dispatch }, { startTime, endTime }) {
    commit('SET_START_TIME', startTime)
    commit('SET_END_TIME', endTime)
    dispatch('refreshMonitoringData')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
