<template>
  <div class="monitoring-charts">
    <div class="charts-header">
      <h3>系统监控面板</h3>
      <div class="actions">
        <el-button
          size="small"
          type="primary"
          icon="el-icon-refresh"
          :loading="isLoading"
          @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 显示错误信息 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      show-icon
      :closable="true"
      @close="clearError"
      style="margin-bottom: 20px;">
    </el-alert>

    <el-row :gutter="20">
      <el-col :span="12">
        <div class="chart-wrapper" v-loading="isLoading">
          <div class="chart-header">
            <span class="chart-title">GPU使用率</span>
            <el-select
              v-model="selectedGpu"
              size="small"
              placeholder="选择GPU"
              @change="handleGpuChange"
              :disabled="isLoading">
              <el-option
                v-for="option in gpuOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value">
              </el-option>
            </el-select>
          </div>
          <line-chart
            :chart-data="gpuData"
            chart-title=""
            y-axis-name="%"
            :y-axis-min="0"
            :y-axis-max="null"
            chart-color="#67C23A" />
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-wrapper">
          <line-chart
            :chart-data="memoryData"
            chart-title="内存使用率"
            y-axis-name="%"
            :show-mock-data="true"
            chart-color="#409EFF" />
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <div class="chart-wrapper">
          <line-chart
            :chart-data="cpuData"
            chart-title="CPU使用率"
            y-axis-name="%"
            :show-mock-data="true"
            chart-color="#F56C6C" />
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-wrapper">
          <line-chart
            :chart-data="networkData"
            chart-title="网络使用率"
            y-axis-name="%"
            :show-mock-data="true"
            chart-color="#E6A23C" />
        </div>
      </el-col>
    </el-row>

    <div class="last-updated">
      最后更新时间：{{ formatDateTime(lastUpdated) }}
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex'
import LineChart from './LineChart.vue'

export default {
  name: 'MonitoringCharts',
  components: {
    LineChart
  },
  data () {
    return {
      refreshInterval: null,
      selectedGpu: 'all' // 默认选择"全部"
    }
  },
  computed: {
    ...mapGetters({
      gpuData: 'monitoring/gpuUsageData',
      memoryData: 'monitoring/memoryData',
      cpuData: 'monitoring/cpuData',
      networkData: 'monitoring/networkData',
      startTime: 'monitoring/startTime',
      endTime: 'monitoring/endTime',
      lastUpdated: 'monitoring/lastUpdated',
      isLoading: 'monitoring/loading',
      errorMessage: 'monitoring/error',
      gpuOptions: 'monitoring/gpuOptions',
      currentSelectedGpuId: 'monitoring/selectedGpuId'
    })
  },
  watch: {
    // 当store中的选中GPU ID变化时，更新本地状态
    currentSelectedGpuId: {
      handler (newVal) {
        this.selectedGpu = newVal
      },
      immediate: true
    }
  },
  created () {
    // 获取GPU列表
    this.fetchGpuList()

    // 每分钟自动刷新一次数据
    this.startAutoRefresh()
  },
  beforeDestroy () {
    // 清理定时器
    this.stopAutoRefresh()
  },
  methods: {
    ...mapActions({
      refreshMonitoringData: 'monitoring/refreshMonitoringData',
      fetchGpuList: 'monitoring/fetchGpuList',
      setSelectedGpuId: 'monitoring/setSelectedGpuId'
    }),
    ...mapMutations({
      clearErrorMsg: 'monitoring/SET_ERROR'
    }),

    // 处理GPU选择变更
    handleGpuChange (gpuId) {
      this.setSelectedGpuId(gpuId)
    },

    // 刷新数据
    async refreshData () {
      try {
        await this.refreshMonitoringData()
      } catch (error) {
        console.error('刷新监控数据失败:', error)
      }
    },

    // 清除错误信息
    clearError () {
      this.clearErrorMsg(null)
    },

    // 格式化日期时间
    formatDateTime (dateTimeStr) {
      if (!dateTimeStr) return '未知'

      const date = new Date(dateTimeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // 启动自动刷新
    startAutoRefresh () {
      this.refreshInterval = setInterval(() => {
        this.refreshData()
      }, 60000) // 每分钟刷新一次
    },

    // 停止自动刷新
    stopAutoRefresh () {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    }
  }
}
</script>

<style scoped>
.monitoring-charts {
  padding: 10px;
}

.charts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.charts-header h3 {
  margin: 0;
  font-size: 18px;
}

.actions {
  display: flex;
  align-items: center;
}

.chart-wrapper {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  height: 350px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-weight: bold;
  font-size: 16px;
}

.last-updated {
  text-align: right;
  color: #909399;
  font-size: 12px;
  margin-top: 10px;
}
</style>
