<template>
  <div class="ip-management">
    <div class="ip-management-header">
      <h2>IP监控管理</h2>
      <div class="filter-actions">
        <el-checkbox v-model="filterDeleted" @change="handleFilterChange">显示已删除</el-checkbox>
        <el-checkbox v-model="filterConnectable" @change="handleFilterChange">仅显示可连通</el-checkbox>
        <el-button type="primary" icon="el-icon-plus" @click="showAddIpDialog">添加IP</el-button>
      </div>
    </div>

    <el-table
      :data="ipUserList"
      v-loading="loading"
      border
      stripe
      style="width: 100%">
      <el-table-column prop="ip" label="IP地址" min-width="140"></el-table-column>
      <el-table-column prop="username" label="账号" min-width="120"></el-table-column>
      <el-table-column label="状态" width="120">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.is_connectable ? 'success' : 'danger'"
            size="small">
            {{ scope.row.is_connectable ? '可连通' : '不可连通' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="SSH密钥" width="120">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.use_ssh_key ? 'primary' : 'info'"
            size="small">
            {{ scope.row.use_ssh_key ? '使用密钥' : '密码认证' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="删除状态" width="120">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.is_deleted ? 'danger' : 'success'"
            size="small">
            {{ scope.row.is_deleted ? '已删除' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)">编辑</el-button>
          <el-button
            size="mini"
            :type="scope.row.is_deleted ? 'success' : 'danger'"
            @click="handleDelete(scope.row)">
            {{ scope.row.is_deleted ? '恢复' : '删除' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 添加/编辑IP对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="ipForm" :rules="ipFormRules" ref="ipForm" label-width="100px">
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="ipForm.ip" placeholder="请输入IP地址"></el-input>
        </el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input v-model="ipForm.username" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="认证方式" prop="use_ssh_key">
          <el-radio-group v-model="ipForm.use_ssh_key">
            <el-radio :label="false">密码认证</el-radio>
            <el-radio :label="true">SSH密钥</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!ipForm.use_ssh_key">
          <el-input v-model="ipForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="SSH密钥路径" prop="ssh_key_path" v-if="ipForm.use_ssh_key">
          <el-input v-model="ipForm.ssh_key_path" placeholder="请输入SSH密钥路径"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'IPManagement',
  data () {
    return {
      filterDeleted: false,
      filterConnectable: true,
      dialogVisible: false,
      dialogTitle: '添加IP',
      isEdit: false,
      ipForm: {
        id: null,
        ip: '',
        username: '',
        password: '',
        use_ssh_key: false,
        ssh_key_path: ''
      },
      ipFormRules: {
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        ssh_key_path: [
          { required: true, message: '请输入SSH密钥路径', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters({
      ipUserList: 'ip/ipUserList',
      pagination: 'ip/pagination',
      loading: 'ip/loading'
    })
  },
  created () {
    // 组件创建时获取IP列表
    this.fetchIpUserList()
  },
  methods: {
    ...mapActions({
      fetchIpUserList: 'ip/fetchIpUserList',
      setPage: 'ip/setPage',
      setFilterOptions: 'ip/setFilterOptions',
      addIpAction: 'ip/addIp',
      deleteIpAction: 'ip/deleteIp'
    }),

    // 处理过滤条件变化
    handleFilterChange () {
      this.setFilterOptions({
        isDeleted: this.filterDeleted,
        isConnectable: this.filterConnectable
      })
    },

    // 处理页码变化
    handleCurrentChange (page) {
      this.setPage(page)
    },

    // 处理每页条数变化
    handleSizeChange (pageSize) {
      this.setFilterOptions({
        pageSize
      })
    },

    // 显示添加IP对话框
    showAddIpDialog () {
      this.dialogTitle = '添加IP'
      this.isEdit = false
      this.resetForm()
      this.dialogVisible = true
    },

    // 处理编辑
    handleEdit (row) {
      this.dialogTitle = '编辑IP'
      this.isEdit = true
      this.ipForm = {
        id: row.id,
        ip: row.ip,
        username: row.username,
        password: '',
        use_ssh_key: row.use_ssh_key,
        ssh_key_path: row.ssh_key_path || ''
      }
      this.dialogVisible = true
    },

    // 处理删除/恢复
    handleDelete (row) {
      const action = row.is_deleted ? '恢复' : '删除'
      this.$confirm(`确认${action}该IP地址?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteIpAction(row.id).then(() => {
          this.$message({
            type: 'success',
            message: `${action}成功!`
          })
        }).catch(error => {
          this.$message.error(`${action}失败: ${error.message}`)
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${action}`
        })
      })
    },

    // 提交表单
    submitForm () {
      this.$refs.ipForm.validate((valid) => {
        if (valid) {
          // 表单验证通过
          const ipData = { ...this.ipForm }

          // 根据认证方式处理数据
          if (ipData.use_ssh_key) {
            delete ipData.password
          } else {
            delete ipData.ssh_key_path
          }

          this.addIpAction(ipData).then((response) => {
            // 检查响应状态码
            if (response && response.code === 200) {
              this.$message({
                type: 'success',
                message: response.message || (this.isEdit ? 'IP更新成功!' : 'IP添加成功!')
              })
              this.dialogVisible = false
            } else {
              // 如果状态码不是200，显示错误信息
              this.$message.error(response?.message || '操作失败')
            }
          }).catch(error => {
            this.$message.error(`操作失败: ${error.message || '未知错误'}`)
          })
        } else {
          return false
        }
      })
    },

    // 重置表单
    resetForm () {
      this.ipForm = {
        id: null,
        ip: '',
        username: '',
        password: '',
        use_ssh_key: false,
        ssh_key_path: ''
      }

      // 如果表单已经被渲染，则重置验证状态
      if (this.$refs.ipForm) {
        this.$refs.ipForm.resetFields()
      }
    }
  }
}
</script>

<style scoped lang="less">
.ip-management {
  padding: 20px;

  .ip-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .filter-actions {
      display: flex;
      align-items: center;

      .el-checkbox {
        margin-right: 20px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

// 深色模式适配
body.dark-theme {
  .ip-management {
    background-color: #1a1a1a;

    .ip-management-header h2 {
      color: #e0e0e0;
    }
  }
}
</style>
