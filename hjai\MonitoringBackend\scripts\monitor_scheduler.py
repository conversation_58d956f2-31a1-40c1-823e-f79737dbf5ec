import asyncio
import logging
from typing import Dict, Any, List, Optional
import time
from datetime import datetime
import pytz

from scripts.connectivity_checker import Connect<PERSON><PERSON><PERSON><PERSON>
from scripts.cpu_monitor import CPUMonitor
from scripts.gpu_monitor import GPUMonitor
from scripts.memory_monitor import MemoryMonitor
from scripts.network_monitor import NetworkMonitor
from scripts.custom_monitor_scheduler import get_custom_monitor_scheduler

# 导入新的时区处理工具
from config.timezone_utils import TZ

# 保持向后兼容
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')

# 获取日志记录器
logger = logging.getLogger(__name__)

class MonitorScheduler:
    """监控调度器类，统一管理所有监控任务"""
    
    def __init__(self):
        # 实例化各类监控器
        self.connectivity_checker = ConnectivityChecker()
        self.cpu_monitor = CPUMonitor()
        self.gpu_monitor = GPUMonitor()
        self.memory_monitor = MemoryMonitor()
        self.network_monitor = NetworkMonitor()
        self.custom_monitor_scheduler = get_custom_monitor_scheduler()
        
        # 监控间隔配置(秒)
        self.connectivity_interval = 3600  # 连通性检查，默认1小时
        self.resource_interval = 300  # 资源监控，默认5分钟
        self.custom_monitor_interval = 300  # 自定义监控，默认5分钟

        # 任务状态
        self.running = False
        self.resource_task = None
        self.connectivity_task = None
        self.custom_monitor_task = None
    
    async def run_connectivity_check(self):
        """运行连通性检查循环"""
        logger.info("启动连通性检查循环，检查间隔：{}秒".format(self.connectivity_interval))
        
        while self.running:
            try:
                # 记录当前时间
                now = datetime.now(SHANGHAI_TZ)
                logger.info(f"开始连通性检查 - {now.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 执行连通性检查
                await self.connectivity_checker.check_all_servers()
                
                # 等待下次检查
                logger.info(f"连通性检查完成，{self.connectivity_interval}秒后进行下一次检查")
                await asyncio.sleep(self.connectivity_interval)
                
            except Exception as e:
                logger.error(f"连通性检查过程出错: {str(e)}")
                # 发生错误时等待一定时间后重试
                await asyncio.sleep(60)
    
    async def run_resource_monitors(self):
        """运行资源监控循环(CPU, GPU, 内存, 网络)"""
        logger.info("启动资源监控循环，监控间隔：{}秒".format(self.resource_interval))
        
        while self.running:
            try:
                # 记录当前时间 - 使用新的时区工具
                utc_now = TZ.now_utc()
                shanghai_now = TZ.now_shanghai()
                logger.info(f"开始资源监控周期 - {shanghai_now.strftime('%Y-%m-%d %H:%M:%S')} (上海时区)")
                logger.debug(f"UTC时间: {utc_now.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 优先单独执行网络监控，然后并行执行其他资源监控任务
                # 为网络监控单独创建任务并等待完成
                logger.info("优先执行网络监控...")
                await self.network_monitor.monitor_all_servers(
                    self.network_monitor.get_network_info, 
                    self.network_monitor.save_network_stats, 
                    only_connectable=True
                )
                
                # 并行执行其他资源监控任务，只监控可连通的服务器
                tasks = [
                    self.cpu_monitor.monitor_all_servers(
                        self.cpu_monitor.get_cpu_info, 
                        self.cpu_monitor.save_cpu_stats, 
                        only_connectable=True
                    ),
                    self.memory_monitor.monitor_all_servers(
                        self.memory_monitor.get_memory_info, 
                        self.memory_monitor.save_memory_stats, 
                        only_connectable=True
                    ),
                    self.gpu_monitor.monitor_all_servers(
                        self.gpu_monitor.get_gpu_info, 
                        self.gpu_monitor.save_gpu_stats, 
                        only_connectable=True
                    )
                ]
                
                # 同时执行所有资源监控
                await asyncio.gather(*tasks)
                
                # 等待下次监控
                logger.info(f"资源监控完成，{self.resource_interval}秒后进行下一次监控")
                await asyncio.sleep(self.resource_interval)
                
            except Exception as e:
                logger.error(f"资源监控过程出错: {str(e)}")
                # 发生错误时等待一定时间后重试
                await asyncio.sleep(60)

    async def run_custom_monitors(self):
        """运行自定义监控循环"""
        logger.info("启动自定义监控循环，监控间隔：{}秒".format(self.custom_monitor_interval))

        # 启动自定义监控调度器（只启动一次）
        try:
            if not self.custom_monitor_scheduler.running:
                await self.custom_monitor_scheduler.start(self.custom_monitor_interval)
                logger.info("自定义监控调度器已启动")
        except Exception as e:
            logger.error(f"启动自定义监控调度器失败: {str(e)}")
            return

        # 监控自定义监控调度器的运行状态
        while self.running:
            try:
                # 检查自定义监控调度器是否还在运行
                if not self.custom_monitor_scheduler.running:
                    logger.warning("自定义监控调度器已停止，尝试重新启动")
                    await self.custom_monitor_scheduler.start(self.custom_monitor_interval)
                
                # 等待一段时间后再次检查
                await asyncio.sleep(60)  # 每分钟检查一次状态

            except Exception as e:
                logger.error(f"自定义监控状态检查出错: {str(e)}")
                # 发生错误时等待一定时间后重试
                await asyncio.sleep(60)
    
    async def start(self, connectivity_interval: int = None, resource_interval: int = None, custom_monitor_interval: int = None):
        """
        启动监控调度器

        Args:
            connectivity_interval: 连通性检查间隔(秒)，默认3600秒(1小时)
            resource_interval: 资源监控间隔(秒)，默认300秒(5分钟)
            custom_monitor_interval: 自定义监控间隔(秒)，默认300秒(5分钟)
        """
        if connectivity_interval:
            self.connectivity_interval = connectivity_interval

        if resource_interval:
            self.resource_interval = resource_interval

        if custom_monitor_interval:
            self.custom_monitor_interval = custom_monitor_interval
            
        # 标记为运行状态
        self.running = True
        
        # 记录启动时间
        start_time = datetime.now(SHANGHAI_TZ).strftime('%Y-%m-%d %H:%M:%S')
        logger.info(f"监控调度器启动 - {start_time}")
        
        # 先执行初始化监控，然后再启动周期性监控任务
        try:
            # 首次执行：先进行连通性检查，再进行资源监控
            logger.info("初始化执行：先进行连通性检查")
            try:
                # 为初始化连通性检查添加超时保护
                await asyncio.wait_for(
                    self.connectivity_checker.check_all_servers(),
                    timeout=120  # 初次检查最多允许2分钟
                )
            except asyncio.TimeoutError:
                logger.warning("初始化连通性检查超时，继续后续任务")
            
            logger.info("初始化执行：开始进行资源监控")
            
            # 修改：创建一个自定义的CPU监控日志前缀
            cpu_monitor_with_prefix = CPUMonitor()
            cpu_monitor_with_prefix.monitor_name = "CPU[初始化]"
            memory_monitor_with_prefix = MemoryMonitor()
            memory_monitor_with_prefix.monitor_name = "内存[初始化]"
            gpu_monitor_with_prefix = GPUMonitor()
            gpu_monitor_with_prefix.monitor_name = "GPU[初始化]"
            network_monitor_with_prefix = NetworkMonitor()
            network_monitor_with_prefix.monitor_name = "网卡[初始化]"
            
            # 优先单独执行网络监控初始化，然后并行执行其他资源监控任务
            logger.info("优先执行网络监控初始化...")
            try:
                await asyncio.wait_for(
                    network_monitor_with_prefix.monitor_all_servers(
                        self.network_monitor.get_network_info, 
                        self.network_monitor.save_network_stats, 
                        only_connectable=True
                    ),
                    timeout=90  # 为网络监控单独设置更长的超时时间
                )
                logger.info("网络监控初始化完成")
            except asyncio.TimeoutError:
                logger.warning("网络监控初始化超时")
                
            # 并行执行其他资源监控任务，只监控可连通的服务器
            tasks = [
                cpu_monitor_with_prefix.monitor_all_servers(
                    self.cpu_monitor.get_cpu_info, 
                    self.cpu_monitor.save_cpu_stats, 
                    only_connectable=True
                ),
                memory_monitor_with_prefix.monitor_all_servers(
                    self.memory_monitor.get_memory_info, 
                    self.memory_monitor.save_memory_stats, 
                    only_connectable=True
                ),
                gpu_monitor_with_prefix.monitor_all_servers(
                    self.gpu_monitor.get_gpu_info, 
                    self.gpu_monitor.save_gpu_stats, 
                    only_connectable=True
                )
            ]
            
            # 使用wait_for代替gather，添加全局超时控制
            try:
                await asyncio.wait_for(
                    asyncio.gather(*tasks),
                    timeout=180  # 初次资源监控最多允许3分钟
                )
                logger.info("初始化监控完成")
            except asyncio.TimeoutError:
                logger.warning("初始化资源监控超时")
                
            # 初始化监控完成，添加一个短暂的延迟
            await asyncio.sleep(5)  # 等待5秒，确保数据库操作完成
            logger.info("初始化监控任务已完成，现在启动周期性监控")
            
            # 在初始化完成后，创建连通性、资源监控和自定义监控后台任务
            self.connectivity_task = asyncio.create_task(self.run_connectivity_check())
            # 为第一次周期性监控添加延迟，避免与初始化太接近
            self.resource_task = asyncio.create_task(self._delayed_resource_monitor())
            # 启动自定义监控任务
            self.custom_monitor_task = asyncio.create_task(self._delayed_custom_monitor())
            
            # 不等待任务完成，直接返回
            logger.info("监控调度器已启动，后台任务正在运行")
                
        except Exception as e:
            logger.error(f"初始化监控过程出错: {str(e)}")
            # 记录详细堆栈信息
            import traceback
            logger.error(traceback.format_exc())
            
            # 即使初始化失败，也尝试启动周期性监控
            self.connectivity_task = asyncio.create_task(self.run_connectivity_check())
            self.resource_task = asyncio.create_task(self.run_resource_monitors())
            logger.info("尽管初始化失败，周期性监控已启动")
            
        return
        
    async def _delayed_resource_monitor(self):
        """延迟启动资源监控，确保与初始化监控有足够时间间隔"""
        # 首次运行增加延迟，确保与初始化监控至少间隔resource_interval的一半时间
        initial_delay = max(self.resource_interval / 2, 60)  # 至少延迟60秒
        logger.info(f"周期性资源监控将在 {initial_delay} 秒后首次执行")
        await asyncio.sleep(initial_delay)
        
        # 执行正常的资源监控循环
        await self.run_resource_monitors()

    async def _delayed_custom_monitor(self):
        """延迟启动自定义监控，确保与初始化监控有足够时间间隔"""
        # 首次运行增加延迟，确保与初始化监控至少间隔custom_monitor_interval的一半时间
        initial_delay = max(self.custom_monitor_interval / 2, 90)  # 至少延迟90秒
        logger.info(f"自定义监控将在 {initial_delay} 秒后首次执行")
        await asyncio.sleep(initial_delay)

        # 执行正常的自定义监控循环
        await self.run_custom_monitors()
    
    async def stop(self):
        """停止监控调度器"""
        self.running = False
        
        # 取消任务
        if self.connectivity_task:
            self.connectivity_task.cancel()

        if self.resource_task:
            self.resource_task.cancel()

        if self.custom_monitor_task:
            self.custom_monitor_task.cancel()

        # 停止自定义监控调度器
        if self.custom_monitor_scheduler.running:
            await self.custom_monitor_scheduler.stop()
            
        logger.info("监控调度器已停止")

# 创建全局调度器实例
scheduler = MonitorScheduler()

async def start_monitoring(connectivity_interval: int = 3600, resource_interval: int = 300, custom_monitor_interval: int = 300):
    """
    启动统一监控服务

    Args:
        connectivity_interval: 连通性检查间隔(秒)，默认3600秒(1小时)
        resource_interval: 资源监控间隔(秒)，默认300秒(5分钟)
        custom_monitor_interval: 自定义监控间隔(秒)，默认300秒(5分钟)
    """
    await scheduler.start(connectivity_interval, resource_interval, custom_monitor_interval)