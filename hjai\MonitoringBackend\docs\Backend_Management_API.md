# MonitoringBackend 后台管理API接口文档

## 项目概述

**项目名称**: 监控后端管理API  
**项目描述**: 航锦云监控系统后台管理接口  
**版本**: 1.0.0  
**基础URL**: `http://localhost:8000` (默认)  
**文档地址**: `/docs` (Swagger UI), `/redoc` (ReDoc)

### 技术栈
- **Web框架**: FastAPI
- **数据库ORM**: Tortoise ORM  
- **时区处理**: Asia/Shanghai (UTC+8)
- **认证方式**: SSH密钥认证 / 密码认证

### 核心功能
- IP用户管理（服务器连接信息管理）
- 自定义监控项配置和管理
- 监控项测试和验证
- 系统状态监控和统计
- 批量操作和执行控制

---

## 目录

1. [通用响应格式](#通用响应格式)
2. [错误处理](#错误处理)
3. [API端点](#api端点)
   - [系统状态](#系统状态)
   - [IP用户管理](#ip用户管理)
   - [自定义监控项管理](#自定义监控项管理)
   - [监控项测试验证](#监控项测试验证)
   - [监控状态管理](#监控状态管理)
   - [批量操作](#批量操作)
4. [数据模型](#数据模型)
5. [时区处理](#时区处理)
6. [SSH认证配置](#ssh认证配置)

---

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 分页响应
```json
{
  "data": [],
  "total": 100,
  "page": 1,
  "page_size": 10,
  "pages": 10
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述"
}
```

---

## 错误处理

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 422 | 请求验证失败 |
| 500 | 服务器内部错误 |

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 参数验证失败 |
| 404 | IP地址不存在或已删除 |
| 404 | 监控项不存在 |
| 500 | 系统内部错误 |

---

## API端点

### 系统状态

#### GET /
获取API根信息

**响应示例**:
```json
{
  "message": "Hello World"
}
```

---

### IP用户管理

#### GET /ip/list
获取所有IP地址列表

**描述**: 获取所有未软删除的IP地址列表

**响应示例**:
```json
[
  "*************",
  "*************",
  "*************"
]
```

#### POST /ip/user-list
获取IP用户详细列表（分页）

**请求体**:
```json
{
  "page": 1,
  "page_size": 10,
  "is_deleted": false,
  "is_connectable": true
}
```

**参数说明**:
- `page`: 页码，从1开始（必填）
- `page_size`: 每页记录数，默认10（可选）
- `is_deleted`: 是否删除过滤（可选）
- `is_connectable`: 是否可连通过滤（可选）

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "ip": "*************",
      "username": "admin",
      "is_deleted": false,
      "is_connectable": true,
      "use_ssh_key": false,
      "ssh_key_path": null
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "pages": 1
}
```

#### POST /ip/add
添加新的IP用户

**请求体**:
```json
{
  "ip": "*************",
  "user": "admin",
  "password": "password123",
  "use_ssh_key": false,
  "ssh_key_path": null
}
```

**参数说明**:
- `ip`: IP地址，必须是有效的IPv4格式（必填）
- `user`: 用户名（必填）
- `password`: 密码，为空时使用SSH密钥认证（可选）
- `use_ssh_key`: 是否使用SSH密钥认证（可选，默认false）
- `ssh_key_path`: SSH私钥文件路径（可选）

**响应示例**:
```json
{
  "code": 200,
  "message": "添加成功，认证方式: 密码认证"
}
```

---

### 自定义监控项管理

#### GET /monitor/items
获取所有监控项列表

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "CPU使用率",
    "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
    "description": "获取CPU使用率",
    "data_type": "number",
    "timeout": 30,
    "retry_count": 2,
    "enabled": true,
    "category": "system",
    "security_level": "normal",
    "created_at": "2024-01-01T10:00:00+08:00"
  }
]
```

#### POST /monitor/items
创建新的监控项

**请求体**:
```json
{
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number",
  "timeout": 30,
  "retry_count": 2,
  "category": "system",
  "security_level": "normal"
}
```

**参数说明**:
- `name`: 监控项名称（必填）
- `command`: 监控命令（必填）
- `description`: 描述信息（可选）
- `data_type`: 数据类型，如string、number、json（可选，默认string）
- `timeout`: 超时时间（秒）（可选，默认30）
- `retry_count`: 重试次数（可选，默认2）
- `category`: 监控分类（可选）
- `security_level`: 安全级别（可选，默认normal）

**响应示例**:
```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number",
  "timeout": 30,
  "retry_count": 2,
  "enabled": true,
  "category": "system",
  "security_level": "normal",
  "created_at": "2024-01-01T10:00:00+08:00"
}
```

#### GET /monitor/items/{item_id}
获取单个监控项详情

**路径参数**:
- `item_id`: 监控项ID

**响应示例**:
```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number",
  "timeout": 30,
  "retry_count": 2,
  "enabled": true,
  "category": "system",
  "security_level": "normal",
  "created_at": "2024-01-01T10:00:00+08:00"
}
```

#### PUT /monitor/items/{item_id}
更新监控项

**路径参数**:
- `item_id`: 监控项ID

**请求体**:
```json
{
  "name": "CPU使用率(更新)",
  "description": "获取CPU使用率(更新版本)",
  "timeout": 45,
  "enabled": true
}
```

**参数说明**: 所有字段都是可选的，只更新提供的字段

**响应示例**:
```json
{
  "id": 1,
  "name": "CPU使用率(更新)",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率(更新版本)",
  "data_type": "number",
  "timeout": 45,
  "retry_count": 2,
  "enabled": true,
  "category": "system",
  "security_level": "normal",
  "created_at": "2024-01-01T10:00:00+08:00"
}
```

#### DELETE /monitor/items/{item_id}
删除监控项

**路径参数**:
- `item_id`: 监控项ID

**响应示例**:
```json
{
  "message": "监控项删除成功"
}
```

#### POST /monitor/items/ip
为监控项添加IP关联

**请求体**:
```json
{
  "monitor_item_id": 1,
  "ip_address": "*************"
}
```

**参数说明**:
- `monitor_item_id`: 监控项ID（必填）
- `ip_address`: IP地址（必填）

**响应示例**:
```json
{
  "id": 1,
  "monitor_item_id": 1,
  "monitor_item_name": "CPU使用率",
  "ip_address": "*************",
  "created_at": "2024-01-01T10:00:00+08:00"
}
```

#### GET /monitor/items/ip/{ip_address}
获取指定IP关联的所有监控项

**路径参数**:
- `ip_address`: IP地址

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "CPU使用率",
    "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
    "description": "获取CPU使用率",
    "data_type": "number",
    "timeout": 30,
    "retry_count": 2,
    "enabled": true,
    "category": "system",
    "security_level": "normal",
    "created_at": "2024-01-01T10:00:00+08:00"
  }
]
```

---

### 监控项测试验证

#### POST /monitor/test
测试监控命令

**请求体**:
```json
{
  "command": "uptime | awk '{print $3}' | sed 's/,//'",
  "ip": "*************",
  "username": "admin",
  "password": "password123",
  "use_ssh_key": false,
  "ssh_key_path": null,
  "timeout": 30,
  "data_type": "string"
}
```

**参数说明**:
- `command`: 要测试的命令（必填）
- `ip`: 目标服务器IP（必填）
- `username`: 用户名（必填）
- `password`: 密码（SSH密钥认证时可选）
- `use_ssh_key`: 是否使用SSH密钥认证（可选）
- `ssh_key_path`: SSH私钥路径（可选）
- `timeout`: 超时时间（可选，默认30秒）
- `data_type`: 期望的数据类型（可选）

**响应示例**:
```json
{
  "success": true,
  "error": null,
  "execution_time": 1.23,
  "output_sample": "2.5",
  "security_level": "normal",
  "recommendations": [
    "命令安全性良好",
    "建议添加错误处理"
  ]
}
```

#### POST /monitor/validate
验证监控项配置

**请求体**:
```json
{
  "name": "系统负载",
  "command": "uptime | awk '{print $3}' | sed 's/,//'",
  "data_type": "number",
  "timeout": 30,
  "category": "system"
}
```

**参数说明**:
- `name`: 监控项名称（必填）
- `command`: 监控命令（必填）
- `data_type`: 数据类型（可选）
- `timeout`: 超时时间（可选）
- `category`: 分类（可选）

**响应示例**:
```json
{
  "is_valid": true,
  "messages": [
    "配置验证通过",
    "命令语法正确"
  ],
  "security_level": "normal",
  "suggestions": [
    "uptime",
    "top -bn1",
    "free -m"
  ]
}
```

#### GET /monitor/suggestions
获取监控命令建议

**查询参数**:
- `category`: 分类过滤（可选）

**响应示例**:
```json
{
  "suggestions": [
    {
      "category": "system",
      "commands": [
        {
          "name": "CPU使用率",
          "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
          "description": "获取CPU使用率百分比"
        },
        {
          "name": "内存使用率",
          "command": "free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'",
          "description": "获取内存使用率百分比"
        }
      ]
    }
  ]
}
```

---

### 监控状态管理

#### GET /monitor/status
获取监控状态概览

**响应示例**:
```json
{
  "global_stats": {
    "total_servers": 5,
    "online_servers": 4,
    "offline_servers": 1,
    "total_monitor_items": 20,
    "healthy_items": 18,
    "warning_items": 1,
    "error_items": 1,
    "overall_success_rate": 95.5
  },
  "problematic_servers_count": 1,
  "problematic_servers": [
    {
      "server_ip": "*************",
      "server_status": "offline",
      "health_score": 45.2,
      "error_items": 3,
      "total_items": 5
    }
  ]
}
```

#### GET /monitor/status/server/{server_ip}
获取指定服务器的监控状态

**路径参数**:
- `server_ip`: 服务器IP地址

**响应示例**:
```json
{
  "server_ip": "*************",
  "server_status": "online",
  "last_check_time": "2024-01-01T10:00:00+08:00",
  "total_monitor_items": 5,
  "healthy_items": 4,
  "warning_items": 1,
  "error_items": 0,
  "overall_health_score": 85.5,
  "problematic_items": [
    {
      "monitor_item_id": 2,
      "monitor_item_name": "磁盘使用率",
      "status": "warning",
      "consecutive_failures": 0,
      "success_rate": 88.5,
      "last_error": null,
      "last_check_time": "2024-01-01T10:00:00+08:00"
    }
  ]
}
```

#### GET /monitor/stats
获取监控性能统计信息

**响应示例**:
```json
{
  "scheduler_stats": {
    "running": true,
    "total_cycles": 1250,
    "successful_cycles": 1200,
    "failed_cycles": 50,
    "success_rate": 96.0,
    "average_cycle_time": 45.2,
    "last_cycle_time": "2024-01-01T10:00:00+08:00"
  },
  "error_stats": {
    "total_errors": 125,
    "connection_errors": 45,
    "timeout_errors": 30,
    "command_errors": 25,
    "other_errors": 25
  },
  "system_health": {
    "scheduler_running": true,
    "success_rate": 96.0,
    "average_cycle_time": 45.2,
    "total_errors": 125
  }
}
```

#### GET /monitor/items/{item_id}/statistics
获取监控项统计信息

**路径参数**:
- `item_id`: 监控项ID

**查询参数**:
- `hours`: 统计时间范围（小时），默认24小时

**响应示例**:
```json
{
  "monitor_item_id": 1,
  "monitor_item_name": "CPU使用率",
  "time_range_hours": 24,
  "total_executions": 288,
  "successful_executions": 275,
  "failed_executions": 13,
  "success_rate": 95.5,
  "average_execution_time": 2.3,
  "servers_count": 5,
  "data_points_count": 275,
  "last_execution_time": "2024-01-01T10:00:00+08:00"
}
```

---

### 批量操作

#### POST /monitor/batch
批量操作监控项

**请求体**:
```json
{
  "item_ids": [1, 2, 3, 4],
  "operation": "enable"
}
```

**参数说明**:
- `item_ids`: 监控项ID列表（必填）
- `operation`: 操作类型，支持：enable（启用）、disable（禁用）、delete（删除）（必填）

**响应示例**:
```json
{
  "success_count": 3,
  "failed_count": 1,
  "success_items": [1, 2, 3],
  "failed_items": [
    {
      "id": 4,
      "error": "监控项不存在"
    }
  ]
}
```

#### POST /monitor/execute
立即执行监控项

**请求体**:
```json
{
  "monitor_item_id": 1,
  "server_ips": ["*************", "*************"]
}
```

**参数说明**:
- `monitor_item_id`: 监控项ID（必填）
- `server_ips`: 服务器IP列表，为空时执行所有关联的服务器（可选）

**响应示例**:
```json
{
  "success": true,
  "message": "Execution successful",
  "results": {
    "*************": {
      "success": true,
      "error": null,
      "data": "15.5"
    },
    "*************": {
      "success": false,
      "error": "连接超时",
      "data": null
    }
  }
}
```

---

## 数据模型

### 基础模型字段

所有模型都继承自基础模型，包含以下通用字段：

```json
{
  "id": 1,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### IPUser (IP用户)

```json
{
  "id": 1,
  "ip": "*************",
  "username": "admin",
  "password": "password123",
  "is_connectable": true,
  "use_ssh_key": false,
  "ssh_key_path": null,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### MonitorItem (监控项)

```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number",
  "timeout": 30,
  "retry_count": 2,
  "enabled": true,
  "category": "system",
  "security_level": "normal",
  "validation_rules": null,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### MonitorItemIP (监控项IP关联)

```json
{
  "id": 1,
  "monitor_item_id": 1,
  "ip_address": "*************",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

---

## 时区处理

### 时区策略

系统采用统一的时区处理策略：

1. **内部存储**: 所有时间数据以UTC格式存储在数据库中
2. **API响应**: 所有时间字段自动转换为上海时区 (Asia/Shanghai, UTC+8) 格式返回
3. **时间格式**: 使用ISO 8601格式，包含时区信息 (`YYYY-MM-DDTHH:MM:SS+08:00`)

### 时间字段说明

| 字段名 | 说明 | 格式示例 |
|--------|------|----------|
| `created_at` | 记录创建时间 | `2024-01-01T10:00:00+08:00` |
| `updated_at` | 记录更新时间 | `2024-01-01T10:00:00+08:00` |
| `timestamp` | 数据采集时间 | `2024-01-01T10:00:00+08:00` |

---

## SSH认证配置

### SSH密钥认证

当 `use_ssh_key` 为 `true` 时，系统使用SSH密钥进行认证：

1. **默认密钥路径**: `~/.ssh/id_rsa`, `~/.ssh/id_ed25519`, `~/.ssh/id_ecdsa`, `~/.ssh/id_dsa`
2. **自定义密钥路径**: 通过 `ssh_key_path` 字段指定
3. **密码字段**: 可以为空

### 密码认证

当 `use_ssh_key` 为 `false` 时，系统使用密码认证：

1. **密码字段**: 必须提供有效密码
2. **安全性**: 密码在数据库中存储

### 认证切换

系统支持认证方式的自动切换：

- 当密码字段为空时，自动使用SSH密钥认证
- 当密码字段不为空且 `use_ssh_key` 为 `false` 时，使用密码认证
- SSH密钥认证失败时，可以自动回退到密码认证（如果提供了密码）

---

## 使用示例

### 1. 添加服务器并创建监控项

```bash
# 1. 添加服务器
curl -X POST "http://localhost:8000/ip/add" \
  -H "Content-Type: application/json" \
  -d '{
    "ip": "*************",
    "user": "admin",
    "password": "password123",
    "use_ssh_key": false
  }'

# 2. 创建监控项
curl -X POST "http://localhost:8000/monitor/items" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "磁盘使用率",
    "command": "df -h / | awk '\''NR==2{print $5}'\'' | sed '\''s/%//'\''",
    "description": "获取根分区磁盘使用率",
    "data_type": "number",
    "category": "system"
  }'

# 3. 关联IP地址
curl -X POST "http://localhost:8000/monitor/items/ip" \
  -H "Content-Type: application/json" \
  -d '{
    "monitor_item_id": 1,
    "ip_address": "*************"
  }'
```

### 2. 测试和验证监控项

```bash
# 1. 测试监控命令
curl -X POST "http://localhost:8000/monitor/test" \
  -H "Content-Type: application/json" \
  -d '{
    "command": "df -h / | awk '\''NR==2{print $5}'\'' | sed '\''s/%//'\''",
    "ip": "*************",
    "username": "admin",
    "password": "password123",
    "timeout": 30,
    "data_type": "number"
  }'

# 2. 验证配置
curl -X POST "http://localhost:8000/monitor/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "磁盘使用率",
    "command": "df -h / | awk '\''NR==2{print $5}'\'' | sed '\''s/%//'\''",
    "data_type": "number",
    "category": "system"
  }'
```

### 3. 批量管理和状态监控

```bash
# 1. 批量启用监控项
curl -X POST "http://localhost:8000/monitor/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "item_ids": [1, 2, 3],
    "operation": "enable"
  }'

# 2. 查看监控状态
curl "http://localhost:8000/monitor/status" | jq '.'

# 3. 立即执行监控项
curl -X POST "http://localhost:8000/monitor/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "monitor_item_id": 1,
    "server_ips": ["*************"]
  }'
```

---

## 常见问题

### Q: 如何处理时区问题？
A: 系统自动处理时区转换，所有API响应中的时间都是上海时区格式。请求时可以使用任何时区格式，系统会自动转换。

### Q: SSH密钥认证如何配置？
A: 设置 `use_ssh_key: true` 并确保SSH密钥文件存在。如果不指定 `ssh_key_path`，系统使用默认路径。

### Q: 监控项测试失败怎么办？
A: 检查命令语法、服务器连接、权限设置。使用 `/monitor/test` 接口进行详细测试。

### Q: 如何查看系统运行状态？
A: 使用 `/monitor/status` 和 `/monitor/stats` 接口获取系统状态和性能统计。

### Q: 批量操作支持哪些类型？
A: 支持 enable（启用）、disable（禁用）、delete（删除）三种批量操作。

---

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现IP用户管理功能
- 添加自定义监控项管理
- 支持监控项测试和验证
- 实现批量操作功能
- 添加监控状态管理
- 统一时区处理机制
- 支持SSH密钥和密码认证
