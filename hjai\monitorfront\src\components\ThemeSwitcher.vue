<template>
  <div class="theme-switcher">
    <el-tooltip :content="tooltipContent" placement="bottom" effect="light">
      <el-button
        type="text"
        class="theme-switch-btn"
        @click="switchTheme"
      >
        <i :class="iconClass"></i>
      </el-button>
    </el-tooltip>
  </div>
</template>

<script>
import { getTheme, toggleTheme, THEME_TYPE } from '@/utils/theme'

export default {
  name: 'ThemeSwitcher',
  data () {
    return {
      currentTheme: getTheme()
    }
  },
  computed: {
    isDarkTheme () {
      return this.currentTheme === THEME_TYPE.DARK
    },
    iconClass () {
      return this.isDarkTheme ? 'el-icon-sunny' : 'el-icon-moon'
    },
    tooltipContent () {
      return this.isDarkTheme ? '切换到浅色模式' : '切换到深色模式'
    }
  },
  methods: {
    switchTheme () {
      this.currentTheme = toggleTheme()
    }
  }
}
</script>

<style scoped lang="less">
.theme-switcher {
  display: inline-block;
}

.theme-switch-btn {
  font-size: 20px;
  padding: 0;
  color: inherit;

  i {
    transition: transform 0.3s;
  }

  &:hover i {
    transform: rotate(30deg);
  }
}
</style>
