"""
统一错误处理管理器

提供统一的异常处理策略、错误分类、日志记录和错误恢复机制
"""

import logging
import traceback
import asyncio
from typing import Dict, Any, Optional, List, Callable, Type
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
import json

from config.timezone_utils import TZ

logger = logging.getLogger(__name__)


class ErrorLevel(Enum):
    """错误级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""
    CONNECTION = "connection"
    AUTHENTICATION = "authentication"
    COMMAND_EXECUTION = "command_execution"
    DATA_PARSING = "data_parsing"
    VALIDATION = "validation"
    TIMEOUT = "timeout"
    PERMISSION = "permission"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorInfo:
    """错误信息"""
    category: ErrorCategory
    level: ErrorLevel
    message: str
    details: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=TZ.now_utc)
    traceback_info: Optional[str] = None
    recovery_suggestions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'category': self.category.value,
            'level': self.level.value,
            'message': self.message,
            'details': self.details,
            'context': self.context,
            'timestamp': self.timestamp.isoformat(),
            'traceback_info': self.traceback_info,
            'recovery_suggestions': self.recovery_suggestions
        }


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        # 错误分类规则
        self.classification_rules = {
            # 连接相关错误
            'connection': [
                'connection refused', 'connection timeout', 'connection reset',
                'network unreachable', 'host unreachable', 'no route to host',
                'connection aborted', 'connection closed'
            ],
            # 认证相关错误
            'authentication': [
                'authentication failed', 'permission denied', 'access denied',
                'invalid credentials', 'unauthorized', 'login failed'
            ],
            # 命令执行错误
            'command_execution': [
                'command not found', 'no such file or directory',
                'operation not permitted', 'invalid argument'
            ],
            # 超时错误
            'timeout': [
                'timeout', 'timed out', 'deadline exceeded'
            ],
            # 数据解析错误
            'data_parsing': [
                'json decode error', 'parse error', 'invalid format',
                'decode error', 'encoding error'
            ],
            # 验证错误
            'validation': [
                'validation failed', 'invalid input', 'constraint violation',
                'format error', 'type error'
            ],
            # 权限错误
            'permission': [
                'permission denied', 'access denied', 'forbidden',
                'insufficient privileges'
            ],
            # 资源错误
            'resource': [
                'out of memory', 'disk full', 'resource exhausted',
                'too many connections', 'quota exceeded'
            ],
            # 配置错误
            'configuration': [
                'configuration error', 'config not found', 'invalid config',
                'missing parameter', 'invalid parameter'
            ]
        }
        
        # 错误级别规则
        self.level_rules = {
            ErrorLevel.CRITICAL: [
                'system crash', 'database error', 'critical failure',
                'service unavailable', 'fatal error'
            ],
            ErrorLevel.HIGH: [
                'connection failed', 'authentication failed', 'timeout',
                'permission denied', 'resource exhausted'
            ],
            ErrorLevel.MEDIUM: [
                'command failed', 'parse error', 'validation failed',
                'configuration error'
            ],
            ErrorLevel.LOW: [
                'warning', 'deprecated', 'minor error'
            ]
        }
        
        # 恢复建议
        self.recovery_suggestions = {
            ErrorCategory.CONNECTION: [
                "检查网络连接",
                "验证目标服务器是否在线",
                "检查防火墙设置",
                "尝试重新连接"
            ],
            ErrorCategory.AUTHENTICATION: [
                "检查用户名和密码",
                "验证SSH密钥配置",
                "检查用户权限",
                "确认认证方式设置"
            ],
            ErrorCategory.COMMAND_EXECUTION: [
                "检查命令语法",
                "验证命令路径",
                "确认命令权限",
                "检查系统环境"
            ],
            ErrorCategory.TIMEOUT: [
                "增加超时时间",
                "检查网络延迟",
                "优化命令执行",
                "分批处理数据"
            ],
            ErrorCategory.DATA_PARSING: [
                "检查数据格式",
                "验证编码设置",
                "确认数据类型",
                "添加数据验证"
            ]
        }
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'errors_by_category': {},
            'errors_by_level': {},
            'recent_errors': []
        }
    
    def classify_error(self, error_message: str, exception: Optional[Exception] = None) -> ErrorInfo:
        """
        分类错误
        
        Args:
            error_message: 错误消息
            exception: 异常对象
            
        Returns:
            ErrorInfo: 错误信息
        """
        error_message_lower = error_message.lower()
        
        # 确定错误分类
        category = ErrorCategory.UNKNOWN
        for cat_name, keywords in self.classification_rules.items():
            if any(keyword in error_message_lower for keyword in keywords):
                category = ErrorCategory(cat_name)
                break
        
        # 确定错误级别
        level = ErrorLevel.LOW
        for error_level, keywords in self.level_rules.items():
            if any(keyword in error_message_lower for keyword in keywords):
                level = error_level
                break
        
        # 获取堆栈信息
        traceback_info = None
        if exception:
            traceback_info = ''.join(traceback.format_exception(
                type(exception), exception, exception.__traceback__
            ))
        
        # 获取恢复建议
        suggestions = self.recovery_suggestions.get(category, [])
        
        error_info = ErrorInfo(
            category=category,
            level=level,
            message=error_message,
            details=str(exception) if exception else None,
            traceback_info=traceback_info,
            recovery_suggestions=suggestions
        )
        
        # 更新统计信息
        self._update_error_stats(error_info)
        
        return error_info
    
    def handle_error(
        self,
        error_message: str,
        exception: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None,
        log_error: bool = True
    ) -> ErrorInfo:
        """
        处理错误
        
        Args:
            error_message: 错误消息
            exception: 异常对象
            context: 错误上下文
            log_error: 是否记录日志
            
        Returns:
            ErrorInfo: 错误信息
        """
        error_info = self.classify_error(error_message, exception)
        
        if context:
            error_info.context.update(context)
        
        if log_error:
            self._log_error(error_info)
        
        return error_info
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        log_message = f"[{error_info.category.value.upper()}] {error_info.message}"
        
        if error_info.context:
            log_message += f" | Context: {json.dumps(error_info.context, default=str)}"
        
        if error_info.level == ErrorLevel.CRITICAL:
            logger.critical(log_message)
            if error_info.traceback_info:
                logger.critical(f"Traceback: {error_info.traceback_info}")
        elif error_info.level == ErrorLevel.HIGH:
            logger.error(log_message)
            if error_info.traceback_info:
                logger.error(f"Traceback: {error_info.traceback_info}")
        elif error_info.level == ErrorLevel.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _update_error_stats(self, error_info: ErrorInfo):
        """更新错误统计"""
        self.error_stats['total_errors'] += 1
        
        # 按分类统计
        category_key = error_info.category.value
        if category_key not in self.error_stats['errors_by_category']:
            self.error_stats['errors_by_category'][category_key] = 0
        self.error_stats['errors_by_category'][category_key] += 1
        
        # 按级别统计
        level_key = error_info.level.value
        if level_key not in self.error_stats['errors_by_level']:
            self.error_stats['errors_by_level'][level_key] = 0
        self.error_stats['errors_by_level'][level_key] += 1
        
        # 记录最近错误（保留最近100个）
        self.error_stats['recent_errors'].append(error_info.to_dict())
        if len(self.error_stats['recent_errors']) > 100:
            self.error_stats['recent_errors'].pop(0)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return self.error_stats.copy()
    
    def reset_error_stats(self):
        """重置错误统计"""
        self.error_stats = {
            'total_errors': 0,
            'errors_by_category': {},
            'errors_by_level': {},
            'recent_errors': []
        }
    
    async def handle_async_error(
        self,
        coro_func: Callable,
        *args,
        context: Optional[Dict[str, Any]] = None,
        retry_count: int = 0,
        retry_delay: float = 1.0,
        **kwargs
    ) -> Any:
        """
        异步错误处理装饰器
        
        Args:
            coro_func: 协程函数
            *args: 位置参数
            context: 错误上下文
            retry_count: 重试次数
            retry_delay: 重试延迟
            **kwargs: 关键字参数
            
        Returns:
            Any: 函数执行结果
        """
        last_error = None
        
        for attempt in range(retry_count + 1):
            try:
                if attempt > 0:
                    await asyncio.sleep(retry_delay * attempt)
                
                return await coro_func(*args, **kwargs)
                
            except Exception as e:
                last_error = e
                error_context = context or {}
                error_context.update({
                    'function': coro_func.__name__,
                    'attempt': attempt + 1,
                    'max_attempts': retry_count + 1
                })
                
                error_info = self.handle_error(
                    f"函数执行失败: {str(e)}",
                    exception=e,
                    context=error_context
                )
                
                if attempt == retry_count:
                    # 最后一次尝试失败
                    break
        
        # 所有重试都失败，抛出最后的异常
        raise last_error


# 全局错误处理器实例
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler


def handle_error(
    error_message: str,
    exception: Optional[Exception] = None,
    context: Optional[Dict[str, Any]] = None,
    log_error: bool = True
) -> ErrorInfo:
    """便捷的错误处理函数"""
    return get_error_handler().handle_error(error_message, exception, context, log_error)
