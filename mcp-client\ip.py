import subprocess
import socket
import platform
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import os
import traceback

# 添加paramiko库用于SSH连接
try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    print("警告: 未安装paramiko库，SSH登录功能将不可用")
    print("请使用命令安装: pip install paramiko")

def ping(ip):
    """使用ping命令检测IP是否可达，使用更可靠的方式避免句柄错误"""
    try:
        # 使用DEVNULL避免输出，使用timeout避免阻塞
        if platform.system().lower() == 'windows':
            # Windows系统使用-n参数
            process = subprocess.Popen(
                ['ping', '-n', '1', '-w', '500', ip],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NO_WINDOW  # 避免显示控制台窗口
            )
        else:
            # 类Unix系统使用-c参数
            process = subprocess.Popen(
                ['ping', '-c', '1', '-W', '0.5', ip],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        
        # 等待进程完成，设置超时时间
        return_code = process.wait(timeout=1)
        return return_code == 0
    except (subprocess.TimeoutExpired, OSError):
        # 处理超时或其他OS错误
        try:
            # 尝试终止进程
            process.kill()
        except (OSError, AttributeError):
            # 忽略终止过程中的错误
            pass
        return False

def get_hostname(ip):
    """尝试获取IP对应的主机名"""
    try:
        return socket.gethostbyaddr(ip)[0]
    except socket.herror:
        return "未知主机名"

def get_mac_address(ip):
    """尝试获取MAC地址（仅在Windows系统上）"""
    if platform.system().lower() != 'windows':
        return "不支持的操作系统"
    
    try:
        # 使用arp命令获取MAC地址
        process = subprocess.Popen(
            f'arp -a {ip}', 
            shell=True, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        stdout, _ = process.communicate(timeout=1)
        result = stdout.decode('gbk', errors='ignore')
        
        lines = result.strip().split('\n')
        for line in lines:
            if ip in line:
                parts = line.split()
                if len(parts) >= 2:
                    return parts[1].replace('-', ':')
        return "未找到MAC地址"
    except Exception as e:
        return f"获取MAC地址时出错: {str(e)}"

def get_open_ports(ip, ports=[80, 443, 22, 21, 3389, 8080]):
    """检测常见端口是否开放"""
    open_ports = []
    for port in ports:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(0.5)
        try:
            result = sock.connect_ex((ip, port))
            if result == 0:
                open_ports.append(port)
        except socket.error:
            pass  # 忽略连接错误
        finally:
            sock.close()
    return open_ports

def try_ssh_login(ip, username="root", password="Hjai@2024", port=22, timeout=3):
    """尝试使用SSH登录到目标设备"""
    if not PARAMIKO_AVAILABLE:
        return {"status": "失败", "message": "未安装paramiko库"}
    
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        client.connect(ip, port=port, username=username, password=password, timeout=timeout)
        stdin, stdout, stderr = client.exec_command('uname -a', timeout=timeout)
        system_info = stdout.read().decode('utf-8', errors='ignore').strip()
        
        stdin, stdout, stderr = client.exec_command('cat /etc/issue', timeout=timeout)
        issue_info = stdout.read().decode('utf-8', errors='ignore').strip()
        
        return {
            "status": "成功",
            "system_info": system_info,
            "issue_info": issue_info
        }
    except Exception as e:
        return {"status": "失败", "message": str(e)}
    finally:
        client.close()

def scan_ip(ip):
    """扫描单个IP并获取信息"""
    if ping(ip):
        hostname = get_hostname(ip)
        mac_address = get_mac_address(ip)
        open_ports = get_open_ports(ip)
        
        # 检查SSH登录
        ssh_result = {"status": "未尝试"}
        if 22 in open_ports:
            ssh_result = try_ssh_login(ip)
        
        return {
            "ip": ip,
            "status": "在线",
            "hostname": hostname,
            "mac_address": mac_address,
            "open_ports": open_ports,
            "ssh_login": ssh_result
        }
    return {"ip": ip, "status": "离线"}

def scan_ip_range(start_ip, end_ip):
    """扫描IP范围"""
    # 解析IP地址
    start_parts = list(map(int, start_ip.split('.')))
    end_parts = list(map(int, end_ip.split('.')))
    
    # 生成IP列表
    ip_list = []
    for i in range(start_parts[0], end_parts[0] + 1):
        for j in range(start_parts[1] if i == start_parts[0] else 0, 
                       end_parts[1] + 1 if i == end_parts[0] else 256):
            for k in range(start_parts[2] if i == start_parts[0] and j == start_parts[1] else 0,
                           end_parts[2] + 1 if i == end_parts[0] and j == end_parts[1] else 256):
                for l in range(start_parts[3] if i == start_parts[0] and j == start_parts[1] and k == start_parts[2] else 0,
                               end_parts[3] + 1 if i == end_parts[0] and j == end_parts[1] and k == end_parts[2] else 256):
                    ip_list.append(f"{i}.{j}.{k}.{l}")
    
    results = []
    total = len(ip_list)
    completed = 0
    
    # 创建IP和future的映射，用于错误处理时获取对应的IP
    future_to_ip = {}
    
    # 使用线程池加速扫描，减少最大工作线程数以避免资源耗尽
    with ThreadPoolExecutor(max_workers=20) as executor:
        # 提交任务并记录对应的IP
        for ip in ip_list:
            future = executor.submit(scan_ip, ip)
            future_to_ip[future] = ip
        
        for future in future_to_ip:
            try:
                result = future.result(timeout=5)  # 添加超时处理
                if result["status"] == "在线":
                    results.append(result)
                    # 只打印IP和端口信息
                    print(f"IP: {result['ip']}")
                    if result['open_ports']:
                        print(f"端口: {', '.join(map(str, result['open_ports']))}")
                    
                    # 只打印SSH连接状态
                    if 22 in result['open_ports']:
                        ssh_status = result['ssh_login']['status']
                        if ssh_status == "成功":
                            print(f"SSH登录: 成功")
                        else:
                            print(f"SSH登录: 失败 - {result['ssh_login'].get('message', '未知原因')}")
                    print("-----------------------------------")
            except Exception as e:
                # 获取当前future对应的IP地址
                current_ip = future_to_ip.get(future, "未知IP")
                # 打印详细的错误信息
                print(f"处理IP {current_ip}时出错:")
                print(traceback.format_exc())
                print("-----------------------------------")
            
            completed += 1
            # 不打印进度信息
    
    return results

def main():
    print("网段IP扫描工具 - 精简输出模式")
    print("===============================\n")
    
    # 使用固定的IP范围
    start_ip = "***********"
    end_ip = "*************"
    
    print(f"开始扫描IP范围: {start_ip} - {end_ip}\n")
    
    try:
        start_time = time.time()
        results = scan_ip_range(start_ip, end_ip)
        end_time = time.time()
        
        # 只打印简要统计信息
        print("\n扫描完成!")
        print(f"发现 {len(results)} 个在线设备")
        
        # 统计SSH登录成功的设备数量
        ssh_success_count = sum(1 for device in results if device.get('ssh_login', {}).get('status') == "成功")
        print(f"SSH登录成功: {ssh_success_count} 台设备")
        
        # 保存结果到文件
        with open("scan_results.txt", "w", encoding="utf-8") as f:
            f.write(f"扫描时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"IP范围: {start_ip} - {end_ip}\n")
            f.write(f"发现 {len(results)} 个在线设备\n")
            f.write(f"SSH登录成功: {ssh_success_count} 台设备\n\n")
            
            for device in results:
                f.write(f"IP: {device['ip']}\n")
                f.write(f"主机名: {device['hostname']}\n")
                f.write(f"MAC地址: {device['mac_address']}\n")
                if device['open_ports']:
                    f.write(f"开放端口: {', '.join(map(str, device['open_ports']))}\n")
                
                # 添加SSH登录信息到结果文件
                if 22 in device.get('open_ports', []):
                    ssh_result = device.get('ssh_login', {})
                    if ssh_result.get('status') == "成功":
                        f.write(f"SSH登录: 成功\n")
                        f.write(f"系统信息: {ssh_result.get('system_info', '未知')}\n")
                        f.write(f"发行版本: {ssh_result.get('issue_info', '未知')}\n")
                    else:
                        f.write(f"SSH登录: 失败 - {ssh_result.get('message', '未知原因')}\n")
                
                f.write("-----------------------------------\n")
        
        print(f"结果已保存到 scan_results.txt")
    except Exception as e:
        # 打印详细的错误信息
        print("执行过程中发生错误:")
        print(traceback.format_exc())

if __name__ == "__main__":
    main()