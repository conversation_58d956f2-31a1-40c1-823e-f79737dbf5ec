import os
import sys
import asyncio
import logging
from tortoise import Tortoise

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入配置
from config.db import TORTOISE_ORM

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

async def migrate():
    """执行数据库迁移"""
    logger.info("开始数据库迁移...")
    
    # 初始化ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 生成数据库表结构（如果不存在）
    logger.info("正在生成表结构...")
    await Tortoise.generate_schemas()
    
    logger.info("数据库迁移完成")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(migrate()) 