<template>
  <div class="ip-selector">
    <el-select
      v-model="selectedIp"
      placeholder="选择服务器IP"
      size="small"
      @change="handleIpChange"
      :loading="loading">
      <el-option
        v-for="ip in ipList"
        :key="ip"
        :label="ip"
        :value="ip">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'IPSelector',
  data () {
    return {
      loading: false
    }
  },
  computed: {
    ...mapGetters({
      ipList: 'ip/ipList',
      currentIp: 'ip/currentIp'
    }),
    selectedIp: {
      get () {
        return this.currentIp
      },
      set (value) {
        // 直接在这里不设置，而是通过handleIpChange方法处理
      }
    }
  },
  created () {
    // 组件创建时获取IP列表
    this.fetchIpList()
  },
  methods: {
    ...mapActions({
      fetchIpList: 'ip/fetchIpList'
    }),
    ...mapMutations({
      setCurrentIp: 'ip/SET_CURRENT_IP'
    }),

    // 处理IP选择变更
    async handleIpChange (ip) {
      this.setCurrentIp(ip)

      // 选择IP后自动获取GPU列表
      try {
        // 获取GPU列表
        if (this.$store.dispatch) {
          await this.$store.dispatch('monitoring/fetchGpuList')

          // 然后刷新监控数据
          await this.$store.dispatch('monitoring/refreshMonitoringData')
        }
      } catch (error) {
        console.error('选择IP后自动刷新数据失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.ip-selector {
  margin-right: 15px;
}
</style>
