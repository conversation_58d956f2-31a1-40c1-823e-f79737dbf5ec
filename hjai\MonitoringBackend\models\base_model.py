from tortoise import fields
from tortoise.models import Model
from datetime import datetime
from typing import Optional, Type, TypeVar, Generic
import pytz

# 导入新的时区处理模块
from config.timezone_utils import TZ, TimezoneMixin
# 保持向后兼容
from config.timezone import now, add_tz

T = TypeVar("T", bound="BaseModel")

# 定义上海时区（向后兼容）
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')

# 定义返回上海时区时间的函数（向后兼容）
def get_shanghai_now():
    """获取当前上海时区的时间，用于字段默认值"""
    return now()

class BaseModel(Model, TimezoneMixin):
    """
    基础模型类，包含所有模型通用的字段
    使用新的时区处理方式，但保持向后兼容
    """
    id = fields.IntField(pk=True, description="主键ID")
    created_at = fields.DatetimeField(description="创建时间")
    updated_at = fields.DatetimeField(description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否已删除")

    class Meta:
        abstract = True  # 标记为抽象类，不会创建实际的表

    async def save(self, *args, **kwargs):
        """
        重写保存方法，使用新的时区处理方式

        修复策略：
        1. 使用带时区信息的UTC时间
        2. 让Tortoise ORM正确处理时区
        3. 消除RuntimeWarning警告
        """
        # 获取当前UTC时间（带时区信息）
        current_utc = TZ.now_utc()

        # 如果是新记录，设置创建时间
        if not self.created_at:
            self.created_at = current_utc  # 保持UTC时区信息

        # 每次保存都更新更新时间
        self.updated_at = current_utc  # 保持UTC时区信息

        # 调用父类的save方法完成保存
        return await super().save(*args, **kwargs)

    def get_created_at_display(self) -> str:
        """获取创建时间的显示格式（上海时区）"""
        if not self.created_at:
            return None
        return TZ.to_display_format(self.created_at, source_tz=TZ.UTC)

    def get_updated_at_display(self) -> str:
        """获取更新时间的显示格式（上海时区）"""
        if not self.updated_at:
            return None
        return TZ.to_display_format(self.updated_at, source_tz=TZ.UTC)
    
    @classmethod
    async def get_by_id(cls: Type[T], id_: int) -> Optional[T]:
        """
        根据ID获取记录，会自动排除已删除的记录
        """
        return await cls.filter(id=id_, is_deleted=False).first()
    
    @classmethod
    async def soft_delete(cls: Type[T], id_: int) -> bool:
        """
        软删除记录
        
        Returns:
            bool: 删除成功返回True，找不到记录返回False
        """
        obj = await cls.filter(id=id_, is_deleted=False).first()
        if not obj:
            return False
            
        obj.is_deleted = True
        await obj.save()
        return True 