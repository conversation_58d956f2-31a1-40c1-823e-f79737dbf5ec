<div align="center">
<h1>Go Ldap Admin</h1>

[![Auth](https://img.shields.io/badge/Auth-eryajf-ff69b4)](https://github.com/eryajf)
[![GitHub Issues](https://img.shields.io/github/issues/eryajf/go-ldap-admin-ui.svg)](https://github.com/eryajf/go-ldap-admin-ui/issues)
[![GitHub Pull Requests](https://img.shields.io/github/issues-pr/eryajf/go-ldap-admin-ui)](https://github.com/eryajf/go-ldap-admin-ui/pulls)
[![GitHub Pull Requests](https://img.shields.io/github/stars/eryajf/go-ldap-admin-ui)](https://github.com/eryajf/go-ldap-admin-ui/stargazers)
[![HitCount](https://views.whatilearened.today/views/github/eryajf/go-ldap-admin-ui.svg)](https://github.com/eryajf/go-ldap-admin-ui)
[![GitHub license](https://img.shields.io/github/license/eryajf/go-ldap-admin-ui)](https://github.com/eryajf/go-ldap-admin-ui/blob/main/LICENSE)

<p> 🌉 基于Go+Vue实现的openLDAP后台管理项目 🌉</p>

<img src="https://cdn.jsdelivr.net/gh/eryajf/tu@main/img/image_20240420_214408.gif" width="800"  height="3">
</div><br>

<p align="center">
  <a href="" rel="noopener">
 <img src="https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220614_131521.jpg" alt="Project logo"></a>
</p>

<!-- START doctoc generated TOC please keep comment here to allow auto update -->
<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->

**目录**

- [🏊 在线体验](#-%E5%9C%A8%E7%BA%BF%E4%BD%93%E9%AA%8C)
- [👨‍💻 项目地址](#-%E9%A1%B9%E7%9B%AE%E5%9C%B0%E5%9D%80)
- [🔗 文档快链](#-%E6%96%87%E6%A1%A3%E5%BF%AB%E9%93%BE)
- [🥰 感谢](#-%E6%84%9F%E8%B0%A2)
- [🤗 另外](#-%E5%8F%A6%E5%A4%96)
- [⚡ 加群](#-%E5%8A%A0%E7%BE%A4)
- [🤑 捐赠](#-%E6%8D%90%E8%B5%A0)
- [📝 使用登记](#-%E4%BD%BF%E7%94%A8%E7%99%BB%E8%AE%B0)
- [🤝 贡献者](#-%E8%B4%A1%E7%8C%AE%E8%80%85)

<!-- END doctoc generated TOC please keep comment here to allow auto update -->


## 🏊 在线体验

提供在线体验地址如下：

- 地址：[http://61.171.114.86:8888](http://61.171.114.86:8888)
- 登陆信息：admin/123456

> 在线环境可能不稳，如果遇到访问异常，或者数据错乱，请联系我进行修复。请勿填写个人敏感信息。


**页面功能概览：**

|    ![登录页](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_165411.png)    | ![首页](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_165545.png)     |
| :----------------------------------------------------------------------------------: | -------------------------------------------------------------------------------- |
|   ![用户管理](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_165623.png)   | ![分组管理](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_165701.png) |
| ![字段关系管理](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_165853.png) | ![菜单管理](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_165954.png) |
|   ![接口管理](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_170015.png)   | ![操作日志](https://cdn.jsdelivr.net/gh/eryajf/tu/img/image_20220724_170035.png) |

## 👨‍💻 项目地址

| 分类 |                     GitHub                     |                        Gitee                        |
| :--: | :--------------------------------------------: | :-------------------------------------------------: |
| 后端 |  https://github.com/eryajf/go-ldap-admin.git   |  https://gitee.com/eryajf-world/go-ldap-admin.git   |
| 前端 | https://github.com/eryajf/go-ldap-admin-ui.git | https://gitee.com/eryajf-world/go-ldap-admin-ui.git |

## 🔗 文档快链

项目相关介绍，使用，最佳实践等相关内容，都会在官方文档呈现，如有疑问，请先阅读官方文档，以下列举以下常用快链。

- [官网地址](http://ldapdoc.eryajf.net)
- [项目背景](http://ldapdoc.eryajf.net/pages/101948/)
- [快速开始](http://ldapdoc.eryajf.net/pages/706e78/)
- [功能概览](http://ldapdoc.eryajf.net/pages/7a40de/)
- [本地开发](http://ldapdoc.eryajf.net/pages/cb7497/)
- [生产部署](http://ldapdoc.eryajf.net/pages/5769c4/)

## 🥰 感谢

感谢如下优秀的项目，没有这些项目，不可能会有 go-ldap-admin：

- 后端技术栈
  - [Gin-v1.6.3](https://github.com/gin-gonic/gin)
  - [Gorm-v1.20.12](https://github.com/go-gorm/gorm)
  - [Go-ldap-v3.4.2](https://github.com/go-ldap/ldap)
  - [Casbin-v2.22.0](https://github.com/casbin/casbin)
- 前端技术栈

  - [element-ui](https://github.com/ElemeFE/element)
  - [axios](https://github.com/axios/axios)

- 另外感谢
  - [go-web-mini](https://github.com/gnimli/go-web-mini)：项目基于该项目重构而成，感谢作者的付出。

## 🤗 另外

- 如果觉得项目不错，麻烦动动小手点个 ⭐️star⭐️!
- 如果你还有其他想法或者需求，欢迎在 issue 中交流！

## 🤑 捐赠

如果你觉得这个项目对你有帮助，你可以请作者喝杯咖啡 ☕️ [点我](http://ldapdoc.eryajf.net/pages/2b6725/)

## 📝 使用登记

如果你所在公司使用了该项目，烦请在这里留下脚印，感谢支持 🥳 [点我](https://github.com/eryajf/go-ldap-admin/issues/18)

## 🤝 贡献者

<!-- readme: collaborators,contributors -start -->
<table>
<tr>
    <td align="center">
        <a href="https://github.com/eryajf">
            <img src="https://avatars.githubusercontent.com/u/33259379?v=4" width="100;" alt="eryajf"/>
            <br />
            <sub><b>二丫讲梵</b></sub>
        </a>
    </td>
    <td align="center">
        <a href="https://github.com/OTR37">
            <img src="https://avatars.githubusercontent.com/u/51768167?v=4" width="100;" alt="OTR37"/>
            <br />
            <sub><b>OTR37</b></sub>
        </a>
    </td>
    <td align="center">
        <a href="https://github.com/xinyuandd">
            <img src="https://avatars.githubusercontent.com/u/3397848?v=4" width="100;" alt="xinyuandd"/>
            <br />
            <sub><b>Xinyuandd</b></sub>
        </a>
    </td>
    <td align="center">
        <a href="https://github.com/wang-xiaowu">
            <img src="https://avatars.githubusercontent.com/u/44340137?v=4" width="100;" alt="wang-xiaowu"/>
            <br />
            <sub><b>Xiaowu</b></sub>
        </a>
    </td>
    <td align="center">
        <a href="https://github.com/nangongchengfeng">
            <img src="https://avatars.githubusercontent.com/u/46562911?v=4" width="100;" alt="nangongchengfeng"/>
            <br />
            <sub><b>南宫乘风</b></sub>
        </a>
    </td>
    <td align="center">
        <a href="https://github.com/nnsay">
            <img src="https://avatars.githubusercontent.com/u/14331919?v=4" width="100;" alt="nnsay"/>
            <br />
            <sub><b>Jimmy Wang</b></sub>
        </a>
    </td></tr>
<tr>
    <td align="center">
        <a href="https://github.com/Sdaiyan">
            <img src="https://avatars.githubusercontent.com/u/53208364?v=4" width="100;" alt="Sdaiyan"/>
            <br />
            <sub><b>Sdaiyan</b></sub>
        </a>
    </td>
    <td align="center">
        <a href="https://github.com/xzzgwzh">
            <img src="https://avatars.githubusercontent.com/u/133201531?v=4" width="100;" alt="xzzgwzh"/>
            <br />
            <sub><b>Xzzgwzh</b></sub>
        </a>
    </td></tr>
</table>
<!-- readme: collaborators,contributors -end -->
