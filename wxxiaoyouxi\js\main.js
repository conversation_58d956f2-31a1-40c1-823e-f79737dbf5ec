// 忽略SharedArrayBuffer警告，这是微信小游戏环境中的一个警告，不影响游戏运行
// [Deprecation] SharedArrayBuffer will require cross-origin isolation as of M92, around July 2021.
// 这是微信小游戏环境的已知警告，与游戏逻辑无关，可以安全忽略
import './render'; // 初始化Canvas
import DataBus from './databus'; // 导入数据管理类
import HallScene from './scenes/hall'; // 导入大厅场景
import GameScene from './scenes/game'; // 导入游戏场景
import ResultScene from './scenes/result'; // 导入结果场景

const ctx = canvas.getContext('2d'); // 获取canvas的2D绘图上下文

// 全局数据管理实例
GameGlobal.databus = new DataBus();

// 添加全局错误处理
wx.onError(function(res) {
  console.log('捕获到全局错误:', res.message);
  // 这里可以添加错误上报或其他处理逻辑
});

/**
 * 游戏主函数
 */
export default class Main {
  aniId = 0; // 用于存储动画帧的ID
  currentScene = null; // 当前场景
  scenes = {}; // 场景集合

  constructor() {
    // 初始化游戏
    this.init();
    // 开始游戏主循环
    this.loop();
  }

  /**
   * 初始化游戏
   */
  init() {
    // 重置游戏数据
    GameGlobal.databus.reset();
    
    // 创建场景
    this.createScenes();
    
    // 默认显示大厅场景
    this.switchScene('hall');
    
    console.log('游戏初始化完成');
  }

  /**
   * 创建场景
   */
  createScenes() {
    try {
      // 创建大厅场景
      this.scenes.hall = new HallScene({
        onStartGame: (gameMode, playerCount) => {
          // 设置游戏模式和玩家数量
          GameGlobal.databus.gameMode = gameMode;
          
          // 初始化游戏
          GameGlobal.databus.initGame(playerCount, gameMode);
          
          // 切换到游戏场景
          this.switchScene('game');
        }
      });
      
      // 创建游戏场景
      this.scenes.game = new GameScene({
        onGameOver: (winner) => {
          // 游戏结束，切换到结果场景
          this.switchScene('result', { winner });
        },
        onBack: () => {
          // 返回大厅
          this.switchScene('hall');
        }
      });
      
      // 创建结果场景
      this.scenes.result = new ResultScene({
        onRestart: () => {
          // 重新开始游戏
          this.switchScene('game');
        },
        onBack: () => {
          // 返回大厅
          this.switchScene('hall');
        }
      });
      
      // 初始化所有场景
      Object.values(this.scenes).forEach(scene => {
        try {
          scene.init();
        } catch (e) {
          console.error(`初始化场景 ${scene.name} 失败:`, e);
        }
      });
    } catch (e) {
      console.error('创建场景失败:', e);
    }
  }
  
  /**
   * 切换场景
   * @param {string} sceneName - 场景名称
   * @param {Object} params - 传递给场景的参数
   */
  switchScene(sceneName, params = {}) {
    // 如果有当前场景，先退出
    if (this.currentScene) {
      this.currentScene.exit();
    }
    
    // 切换到新场景
    this.currentScene = this.scenes[sceneName];
    
    // 如果有参数，设置参数
    if (params) {
      this.currentScene.setParams && this.currentScene.setParams(params);
    }
    
    // 进入新场景
    this.currentScene.enter();
  }

  /**
   * 渲染游戏画面
   */
  render() {
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 渲染当前场景
    if (this.currentScene) {
      this.currentScene.render(ctx);
    }
  }

  /**
   * 更新游戏逻辑
   */
  update() {
    // 增加帧数
    GameGlobal.databus.frame++;
    
    // 更新当前场景
    if (this.currentScene) {
      this.currentScene.update();
    }
  }

  /**
   * 游戏循环
   */
  loop() {
    // 更新游戏逻辑
    this.update();
    
    // 渲染游戏画面
    this.render();

    // 循环调用，形成帧动画
    this.aniId = requestAnimationFrame(this.loop.bind(this));
  }
}
