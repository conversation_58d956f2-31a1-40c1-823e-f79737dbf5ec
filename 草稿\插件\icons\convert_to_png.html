<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>SVG to PNG Converter</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f8f9fa;
    }
    h1 {
      color: #4a89dc;
      text-align: center;
    }
    .instructions {
      background-color: #fff;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .icon-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }
    .icon-box {
      background-color: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      text-align: center;
    }
    .icon-box img {
      display: block;
      margin: 0 auto;
      border: 1px dashed #ccc;
      padding: 10px;
      background-color: #f0f4ff;
    }
    .icon-box p {
      margin: 10px 0 5px;
      font-weight: bold;
    }
    .icon-box button {
      background-color: #4a89dc;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    .icon-box button:hover {
      background-color: #3d7acc;
    }
  </style>
</head>
<body>
  <h1>SVG to PNG Converter</h1>
  
  <div class="instructions">
    <h3>如何使用：</h3>
    <ol>
      <li>点击下方的"转换为PNG"按钮</li>
      <li>在新标签页中右键点击图像</li>
      <li>选择"图片另存为..."</li>
      <li>保存为PNG格式，使用相应的文件名（icon16.png, icon32.png等）</li>
    </ol>
  </div>
  
  <div class="icon-container">
    <div class="icon-box">
      <p>16x16 图标</p>
      <img src="icon16.svg" width="16" height="16" alt="16x16 Icon" id="icon16">
      <button onclick="convertToPNG('icon16', 16)">转换为PNG</button>
    </div>
    
    <div class="icon-box">
      <p>32x32 图标</p>
      <img src="icon32.svg" width="32" height="32" alt="32x32 Icon" id="icon32">
      <button onclick="convertToPNG('icon32', 32)">转换为PNG</button>
    </div>
    
    <div class="icon-box">
      <p>48x48 图标</p>
      <img src="icon48.svg" width="48" height="48" alt="48x48 Icon" id="icon48">
      <button onclick="convertToPNG('icon48', 48)">转换为PNG</button>
    </div>
    
    <div class="icon-box">
      <p>128x128 图标</p>
      <img src="icon128.svg" width="128" height="128" alt="128x128 Icon" id="icon128">
      <button onclick="convertToPNG('icon128', 128)">转换为PNG</button>
    </div>
  </div>

  <script>
    function convertToPNG(id, size) {
      const svg = document.getElementById(id);
      const canvas = document.createElement('canvas');
      canvas.width = size;
      canvas.height = size;
      const ctx = canvas.getContext('2d');
      
      const img = new Image();
      img.onload = function() {
        ctx.drawImage(img, 0, 0, size, size);
        const dataURL = canvas.toDataURL('image/png');
        
        const win = window.open();
        win.document.write(`<img src="${dataURL}" width="${size*4}" height="${size*4}" style="image-rendering: pixelated; background: #f0f0f0; padding: 20px;">`);
        win.document.title = `${id}.png - 右键保存`;
      };
      img.src = svg.src;
    }
  </script>
</body>
</html>