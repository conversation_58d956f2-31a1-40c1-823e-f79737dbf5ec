import request from '@/utils/request'

/**
 * 获取GPU统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.ip - 服务器IP地址
 * @param {string} params.start_time - 开始时间 ISO格式
 * @param {string} params.end_time - 结束时间 ISO格式
 * @param {string} [params.gpu_index='all'] - GPU索引，'all'表示所有GPU，或特定GPU索引数字
 * @param {boolean} [params.useCache=true] - 是否使用缓存
 * @param {number} [params.cacheTime=60000] - 缓存时间（毫秒）
 * @returns {Promise} - 请求Promise
 */
export function getGpuStats (params) {
  return request({
    url: '/gpu/stats',
    method: 'post',
    data: {
      ip: params.ip,
      start_time: params.start_time,
      end_time: params.end_time,
      gpu_index: params.gpu_index || 'all'
    },
    useCache: params.useCache !== false,
    cacheTime: params.cacheTime || 60000, // 默认缓存1分钟
    retry: 2, // 失败时重试2次
    retryDelay: 1000 // 重试间隔1秒
  })
}

/**
 * 获取GPU数量和列表
 * @param {Object} params - 查询参数
 * @param {string} params.ip - 服务器IP地址
 * @param {boolean} [params.useCache=true] - 是否使用缓存
 * @param {number} [params.cacheTime=300000] - 缓存时间（毫秒）
 * @returns {Promise} - 请求Promise
 */
export function getGpuCount (params) {
  return request({
    url: '/gpu/count',
    method: 'post',
    data: {
      ip: params.ip,
      gpu_count: true
    },
    useCache: params.useCache !== false,
    cacheTime: params.cacheTime || 300000, // 默认缓存5分钟
    retry: 2, // 失败时重试2次
    retryDelay: 1000 // 重试间隔1秒
  })
}
