from fastapi import APIRouter, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from models.memory_stats import MemoryStats
from models.ip_user import IPUser
# 导入新的时区处理工具
from config.timezone_utils import TZ
# 保持向后兼容
from config.timezone import add_tz


# 定义请求和响应模型
class MemoryStatsRequest(BaseModel):
    """内存统计数据请求模型"""
    ip: str
    start_time: datetime
    end_time: datetime


class MemoryStatsResponse(BaseModel):
    """内存统计数据响应模型"""
    ip: str
    timestamp: datetime
    usage_percent: float

    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


# 创建路由
router = APIRouter(
    prefix="/memory",
    tags=["内存监控"]
)


@router.post("/stats", response_model=List[MemoryStatsResponse])
async def get_memory_stats(request: MemoryStatsRequest):
    """
    根据IP地址和时间范围查询内存监控数据
    
    参数:
        request: 包含ip、start_time和end_time的请求
    
    返回:
        内存监控数据列表
    """
    # 验证请求参数
    if not request.ip or not request.start_time or not request.end_time:
        raise HTTPException(status_code=400, detail="请求必须包含ip、start_time和end_time字段")
    
    # 验证时间范围
    if request.start_time >= request.end_time:
        raise HTTPException(status_code=400, detail="start_time必须早于end_time")
    
    # 首先查询ip_user表，判断IP是否存在
    ip_user = await IPUser.filter(ip=request.ip).first()
    
    # 如果IP在ip_user表中不存在，返回404错误
    if not ip_user:
        raise HTTPException(status_code=404, detail=f"IP地址为 {request.ip} 的不存在")
    
    # 如果IP存在但已标记为删除
    if ip_user.is_deleted:
        raise HTTPException(status_code=404, detail=f"IP地址 {request.ip} 的监控数据已被删除")
    
    # 将输入的时间范围转换为UTC时间进行查询
    start_time_utc = TZ.from_input(request.start_time)
    end_time_utc = TZ.from_input(request.end_time)

    # 查询指定时间范围内的内存使用数据（数据库中存储的是带时区信息的UTC时间）
    memory_stats = await MemoryStats.filter(
        ip=request.ip,
        timestamp__gte=start_time_utc,  # 保持UTC时区信息
        timestamp__lte=end_time_utc,    # 保持UTC时区信息
        is_deleted=False
    ).order_by('timestamp').all()
    
    # 如果在指定时间范围内没有数据，返回空列表
    if not memory_stats:
        return []
    
    # 转换为响应格式，只返回IP、时间和使用率百分比
    return [
        MemoryStatsResponse(
            ip=stat.ip,
            timestamp=stat.timestamp,
            usage_percent=stat.usage_percent
        )
        for stat in memory_stats
    ] 