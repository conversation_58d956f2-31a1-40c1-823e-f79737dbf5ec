import logging
import paramiko
from typing import Dict, Any

from models.memory_stats import MemoryStats
from scripts.base_monitor import BaseMonitor
from config.settings import get_shanghai_time

# 获取日志记录器
logger = logging.getLogger(__name__)

class MemoryMonitor(BaseMonitor):
    """内存监控类"""
    
    def __init__(self):
        # 初始化，设置默认监控间隔为5分钟(300秒)
        super().__init__("内存", 300)
    
    def get_memory_info(self, ssh_client: paramiko.SSHClient) -> Dict[str, Any]:
        """
        通过SSH执行命令获取内存信息
        
        Args:
            ssh_client: SSH客户端连接
            
        Returns:
            Dict[str, Any]: 内存信息字典
        """
        try:
            # 执行命令获取内存信息
            stdin, stdout, stderr = ssh_client.exec_command("free -m")
            
            # 读取输出
            output = stdout.read().decode("utf-8").strip()
            error = stderr.read().decode("utf-8").strip()
            
            if error:
                logger.error(f"执行free命令出错: {error}")
                return {}
            
            # 解析输出
            lines = output.split("\n")
            if len(lines) < 2:
                logger.error("free命令输出格式异常")
                return {}
            
            # 解析内存行
            mem_line = lines[1].split()
            if len(mem_line) < 7:
                logger.error(f"内存数据格式异常: {lines[1]}")
                return {}
            
            # 获取内存信息 - 只保留必要的指标
            memory_info = {
                "total": int(mem_line[1]),             # 总内存 (MB)
                "used": int(mem_line[2]),              # 已用内存 (MB)
                "free": int(mem_line[3]),              # 空闲内存 (MB)
                "available": int(mem_line[6])          # 可用内存 (MB)
            }
            
            # 计算内存使用率
            memory_info["usage_percent"] = round(memory_info["used"] / memory_info["total"] * 100, 2)
            
            # 执行命令获取SWAP信息
            stdin, stdout, stderr = ssh_client.exec_command("cat /proc/swaps")
            output = stdout.read().decode("utf-8").strip()
            
            # 解析SWAP信息
            swap_total = 0
            swap_used = 0
            
            if len(output.split('\n')) > 1:
                for line in output.split('\n')[1:]:  # 跳过标题行
                    parts = line.split()
                    if len(parts) >= 4:
                        swap_total += int(parts[2]) // 1024  # 转换为MB
                        swap_used += int(parts[3]) // 1024   # 转换为MB
            
            memory_info["swap_total"] = swap_total
            memory_info["swap_used"] = swap_used
            
            return memory_info
        except Exception as e:
            logger.error(f"获取内存信息失败: {str(e)}")
            return {}
    
    async def save_memory_stats(self, ip: str, memory_info: Dict[str, Any]) -> bool:
        """
        保存内存统计数据到数据库
        
        Args:
            ip: 服务器IP
            memory_info: 内存信息字典
            
        Returns:
            bool: 是否保存成功
        """
        if not memory_info:
            logger.warning(f"没有内存数据可保存: {ip}")
            return False
        
        try:
            # 获取当前UTC时间用于数据库存储
            from config.timezone_utils import TZ
            current_time = TZ.now_utc()  # 保持UTC时区信息
            
            # 创建内存统计记录
            await MemoryStats.create(
                ip=ip,
                total=memory_info["total"],
                used=memory_info["used"],
                free=memory_info["free"],
                available=memory_info["available"],
                usage_percent=memory_info["usage_percent"],
                swap_total=memory_info["swap_total"],
                swap_used=memory_info["swap_used"],
                timestamp=current_time  # 手动设置时间戳
            )
            logger.debug(f"成功保存服务器 {ip} 的内存统计数据到数据库")
            return True
        except Exception as e:
            logger.error(f"保存内存统计数据失败: {str(e)}")
            return False
    
    async def start_monitoring(self, interval_seconds: int = None) -> None:
        """
        开始定时监控所有服务器内存
        
        Args:
            interval_seconds: 监控间隔，单位秒，默认为self.default_interval(300秒)
        """
        # 使用基类的通用监控启动方法
        await super().start_monitoring(
            data_collector=self.get_memory_info,
            data_saver=self.save_memory_stats,
            only_connectable=True,
            interval_seconds=interval_seconds
        )

# 为保持向后兼容性添加的函数
async def start_monitoring(interval_seconds: int = 300) -> None:
    """
    开始定时监控所有服务器内存（兼容旧版API）
    
    Args:
        interval_seconds: 监控间隔，单位秒，默认300秒(5分钟)
    """
    logger.info("通过兼容函数启动内存监控服务")
    monitor = MemoryMonitor()
    await monitor.start_monitoring(interval_seconds) 