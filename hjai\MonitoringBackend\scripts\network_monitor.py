import logging
import paramiko
import time
import traceback
import re
from typing import Dict, Any, List, Tuple, Optional

from models.network_stats import NetworkStatsSummary, NetworkStatsDetail
from scripts.base_monitor import BaseMonitor

logger = logging.getLogger(__name__)

class NetworkMonitor(BaseMonitor):
    """网卡监控类"""
    
    def __init__(self):
        super().__init__("网卡", 300)
    
    def _check_ssh_connection(self, ssh_client: paramiko.SSHClient) -> bool:
        """检查SSH连接是否有效"""
        if ssh_client is None:
            return False
            
        try:
            transport = ssh_client.get_transport()
            if transport and transport.is_active() and transport.is_authenticated():
                stdin, stdout, stderr = ssh_client.exec_command("echo 1", timeout=2)
                return stdout.channel.recv_exit_status() == 0
            return False
        except Exception:
            return False
    
    def _execute_ssh_command(self, ssh_client: paramiko.SSHClient, command: str, timeout: int = 10) -> <PERSON><PERSON>[Optional[str], bool]:
        """执行SSH命令"""
        if not self._check_ssh_connection(ssh_client):
            return None, False
                
        try:
            stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
            output = stdout.read().decode("utf-8").strip()
            exit_status = stdout.channel.recv_exit_status()
            return output, exit_status == 0
        except Exception as e:
            logger.warning(f"执行命令失败: {str(e)}")
            return None, False
    
    def get_network_info(self, ssh_client: paramiko.SSHClient) -> Dict[str, Any]:
        """获取网卡信息"""
        try:
            # 批量获取网卡信息的脚本
            batch_cmd = """
#!/bin/bash

# 获取所有网卡列表
interfaces=$(ip link show | grep '^[0-9]' | awk '{print $2}' | sed 's/://g')
echo "==INTERFACES_START=="
echo "$interfaces"
echo "==INTERFACES_END=="

# 获取完整的 ip link show 输出
echo "==IP_LINK_FULL_START=="
ip link show
echo "==IP_LINK_FULL_END=="

# 获取流量统计（第一次）
echo "==STATS_FIRST_START=="
cat /proc/net/dev
echo "==STATS_FIRST_END=="

# 等待0.3秒
sleep 0.3

# 获取流量统计（第二次）
echo "==STATS_SECOND_START=="
cat /proc/net/dev
echo "==STATS_SECOND_END=="

# 获取IP地址信息
echo "==IP_ADDR_START=="
ip addr show
echo "==IP_ADDR_END=="

# 获取每个网卡的系统级信息
for iface in $interfaces; do
    echo "==IFACE_SYS_START:$iface=="
    
    # 操作状态
    echo -n "OPERSTATE:"
    cat /sys/class/net/$iface/operstate 2>/dev/null || echo "unknown"
    
    # 速率
    echo -n "SPEED:"
    speed=$(cat /sys/class/net/$iface/speed 2>/dev/null || echo "unknown")
    if [ "$speed" != "unknown" ] && [ "$speed" -gt 0 ] 2>/dev/null; then
        echo "${speed}"
    else
        echo "unknown"
    fi
    
    # 双工模式
    echo -n "DUPLEX:"
    cat /sys/class/net/$iface/duplex 2>/dev/null || echo "unknown"
    
    # 载波状态
    echo -n "CARRIER:"
    cat /sys/class/net/$iface/carrier 2>/dev/null || echo "unknown"
    
    echo "==IFACE_SYS_END:$iface=="
done
"""
            
            output, success = self._execute_ssh_command(ssh_client, batch_cmd, timeout=30)
            if not success or not output:
                return {}
            
            return self._parse_detailed_network_output(output)
            
        except Exception as e:
            logger.error(f"获取网卡信息失败: {str(e)}")
            return {}
    
    def _parse_detailed_network_output(self, output: str) -> Dict[str, Any]:
        """解析详细的网卡信息输出"""
        network_info = {}
        
        # 1. 解析网卡列表
        interfaces = []
        if "==INTERFACES_START==" in output and "==INTERFACES_END==" in output:
            iface_section = output.split("==INTERFACES_START==")[1].split("==INTERFACES_END==")[0].strip()
            interfaces = iface_section.split() if iface_section else []
        
        if not interfaces:
            return {}
        
        logger.debug(f"发现 {len(interfaces)} 个网卡: {interfaces}")
        
        # 2. 解析 ip link show 的完整输出
        link_info = {}
        if "==IP_LINK_FULL_START==" in output and "==IP_LINK_FULL_END==" in output:
            link_section = output.split("==IP_LINK_FULL_START==")[1].split("==IP_LINK_FULL_END==")[0].strip()
            link_info = self._parse_ip_link_output(link_section)
        
        # 3. 解析 ip addr show 输出
        addr_info = {}
        if "==IP_ADDR_START==" in output and "==IP_ADDR_END==" in output:
            addr_section = output.split("==IP_ADDR_START==")[1].split("==IP_ADDR_END==")[0].strip()
            addr_info = self._parse_ip_addr_output(addr_section)
        
        # 4. 解析流量统计
        first_stats = self._parse_stats_section(output, "==STATS_FIRST_START==", "==STATS_FIRST_END==", interfaces)
        second_stats = self._parse_stats_section(output, "==STATS_SECOND_START==", "==STATS_SECOND_END==", interfaces)
        
        # 5. 解析系统级信息
        sys_info = {}
        for iface in interfaces:
            sys_start = f"==IFACE_SYS_START:{iface}=="
            sys_end = f"==IFACE_SYS_END:{iface}=="
            
            if sys_start in output and sys_end in output:
                sys_section = output.split(sys_start)[1].split(sys_end)[0].strip()
                sys_info[iface] = self._parse_sys_info(sys_section)
        
        # 6. 合并所有信息 - 保留所有网卡，不进行过滤
        for iface in interfaces:
            link_data = link_info.get(iface, {})
            addr_data = addr_info.get(iface, {})
            sys_data = sys_info.get(iface, {})
            
            info = {
                # 基本信息
                "interface_name": iface,
                "index": link_data.get("index", 0),
                
                # 状态信息
                "state": sys_data.get("operstate", "unknown"),
                "admin_state": "up" if "UP" in link_data.get("flags", []) else "down",
                "carrier": sys_data.get("carrier", "unknown"),
                
                # 物理信息
                "mac_address": link_data.get("mac", "unknown"),
                "mtu": link_data.get("mtu", 0),
                "speed": sys_data.get("speed", "unknown"),
                "duplex": sys_data.get("duplex", "unknown"),
                
                # 网络信息
                "ip_addresses": addr_data.get("ip_addresses", []),
                "ip_address": addr_data.get("primary_ip"),
                
                # 设备类型和特性
                "type": link_data.get("type", "unknown"),
                "flags": link_data.get("flags", []),
                "qdisc": link_data.get("qdisc", "unknown"),
                "master": link_data.get("master"),
                "altname": link_data.get("altname"),
                
                # 流量统计默认值
                "rx_bytes": 0,
                "rx_mb": 0.0,
                "rx_packets": 0,
                "tx_bytes": 0,
                "tx_mb": 0.0,
                "tx_packets": 0,
                "current_rx_speed": 0.0,
                "current_tx_speed": 0.0
            }
            
            # 计算流量和速率
            if iface in second_stats:
                stats = second_stats[iface]
                info.update({
                    "rx_bytes": stats["rx_bytes"],
                    "rx_packets": stats["rx_packets"],
                    "tx_bytes": stats["tx_bytes"],
                    "tx_packets": stats["tx_packets"],
                    "rx_mb": round(stats["rx_bytes"] / (1024 * 1024), 2),
                    "tx_mb": round(stats["tx_bytes"] / (1024 * 1024), 2)
                })
                
                # 计算速率
                if iface in first_stats:
                    rx_diff = stats["rx_bytes"] - first_stats[iface]["rx_bytes"]
                    tx_diff = stats["tx_bytes"] - first_stats[iface]["tx_bytes"]
                    
                    # 处理计数器溢出
                    if rx_diff < 0:
                        rx_diff += 2**64
                    if tx_diff < 0:
                        tx_diff += 2**64
                    
                    info["current_rx_speed"] = round((rx_diff * 8) / (0.3 * 1000 * 1000), 2)
                    info["current_tx_speed"] = round((tx_diff * 8) / (0.3 * 1000 * 1000), 2)
            
            network_info[iface] = info
        
        logger.debug(f"成功解析 {len(network_info)} 个网卡的完整信息")
        return network_info
    
    def _parse_ip_link_output(self, link_output: str) -> Dict[str, Dict]:
        """解析 ip link show 的输出"""
        link_info = {}
        current_iface = None
        
        for line in link_output.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # 匹配网卡定义行：1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 ...
            if re.match(r'^\d+:', line):
                parts = line.split()
                if len(parts) >= 2:
                    index = int(parts[0].rstrip(':'))
                    iface_name = parts[1].rstrip(':')
                    
                    # 解析标志
                    flags = []
                    if '<' in line and '>' in line:
                        flags_str = line.split('<')[1].split('>')[0]
                        flags = flags_str.split(',')
                    
                    # 解析其他属性
                    mtu = 0
                    qdisc = "unknown"
                    state = "unknown"
                    master = None
                    
                    for i, part in enumerate(parts):
                        if part == "mtu" and i + 1 < len(parts):
                            try:
                                mtu = int(parts[i + 1])
                            except ValueError:
                                pass
                        elif part == "qdisc" and i + 1 < len(parts):
                            qdisc = parts[i + 1]
                        elif part == "state" and i + 1 < len(parts):
                            state = parts[i + 1]
                        elif part == "master" and i + 1 < len(parts):
                            master = parts[i + 1]
                    
                    link_info[iface_name] = {
                        "index": index,
                        "flags": flags,
                        "mtu": mtu,
                        "qdisc": qdisc,
                        "state": state,
                        "master": master,
                        "mac": "unknown",
                        "type": "unknown",
                        "altname": None
                    }
                    current_iface = iface_name
                    
            # 解析物理地址行：link/ether 00:31:11:06:26:25 brd ff:ff:ff:ff:ff:ff
            elif line.startswith("link/") and current_iface:
                parts = line.split()
                if len(parts) >= 2:
                    link_type = parts[0].split('/')[1]
                    mac_addr = parts[1]
                    
                    link_info[current_iface]["type"] = link_type
                    if mac_addr != "00:00:00:00:00:00":
                        link_info[current_iface]["mac"] = mac_addr
                        
            # 解析别名：altname enp168s0f0np0
            elif line.startswith("altname") and current_iface:
                altname = line.split("altname")[1].strip()
                link_info[current_iface]["altname"] = altname
        
        return link_info
    
    def _parse_ip_addr_output(self, addr_output: str) -> Dict[str, Dict]:
        """解析 ip addr show 的输出"""
        addr_info = {}
        current_iface = None
        
        for line in addr_output.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # 匹配网卡定义行
            if re.match(r'^\d+:', line):
                parts = line.split()
                if len(parts) >= 2:
                    iface_name = parts[1].rstrip(':')
                    addr_info[iface_name] = {
                        "ip_addresses": [],
                        "primary_ip": None
                    }
                    current_iface = iface_name
                    
            # 解析IP地址行：inet *************/24 brd ************* scope global bond0
            elif line.startswith("inet ") and current_iface:
                parts = line.split()
                if len(parts) >= 2:
                    ip_cidr = parts[1]
                    ip_addr = ip_cidr.split('/')[0]
                    
                    addr_info[current_iface]["ip_addresses"].append(ip_cidr)
                    
                    # 设置主IP（第一个非回环IP）
                    if not addr_info[current_iface]["primary_ip"] and ip_addr != "127.0.0.1":
                        addr_info[current_iface]["primary_ip"] = ip_addr
        
        return addr_info
    
    def _parse_sys_info(self, sys_section: str) -> Dict[str, str]:
        """解析系统级信息"""
        sys_info = {}
        
        for line in sys_section.split('\n'):
            if line.startswith("OPERSTATE:"):
                sys_info["operstate"] = line.split("OPERSTATE:")[1].strip()
            elif line.startswith("SPEED:"):
                speed = line.split("SPEED:")[1].strip()
                if speed != "unknown" and speed.isdigit():
                    sys_info["speed"] = f"{speed} Mbps"
                else:
                    sys_info["speed"] = "unknown"
            elif line.startswith("DUPLEX:"):
                sys_info["duplex"] = line.split("DUPLEX:")[1].strip()
            elif line.startswith("CARRIER:"):
                sys_info["carrier"] = line.split("CARRIER:")[1].strip()
        
        return sys_info
    
    def _parse_stats_section(self, output: str, start_marker: str, end_marker: str, interfaces: list) -> Dict[str, Dict]:
        """解析流量统计段"""
        stats = {}
        if start_marker in output and end_marker in output:
            stats_section = output.split(start_marker)[1].split(end_marker)[0].strip()
            for line in stats_section.split('\n'):
                if "Inter-|" in line or "face" in line or not line.strip():
                    continue
                
                for iface in interfaces:
                    if iface in line:
                        parts = line.split(':')
                        if len(parts) >= 2 and parts[0].strip() == iface:
                            data_parts = line.split()
                            try:
                                if ':' in data_parts[0]:
                                    rx_bytes, rx_packets = int(data_parts[1]), int(data_parts[2])
                                    tx_bytes, tx_packets = int(data_parts[9]), int(data_parts[10])
                                else:
                                    rx_bytes, rx_packets = int(data_parts[0]), int(data_parts[1])
                                    tx_bytes, tx_packets = int(data_parts[8]), int(data_parts[9])
                                
                                stats[iface] = {
                                    "rx_bytes": rx_bytes, "rx_packets": rx_packets,
                                    "tx_bytes": tx_bytes, "tx_packets": tx_packets
                                }
                            except (ValueError, IndexError):
                                pass
                            break
        return stats
    
    async def save_network_stats(self, ip: str, network_info: Dict[str, Any]) -> bool:
        """保存网卡统计数据到数据库"""
        if not network_info:
            logger.warning(f"没有网卡数据可保存: {ip}")
            return False
        
        try:
            active_interfaces = sum(1 for info in network_info.values() if info["state"] == "up")
            
            # 保存摘要
            summary = NetworkStatsSummary(
                ip=ip,
                total_interfaces=len(network_info),
                active_interfaces=active_interfaces
            )
            await summary.save()
            
            # 保存详细信息 - 保存所有网卡
            for iface, info in network_info.items():
                detail = NetworkStatsDetail(
                    summary=summary,
                    interface_name=iface,
                    state=info["state"],
                    mac_address=info["mac_address"],
                    speed=info["speed"],
                    ip_address=info["ip_address"],
                    rx_bytes=info["rx_bytes"],
                    rx_mb=info["rx_mb"],
                    rx_packets=info["rx_packets"],
                    tx_bytes=info["tx_bytes"],
                    tx_mb=info["tx_mb"],
                    tx_packets=info["tx_packets"],
                    current_rx_speed=info["current_rx_speed"],
                    current_tx_speed=info["current_tx_speed"]
                )
                await detail.save()
                
            logger.debug(f"成功保存服务器 {ip} 的 {len(network_info)} 个网卡统计数据到数据库")
            return True
        except Exception as e:
            logger.error(f"保存网卡统计数据失败: {str(e)}")
            return False
    
    async def start_monitoring(self, interval_seconds: int = None) -> None:
        """开始定时监控所有服务器网络"""
        await super().start_monitoring(
            data_collector=self.get_network_info,
            data_saver=self.save_network_stats,
            only_connectable=True,
            interval_seconds=interval_seconds
        )

# 向后兼容函数
async def start_monitoring(interval_seconds: int = 300, skip_problem_interfaces: bool = True) -> None:
    """开始定时监控所有服务器网卡（兼容旧版API）"""
    logger.info("通过兼容函数启动网卡监控服务")
    monitor = NetworkMonitor()
    await monitor.start_monitoring(interval_seconds)