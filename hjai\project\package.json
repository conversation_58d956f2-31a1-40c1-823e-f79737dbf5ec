{"name": "monitoring-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.38", "vue-router": "^4.2.5", "axios": "^1.6.0", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "pinia": "^2.1.7", "dayjs": "^1.11.10", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4", "@types/node": "^20.10.0"}}