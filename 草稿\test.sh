nmcli connection modify ens18 ipv4.addresses ***********/24 ipv4.dns *************** ipv4.gateway *********** ipv4.method manual  autoconnect yes
nmcli con down ens18 && nmcli con up ens18


for i in 50
do
    sudo scp -r /opt/ddn/ kgzs@10.102.11.$i:/tmp/  # 先复制到临时目录
done 


 
sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@************




for ip in 9 10 {33..56}
do
    echo -e "\033[31mIP: 10.102.11.$ip\033[0m"  
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'cat /etc/fstab' | \
    awk '
    BEGIN {printf "%-75s %8s %8s %8s %4s %s\n", "Filesystem", "Size", "Used", "Avail", "Use%", "Mounted on"}
    /10\.11\.116\.1@o2ib/ {printf "%-75s %8s %8s %8s %4s %s\n", $1, $2, $3, $4, $5, $6}'
    echo "----------------------------------------"
done



11.116.1@o2ib:***********@o2ib:***********@o2ib:***********@o2ib:/hjfs /lustre lustre defaults,_defaults,_netdev,nofail,x-systemd.mount,xsystemd.requires=lnet.service 0 0


sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@************


for ip in 9 10 {33..56}
do
    echo -e "\n\033[32m==================== IP: 10.102.11.$ip ====================\033[0m\n"
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'echo "Kgzs@2024" | sudo -S cat /etc/fstab'
    echo -e "\n\033[32m========================================================\033[0m\n\n"
done



sudo systemctl stop unattended-upgrades
sudo systemctl disable unattended-upgrades  # 永久禁用（不推荐长期使用）



VERSION_STRING=5:28.1.1-1~ubuntu.22.04~jammy
sudo apt-get install docker-ce=$VERSION_STRING docker-ce-cli=$VERSION_STRING containerd.io docker-buildx-plugin docker-compose-plugin




cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv unset interface vlan1035 ip vrf C000
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv set interface vlan1035 ip vrf C006
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv unset service dhcp-relay C000 interface vlan1035
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv set service dhcp-relay C006 interface vlan1035
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv set interface bond36 bridge domain br_default access 1035
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv config apply

cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv set interface bond16 bridge domain br_default access 1016
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv unset interface vlan1035 ip vrf C000
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv set interface vlan1035 ip vrf C006
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv unset service dhcp-relay C006 interface vlan1015
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv set service dhcp-relay C001 interface vlan1015
cumulus@Storage-SN4600C-Leaf-1:mgmt:~$ nv config apply





rsync -av   root@************:/root/resources.zip  ./




for ip in  10 {33..56} 
do 
    echo -e "\n\033[32m==================== IP: 10.102.11.$ip ====================\033[0m\n" 
    # 定义要查找的模式和替换/追加的内容
    PATTERN="***********"
    NEW_MOUNT="***********@o2ib:***********@o2ib:***********@o2ib:***********@o2ib:/hjfs /lustre lustre defaults,_netdev,x-systemd.mount,x-systemd.requires=lnet.service 0 0"
    
    # 使用ssh连接到远程服务器并执行命令
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip "echo 'Kgzs@2024' | sudo -S bash -c \"\n\
        # 检查fstab文件中是否存在以***********开头的行
        if grep -q '^$PATTERN' /etc/fstab; then
            # 如果存在，替换该行
            echo '替换已存在的挂载点配置...'
            sed -i '/^$PATTERN/c\\$NEW_MOUNT' /etc/fstab
        else
            # 如果不存在，追加新行
            echo '添加新的挂载点配置...'
            echo '$NEW_MOUNT' >> /etc/fstab
        fi
        
        # 显示更新后的fstab文件内容
        echo '更新后的fstab文件内容:' >> /tmp/fstabchange.log
        cat /etc/fstab >> /tmp/fstabchange.log
    \""
    
    echo -e "\n\033[32m========================================================\033[0m\n\n" 
done




# 定义IP数组
IPS=(
    "***********" "***********" "***********" "***********" "***********"
    "***********" "***********" "***********" "***********1" "***********2"
    "************" "***********4" "***********5" "***********6" "***********7"
    "***********8" "***********7" "***********8" "***********9" "***********0"
    "***********1" "***********2" "***********3" "***********4" "************"
    "************" "***********03" "***********04" "***********05" "***********11"
    "***********12" "***********13" "***********14" "***********15"
)

for ip in "${IPS[@]}"
do 
    echo -e "\n\033[32m==================== IP: $ip ====================\033[0m\n" 
    # 定义要查找的模式和替换/追加的内容
    PATTERN="***********"
    NEW_MOUNT="***********@o2ib:***********@o2ib:***********@o2ib:***********@o2ib:/hjfs /lustre lustre defaults,_netdev,x-systemd.mount,x-systemd.requires=lnet.service 0 0"
    
    # 使用ssh连接到远程服务器并执行命令
    sudo sshpass -p 'Admin@123_' ssh -o StrictHostKeyChecking=no root@$ip "
        # 检查fstab文件中是否存在以***********开头的行
        if grep -q '^$PATTERN' /etc/fstab; then
            # 如果存在，替换该行
            echo '替换已存在的挂载点配置...'
            sed -i '/^$PATTERN/c\\$NEW_MOUNT' /etc/fstab
        else
            # 如果不存在，追加新行
            echo '添加新的挂载点配置...'
            echo '$NEW_MOUNT' >> /etc/fstab
        fi
        
        # 显示更新后的fstab文件内容
        echo '更新后的fstab文件内容:' >> /tmp/fstabchange.log
        cat /etc/fstab >> /tmp/fstabchange.log
    "
    
    echo -e "\n\033[32m========================================================\033[0m\n\n" 
done




IPS=(
    "***********" "***********" "***********" "***********" "***********"
    "***********" "***********" "***********" "***********1" "***********2"
    "************" "***********4" "***********5" "***********6" "***********7"
    "***********8" "***********7" "***********8" "***********9" "***********0"
    "***********1" "***********2" "***********3" "***********4" "************"
    "************" "***********03" "***********04" "***********05" "***********11"
    "***********12" "***********13" "***********14" "***********15"
)

for ip in "${IPS[@]}"
do
    echo -e "\033[31mIP: $ip\033[0m"  
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@$ip 'cat /etc/fstab' | \
    awk '
    BEGIN {printf "%-75s %8s %8s %8s %4s %s\n", "Filesystem", "Size", "Used", "Avail", "Use%", "Mounted on"}
    /10\.11\.116\.1@o2ib/ {printf "%-75s %8s %8s %8s %4s %s\n", $1, $2, $3, $4, $5, $6}'
    echo "----------------------------------------"
done



for ip in "${IPS[@]}"
do
    echo -e "\033[31mIP: $ip\033[0m"  
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@$ip 'df -h' | \
    awk '
    BEGIN {printf "%-75s %8s %8s %8s %4s %s\n", "Filesystem", "Size", "Used", "Avail", "Use%", "Mounted on"}
    /10\.11\.116\.1@o2ib/ {printf "%-75s %8s %8s %8s %4s %s\n", $1, $2, $3, $4, $5, $6}'
    echo "----------------------------------------"
done




for ip in 9 10 {33..56}
do
    echo -e "\n\033[32m==================== IP: 10.102.11.$ip ====================\033[0m\n"
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'echo "Kgzs@2024" | sudo -S cat /etc/fstab'
    echo -e "\n\033[32m========================================================\033[0m\n\n"
done



for ip in 9 10 {33..56}
do
    echo -e "\033[31mIP: $ip\033[0m"  
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'cat /etc/systemd/system/exa-client-deploy.service' | \
    awk '/^ExecStart/ {print $0}'
    echo "----------------------------------------"
done


for ip in 9 10 {33..56}
do
    echo -e "\033[31mIP: $ip\033[0m"  
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'df -h' | \
    awk '
    BEGIN {printf "%-75s %8s %8s %8s %4s %s\n", "Filesystem", "Size", "Used", "Avail", "Use%", "Mounted on"}
    /10\.11\.116\.1@o2ib/ {printf "%-75s %8s %8s %8s %4s %s\n", $1, $2, $3, $4, $5, $6}'
    echo "----------------------------------------"
done

for ip in "${IPS[@]}"
do
    echo -e "\033[31mIP: $ip\033[0m"
    # 显示服务配置文件中的 ExecStart 行
    sudo sshpass -p 'Admin@123_' ssh -o StrictHostKeyChecking=no root@$ip 'cat /etc/systemd/system/exa-client-deploy.service' | \
    awk '/^ExecStart/ {print $0}'
    echo "----------------------------------------"
done




IPS=(
    "***********" "***********" "***********" "***********" "***********"
    "***********" "***********" "***********" "***********1" "***********2"
    "************" "***********4" "***********5" "***********6" "***********7"
    "***********8" "***********7" "***********8" "***********9" "***********0"
    "***********1" "***********2" "***********3" "***********4" "************"
    "************" "***********03" "***********04" "***********05" "***********11"
    "***********12" "***********13" "***********14" "***********15"
)

for ip in "${IPS[@]}"
do
    echo -e "\033[31mIP: $ip\033[0m"
    # 执行 lctl list_nids 命令并显示结果
    sudo sshpass -p 'Admin@123_' ssh -o StrictHostKeyChecking=no root@$ip "
        lctl list_nids
    "
    echo "----------------------------------------"
done

IP: 9
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 10
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 33
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 34
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 35
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 36
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 37
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 38
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 39
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 40
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 41
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 42
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 43
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 44
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 45
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0,enp86s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 46
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 47
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 48
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 49
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 50
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 51
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 52
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 53
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 54
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 55
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------
IP: 56
ExecStart=/opt/ddn/exascaler/exa_client_deploy -c -l "o2ib(enp162s0np0)" --yes --skip-ro-tuning
----------------------------------------


37,38  

for ip in 9 10 {33..56}
do
    echo -e "\033[31mIP: $ip\033[0m"
    # 执行 lctl list_nids 命令并显示结果
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip "
        echo 'Kgzs@2024' | sudo -S lctl list_nids
    "
    echo "----------------------------------------"
done




IP: 9
[sudo] password for kgzs: 
[sudo] password for kgzs: ***********@o2ib
----------------------------------------
IP: 10
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 33
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 34
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 35
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 36
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 37
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 38
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 39
[sudo] password for kgzs: ************@o2ib
************@o2ib
----------------------------------------
IP: 40
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 41
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 42
[sudo] password for kgzs: ************@o2ib
************@o2ib
----------------------------------------
IP: 43
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 44
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 45
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 46
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 47
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 48
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 49
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 50
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 51
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 52
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 53
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 54
[sudo] password for kgzs: ************@o2ib
----------------------------------------
IP: 55
[sudo] password for kgzs: *************@o2ib
----------------------------------------
IP: 56
[sudo] password for kgzs: ************@o2ib
----------------------------------------



df -h | 
    awk '
    BEGIN {printf "%-75s %8s %8s %8s %4s %s\n", "Filesystem", "Size", "Used", "Avail", "Use%", "Mounted on"}
    /10\.11\.116\.1@o2ib.*\/lustre$/ {printf "%-75s %8s %8s %8s %4s %s\n", $1, $2, $3, $4, $5, $6}'

df -h|grep grep IP_ADDRESS@o2ib