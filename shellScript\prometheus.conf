# my global config
global:
  scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
# Here it's Prometheus itself.
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: "prometheus"

    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.

    static_configs:
      - targets: ["localhost:9090"]

  - job_name: 'dcgm-explorer'

    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.

    static_configs:
      # hostname:port. dcgm_prometheus.py will publish to port 8000 by default. If you use -p to change this
      # when invoking the script, be sure to update it here
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']
      - targets: ['************:9400']

  - job_name: 'ipmi_exporter'
    params:
      module: ['default']
    scrape_interval: 1m
    scrape_timeout: 30s
    metrics_path: /ipmi
    scheme: http
    file_sd_configs:
      - files:
          - /etc/prometheus/ipmi_targets.yml
        refresh_interval: 5m
    relabel_configs:
      - source_labels: [__address__]
        separator: ;
        regex: (.*)
        target_label: __param_target
        replacement: ${1}
        action: replace
      - source_labels: [__param_target]
        separator: ;
        regex: (.*)
        target_label: instance
        replacement: ${1}
        action: replace
      - separator: ;
        regex: .*
        target_label: __address__
        replacement: localhost:9290
        action: replace 
