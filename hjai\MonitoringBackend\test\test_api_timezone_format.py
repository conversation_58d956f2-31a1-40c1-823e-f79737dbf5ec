"""
测试API时区格式修复

验证所有API响应中的timestamp字段都显示为上海时区格式（+08:00）
"""

import os
import sys
import asyncio
import json
from datetime import datetime, timezone

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

async def test_pydantic_models():
    """测试Pydantic模型的JSON编码器"""
    from config.timezone_utils import TZ
    
    print("🧪 测试Pydantic模型JSON编码器...")
    
    # 创建一个UTC时间用于测试
    test_utc_time = datetime(2025, 6, 25, 6, 30, 0, tzinfo=timezone.utc)
    print(f"测试UTC时间: {test_utc_time}")
    
    # 测试内存统计响应模型
    from routers.memory_stats import MemoryStatsResponse
    memory_response = MemoryStatsResponse(
        ip="*************",
        timestamp=test_utc_time,
        usage_percent=75.5
    )
    memory_json = memory_response.model_dump_json()
    print(f"✅ 内存统计响应: {memory_json}")
    assert "+08:00" in memory_json, "内存统计响应应包含+08:00时区"
    assert "14:30:00" in memory_json, "应显示上海时区时间14:30"
    
    # 测试GPU统计响应模型
    from models.gpu_stats import GPUStatsResponse
    gpu_response = GPUStatsResponse(
        ip="*************",
        timestamp=test_utc_time,
        avg_usage=80.0,
        total_memory_used=8192.0,
        total_memory_total=16384.0,
        memory_usage_percent=50.0,
        avg_temperature=65.0
    )
    gpu_json = gpu_response.model_dump_json()
    print(f"✅ GPU统计响应: {gpu_json}")
    assert "+08:00" in gpu_json, "GPU统计响应应包含+08:00时区"
    assert "14:30:00" in gpu_json, "应显示上海时区时间14:30"
    
    # 测试自定义监控响应模型
    from routers.custom_monitor import MonitorDataResponse
    monitor_response = MonitorDataResponse(
        id=1,
        monitor_item_id=1,
        monitor_item_name="测试监控项",
        ip="*************",
        timestamp=test_utc_time,
        value="test_value",
        status=0
    )
    monitor_json = monitor_response.model_dump_json()
    print(f"✅ 自定义监控响应: {monitor_json}")
    assert "+08:00" in monitor_json, "自定义监控响应应包含+08:00时区"
    assert "14:30:00" in monitor_json, "应显示上海时区时间14:30"
    
    return True

def test_custom_json_response():
    """测试自定义JSON响应类"""
    from main import CustomJSONResponse
    from config.timezone_utils import TZ
    
    print("\n🧪 测试CustomJSONResponse...")
    
    # 创建测试数据
    test_utc_time = datetime(2025, 6, 25, 6, 30, 0, tzinfo=timezone.utc)
    test_data = {
        "ip": "*************",
        "timestamp": test_utc_time,
        "value": 123.45
    }
    
    # 使用CustomJSONResponse渲染
    response = CustomJSONResponse(content=test_data)
    json_bytes = response.render(test_data)
    json_str = json_bytes.decode('utf-8')
    
    print(f"✅ CustomJSONResponse输出: {json_str}")
    assert "+08:00" in json_str, "CustomJSONResponse应包含+08:00时区"
    assert "14:30:00" in json_str, "应显示上海时区时间14:30"
    
    return True

def test_timezone_utils():
    """测试时区工具类"""
    from config.timezone_utils import TZ, ModernJSONEncoder
    
    print("\n🧪 测试时区工具类...")
    
    # 测试UTC时间转换为显示格式
    test_utc_time = datetime(2025, 6, 25, 6, 30, 0, tzinfo=timezone.utc)
    display_format = TZ.to_display_format(test_utc_time)
    
    print(f"✅ UTC时间: {test_utc_time}")
    print(f"✅ 显示格式: {display_format}")
    assert "+08:00" in display_format, "显示格式应包含+08:00时区"
    assert "14:30:00" in display_format, "应显示上海时区时间14:30"
    
    # 测试ModernJSONEncoder
    test_data = {"timestamp": test_utc_time, "value": 100}
    json_str = json.dumps(test_data, cls=ModernJSONEncoder)
    
    print(f"✅ ModernJSONEncoder输出: {json_str}")
    assert "+08:00" in json_str, "ModernJSONEncoder应包含+08:00时区"
    assert "14:30:00" in json_str, "应显示上海时区时间14:30"
    
    return True

def test_database_time_conversion():
    """测试数据库时间转换"""
    from config.timezone_utils import TZ
    
    print("\n🧪 测试数据库时间转换...")
    
    # 模拟从数据库读取的UTC时间（带时区信息）
    db_utc_time = datetime(2025, 6, 25, 6, 30, 0, tzinfo=timezone.utc)
    
    # 转换为显示格式
    display_format = TZ.to_display_format(db_utc_time)
    
    print(f"✅ 数据库UTC时间: {db_utc_time}")
    print(f"✅ 显示格式: {display_format}")
    assert "+08:00" in display_format, "数据库时间显示格式应包含+08:00时区"
    assert "14:30:00" in display_format, "应显示上海时区时间14:30"
    
    # 测试naive datetime（旧数据兼容性）
    naive_utc_time = datetime(2025, 6, 25, 6, 30, 0)  # 没有时区信息
    display_format_naive = TZ.to_display_format(naive_utc_time, source_tz=TZ.UTC)
    
    print(f"✅ Naive UTC时间: {naive_utc_time}")
    print(f"✅ 显示格式: {display_format_naive}")
    assert "+08:00" in display_format_naive, "Naive时间显示格式应包含+08:00时区"
    assert "14:30:00" in display_format_naive, "应显示上海时区时间14:30"
    
    return True

def test_edge_cases():
    """测试边界情况"""
    from config.timezone_utils import TZ
    
    print("\n🧪 测试边界情况...")
    
    # 测试None值
    none_result = TZ.to_display_format(None)
    assert none_result is None, "None值应返回None"
    print("✅ None值处理正确")
    
    # 测试跨日期的时间转换
    utc_late_night = datetime(2025, 6, 25, 20, 0, 0, tzinfo=timezone.utc)
    display_late = TZ.to_display_format(utc_late_night)
    
    print(f"✅ UTC深夜时间: {utc_late_night}")
    print(f"✅ 上海时区显示: {display_late}")
    assert "+08:00" in display_late, "深夜时间应包含+08:00时区"
    assert "2025-06-26T04:00:00" in display_late, "应显示第二天的上海时区时间"
    
    return True

async def main():
    """主函数"""
    print("🔧 开始测试API时区格式修复...")
    print("=" * 60)
    
    tests = [
        ("时区工具类", test_timezone_utils),
        ("数据库时间转换", test_database_time_conversion),
        ("CustomJSONResponse", test_custom_json_response),
        ("Pydantic模型", test_pydantic_models),
        ("边界情况", test_edge_cases),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API时区格式测试通过！")
        print("\n📝 修复总结:")
        print("1. ✅ 所有Pydantic模型使用统一的时区编码器")
        print("2. ✅ CustomJSONResponse正确处理时区转换")
        print("3. ✅ 所有timestamp字段显示为+08:00格式")
        print("4. ✅ 兼容新旧数据格式")
        print("5. ✅ 边界情况处理正确")
        return True
    else:
        print("⚠️  部分测试失败，请检查修复。")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
