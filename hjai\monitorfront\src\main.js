import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 引入 Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 引入自定义样式
import '@/assets/styles/index.less'

// 引入主题管理
import { initTheme } from '@/utils/theme'

Vue.config.productionTip = false

// 使用 Element UI
Vue.use(ElementUI)

// 全局错误处理
Vue.config.errorHandler = function (err, vm, info) {
  console.error('Vue全局错误:', err, info)
}

// 初始化主题
initTheme()

// 打印当前环境和版本信息
console.log('Vue 版本:', Vue.version)
console.log('环境:', process.env.NODE_ENV)
console.log('Element UI 已加载')

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
