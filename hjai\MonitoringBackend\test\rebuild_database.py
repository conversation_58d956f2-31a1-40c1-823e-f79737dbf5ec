import os
import sys
import asyncio
import logging
import aiomysql
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入配置
from config.db import TORTOISE_ORM, init_db
from tortoise import Tortoise

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

async def drop_and_rebuild():
    """删除并重建数据库表"""
    logger.warning("准备删除并重建所有数据库表...")
    
    # 获取数据库连接信息
    db_config = TORTOISE_ORM["connections"]["default"]["credentials"]
    
    # 直接使用aiomysql连接数据库，因为我们需要在Tortoise初始化前执行一些操作
    conn = await aiomysql.connect(
        host=db_config["host"],
        port=db_config["port"],
        user=db_config["user"],
        password=db_config["password"],
        db=db_config["database"],
    )
    
    # 获取当前所有表
    async with conn.cursor() as cursor:
        # 先禁用外键检查以避免删除顺序问题
        await cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # 获取所有表
        await cursor.execute(f"SHOW TABLES FROM {db_config['database']}")
        tables = await cursor.fetchall()
        
        # 逐个删除表
        for table in tables:
            table_name = table[0]
            logger.warning(f"正在删除表: {table_name}")
            await cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        
        # 恢复外键检查
        await cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
    
    # 关闭连接
    conn.close()
    
    logger.info("所有表已删除，准备重建...")
    
    # 初始化ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 创建全新的数据库表结构
    logger.info("正在生成新的表结构...")
    await Tortoise.generate_schemas(safe=False)
    logger.info("数据库表结构已重建")
    
    # 关闭ORM连接
    await Tortoise.close_connections()

async def main():
    """主函数"""
    try:
        # 询问确认
        print("警告: 此操作将删除所有表并重建数据库结构，所有数据将丢失!")
        confirm = input("确认继续? (Y/N): ").strip().upper()
        
        if confirm != "Y":
            print("操作已取消")
            return
            
        # 执行删除和重建
        await drop_and_rebuild()
        print("数据库重建成功!")
        
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 