"""
统一时区处理工具模块

提供标准化的时区处理功能，遵循以下原则：
1. 内部统一使用UTC时间进行计算和存储
2. 仅在用户界面输入/输出时进行时区转换
3. 所有时区转换逻辑集中在此模块中
4. 确保时区处理的一致性和可靠性
"""

import os
import time
from datetime import datetime, timezone, timedelta
from zoneinfo import ZoneInfo
import json
from typing import Any, Optional, Union
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 时区常量
UTC = timezone.utc
SHANGHAI_TZ = ZoneInfo("Asia/Shanghai")
SHANGHAI_OFFSET = timedelta(hours=8)

class TimezoneUtils:
    """统一时区处理工具类"""

    @staticmethod
    def utc_now() -> datetime:
        """
        获取当前UTC时间

        Returns:
            datetime: 当前UTC时间（带UTC时区信息）
        """
        return datetime.now(UTC)

    @staticmethod
    def utc_now_naive() -> datetime:
        """
        获取当前UTC时间（不带时区信息）

        Returns:
            datetime: 当前UTC时间（不带时区信息）
        """
        return datetime.utcnow()

    @staticmethod
    def shanghai_now() -> datetime:
        """
        获取当前上海时区时间

        Returns:
            datetime: 当前上海时区时间（带时区信息）
        """
        return datetime.now(SHANGHAI_TZ)

    @staticmethod
    def shanghai_now_naive() -> datetime:
        """
        获取当前上海时区时间（不带时区信息）

        Returns:
            datetime: 当前上海时区时间（不带时区信息）
        """
        utc_time = datetime.utcnow()
        return utc_time + SHANGHAI_OFFSET

    @staticmethod
    def to_utc(dt: datetime, assume_shanghai: bool = True) -> datetime:
        """
        将datetime转换为UTC时间

        Args:
            dt: 要转换的datetime对象
            assume_shanghai: 如果dt没有时区信息，是否假设为上海时区

        Returns:
            datetime: UTC时间（带UTC时区信息）
        """
        if dt is None:
            return None

        if dt.tzinfo is None:
            if assume_shanghai:
                # 假设输入是上海时区时间
                dt = dt.replace(tzinfo=SHANGHAI_TZ)
            else:
                # 假设输入是UTC时间
                dt = dt.replace(tzinfo=UTC)

        return dt.astimezone(UTC)

    @staticmethod
    def to_shanghai(dt: datetime, assume_utc: bool = True) -> datetime:
        """
        将datetime转换为上海时区时间

        Args:
            dt: 要转换的datetime对象
            assume_utc: 如果dt没有时区信息，是否假设为UTC时间

        Returns:
            datetime: 上海时区时间（带时区信息）
        """
        if dt is None:
            return None

        if dt.tzinfo is None:
            if assume_utc:
                # 假设输入是UTC时间
                dt = dt.replace(tzinfo=UTC)
            else:
                # 假设输入是上海时区时间
                dt = dt.replace(tzinfo=SHANGHAI_TZ)

        return dt.astimezone(SHANGHAI_TZ)

    @staticmethod
    def to_shanghai_naive(dt: datetime, assume_utc: bool = True) -> datetime:
        """
        将datetime转换为上海时区时间（不带时区信息）

        Args:
            dt: 要转换的datetime对象
            assume_utc: 如果dt没有时区信息，是否假设为UTC时间

        Returns:
            datetime: 上海时区时间（不带时区信息）
        """
        shanghai_dt = TimezoneUtils.to_shanghai(dt, assume_utc)
        return shanghai_dt.replace(tzinfo=None) if shanghai_dt else None

    @staticmethod
    def to_shanghai_display(dt: datetime, assume_utc: bool = True) -> str:
        """
        将datetime转换为上海时区显示格式

        Args:
            dt: 要转换的datetime对象
            assume_utc: 如果dt没有时区信息，是否假设为UTC时间

        Returns:
            str: 上海时区ISO格式字符串，如 "2023-12-25T14:30:00+08:00"
        """
        if dt is None:
            return None

        shanghai_dt = TimezoneUtils.to_shanghai(dt, assume_utc)
        return shanghai_dt.isoformat()

    @staticmethod
    def from_shanghai_input(dt_str: str) -> datetime:
        """
        解析上海时区输入字符串为UTC时间

        Args:
            dt_str: 时间字符串，假设为上海时区

        Returns:
            datetime: UTC时间（带UTC时区信息）
        """
        if not dt_str:
            return None

        try:
            # 尝试解析ISO格式
            if '+' in dt_str or dt_str.endswith('Z'):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            else:
                # 没有时区信息，假设为上海时区
                dt = datetime.fromisoformat(dt_str)
                dt = dt.replace(tzinfo=SHANGHAI_TZ)

            return dt.astimezone(UTC)
        except ValueError as e:
            logger.error(f"解析时间字符串失败: {dt_str}, 错误: {e}")
            raise ValueError(f"无效的时间格式: {dt_str}")

    @staticmethod
    def add_shanghai_tz(dt: datetime) -> datetime:
        """
        为naive datetime添加上海时区信息

        Args:
            dt: 不带时区信息的datetime对象

        Returns:
            datetime: 带上海时区信息的datetime对象
        """
        if dt is None:
            return None

        if dt.tzinfo is not None:
            return dt

        return dt.replace(tzinfo=SHANGHAI_TZ)

    @staticmethod
    def remove_tz(dt: datetime) -> datetime:
        """
        移除datetime的时区信息

        Args:
            dt: 带时区信息的datetime对象

        Returns:
            datetime: 不带时区信息的datetime对象
        """
        if dt is None:
            return None

        return dt.replace(tzinfo=None)

# JSON编码器
class UTCToShanghaiEncoder(json.JSONEncoder):
    """
    将UTC时间转换为上海时区显示的JSON编码器
    """
    def default(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            return TimezoneUtils.to_shanghai_display(obj, assume_utc=True)
        return super().default(obj)

# 向后兼容的函数（保持现有API不变）
def now() -> datetime:
    """获取当前上海时区时间（不带时区信息）- 向后兼容"""
    return TimezoneUtils.shanghai_now_naive()

def utcnow() -> datetime:
    """获取当前UTC时间（不带时区信息）- 向后兼容"""
    return TimezoneUtils.utc_now_naive()

def add_tz(dt: datetime) -> datetime:
    """为datetime添加上海时区信息 - 向后兼容"""
    return TimezoneUtils.add_shanghai_tz(dt)

def remove_tz(dt: datetime) -> datetime:
    """移除datetime的时区信息 - 向后兼容"""
    return TimezoneUtils.remove_tz(dt)

def convert_to_shanghai(dt: datetime) -> datetime:
    """将任意时区datetime转换为上海时区（不带时区信息）- 向后兼容"""
    return TimezoneUtils.to_shanghai_naive(dt, assume_utc=True)

def fix_db_time(dt: datetime) -> datetime:
    """修复从数据库返回的时间 - 向后兼容"""
    if dt is None:
        return None
    # 假设数据库返回的是UTC时间，转换为上海时区显示
    return TimezoneUtils.to_shanghai_naive(dt, assume_utc=True)

# 新的JSON编码器（向后兼容）
class DateTimeWithTZEncoder(json.JSONEncoder):
    """向后兼容的JSON编码器"""
    def default(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            return TimezoneUtils.to_shanghai_display(obj, assume_utc=True)
        return super().default(obj)

def datetime_with_tz_encoder(dt: datetime) -> str:
    """向后兼容的时间编码函数"""
    return TimezoneUtils.to_shanghai_display(dt, assume_utc=True)

# 设置环境变量（保持向后兼容）
os.environ['TZ'] = 'Asia/Shanghai'
try:
    if hasattr(time, 'tzset'):
        time.tzset()
except Exception:
    pass

def setup_global_timezone():
    """向后兼容的设置函数"""
    pass

def verify_timezone_settings():
    """验证时区设置"""
    utc_time = TimezoneUtils.utc_now()
    shanghai_time = TimezoneUtils.shanghai_now()

    print(f"当前UTC时间: {utc_time}")
    print(f"当前上海时间: {shanghai_time}")
    print(f"时差: {(shanghai_time.hour - utc_time.hour) % 24}小时")
    print("✅ 新的时区工具类已就绪")

# 自动验证（仅在直接运行时）
if __name__ == "__main__":
    verify_timezone_settings()