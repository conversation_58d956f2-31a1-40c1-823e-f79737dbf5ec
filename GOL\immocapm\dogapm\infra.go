package dogapm

import (
	"context"
	"database/sql"

	_ "github.com/go-sql-driver/mysql"
	"github.com/redis/go-redis/v9"
)

type Infra struct {
	Db *sql.DB
	//Redis
	Rdb *redis.Client
}

type InfraOption func(i *Infra)

func InfraDbOption(connectUrl string) InfraOption {
	return func(i *Infra) {
		db, err := sql.Open("mysql", connectUrl)
		if err != nil {
			panic(err)
		}
		err = db.Ping()
		if err != nil {
			panic(err)
		}

		i.Db = db
	}
}

func InfraRedisOption(addr string) InfraOption {
	return func(i *Infra) {
		rdb := redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: "", // no password set
			DB:       0,  // use default DB
		})
		res, err := rdb.Ping(context.TODO()).Result()
		if err != nil {
			panic(err)
		}
		if res != "PONG" {
			panic("redis ping error")
		}

		i.Rdb = rdb
	}
}

var infra = &Infra{}

func (i *Infra) Init(opts ...InfraOption) {
	for _, opt := range opts {
		opt(i)
	}
}
