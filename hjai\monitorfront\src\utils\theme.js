/**
 * 主题管理工具
 * 用于切换深色/浅色主题
 */

// 主题类型
export const THEME_TYPE = {
  LIGHT: 'light',
  DARK: 'dark'
}

// 本地存储键名
const THEME_KEY = 'monitor_theme'

/**
 * 获取当前主题
 * @returns {string} - 主题类型
 */
export function getTheme () {
  return localStorage.getItem(THEME_KEY) || THEME_TYPE.LIGHT
}

/**
 * 设置主题
 * @param {string} theme - 主题类型
 */
export function setTheme (theme) {
  // 保存到本地存储
  localStorage.setItem(THEME_KEY, theme)

  // 应用主题
  applyTheme(theme)

  // 触发主题变化事件
  dispatchThemeChangeEvent(theme)
}

/**
 * 切换主题
 * @returns {string} - 切换后的主题类型
 */
export function toggleTheme () {
  const currentTheme = getTheme()
  const newTheme = currentTheme === THEME_TYPE.LIGHT ? THEME_TYPE.DARK : THEME_TYPE.LIGHT
  setTheme(newTheme)
  return newTheme
}

/**
 * 应用主题
 * @param {string} theme - 主题类型
 */
export function applyTheme (theme) {
  // 移除所有主题相关的类
  document.body.classList.remove('light-theme', 'dark-theme')

  // 添加当前主题类
  document.body.classList.add(`${theme}-theme`)

  // 设置 ECharts 主题
  window.ECHARTS_THEME = theme === THEME_TYPE.DARK ? 'dark' : ''
}

/**
 * 触发主题变化事件
 * @param {string} theme - 主题类型
 */
export function dispatchThemeChangeEvent (theme) {
  // 创建自定义事件
  const event = new CustomEvent('themeChange', {
    detail: { theme }
  })

  // 触发事件
  document.dispatchEvent(event)
}

/**
 * 初始化主题
 */
export function initTheme () {
  // 获取保存的主题或使用默认主题
  const savedTheme = getTheme()

  // 应用主题
  applyTheme(savedTheme)
}
