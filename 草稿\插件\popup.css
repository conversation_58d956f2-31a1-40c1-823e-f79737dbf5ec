* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  width: 340px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  animation: slideIn 0.3s ease-out;
  transition: opacity 0.5s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 24px;
  text-align: center;
  letter-spacing: -0.5px;
  position: relative;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.input-group {
  margin-bottom: 20px;
}

label {
  display: block;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #4a5568;
  position: relative;
}

label::before {
  content: '⏱️';
  margin-right: 8px;
  font-size: 16px;
}

.input-with-icon {
  position: relative;
}

input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid transparent;
  border-radius: 12px;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #667eea, #764ba2) border-box;
  font-weight: 500;
}

input:focus {
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

input::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.input-hint {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #9ca3af;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 500;
}

.button-group {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

button {
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

button:hover::before {
  transform: translateX(0);
}

.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(102, 126, 234, 0.4);
}

.primary:active {
  transform: translateY(1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.25);
}

.danger {
  background: linear-gradient(135deg, #f6365d, #ff5e54);
  color: white;
  box-shadow: 0 8px 20px rgba(246, 54, 93, 0.3);
}

.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(246, 54, 93, 0.4);
}

.danger:active {
  transform: translateY(1px);
  box-shadow: 0 5px 15px rgba(246, 54, 93, 0.25);
}

.status-bar {
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  font-size: 14px;
  color: #4a5568;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.5);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: gradientMove 3s linear infinite;
}

@keyframes gradientMove {
  0% { background-position: 0% 0%; }
  100% { background-position: 200% 0%; }
}

.icon {
  margin-right: 10px;
  font-size: 16px;
  position: relative;
  z-index: 2;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.5), transparent);
}

.version {
  padding: 5px 10px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  color: #667eea;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.5);
}