/**
 * 音效管理器
 * 负责音效的加载、播放和管理
 */
import { AUDIO_CONFIG } from '../config/gameConfig';

class AudioManager {
  constructor() {
    this.audioContext = null;
    this.audioBuffers = new Map();
    this.audioInstances = new Map();
    this.enabled = true;
    this.backgroundMusic = null;
    this.isBackgroundPlaying = false;
    
    // 初始化音频上下文
    this.initAudioContext();
    
    // 预加载音效
    this.preloadAudio();
  }
  
  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    try {
      // 微信小游戏环境下创建音频上下文
      if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
        this.audioContext = wx.createInnerAudioContext();
      }
    } catch (error) {
      console.warn('音频上下文初始化失败:', error);
    }
  }
  
  /**
   * 预加载音效文件
   */
  async preloadAudio() {
    const audioFiles = AUDIO_CONFIG.files;
    
    const loadPromises = Object.entries(audioFiles).map(async ([key, path]) => {
      try {
        await this.loadAudio(key, path);
        console.log(`音效加载成功: ${key}`);
      } catch (error) {
        console.warn(`音效加载失败: ${key}`, error);
        // 即使加载失败也创建一个静默的音频对象
        const mockAudio = {
          play: () => {},
          pause: () => {},
          stop: () => {},
          volume: 1,
          loop: false,
          onEnded: () => {},
          onError: () => {}
        };
        this.audioInstances.set(key, mockAudio);
      }
    });
    
    await Promise.allSettled(loadPromises);
    console.log('音效预加载完成');
  }
  
  /**
   * 加载单个音效文件
   * @param {string} key - 音效键名
   * @param {string} path - 音效文件路径
   */
  loadAudio(key, path) {
    return new Promise((resolve, reject) => {
      if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
        const audio = wx.createInnerAudioContext();
        audio.src = path;
        
        audio.onCanplay(() => {
          this.audioInstances.set(key, audio);
          resolve(audio);
        });
        
        audio.onError((error) => {
          console.warn(`音效文件不存在或加载失败: ${key} (${path})，使用静默模式`);
          // 创建静默的模拟音频对象
          const mockAudio = {
            play: () => {},
            pause: () => {},
            stop: () => {},
            volume: 1,
            loop: false,
            onEnded: () => {},
            onError: () => {}
          };
          this.audioInstances.set(key, mockAudio);
          resolve(mockAudio);
        });
      } else {
        // 非微信环境，创建模拟音频对象
        const mockAudio = {
          play: () => {},
          pause: () => {},
          stop: () => {},
          volume: 1,
          loop: false,
          onEnded: () => {},
          onError: () => {}
        };
        this.audioInstances.set(key, mockAudio);
        resolve(mockAudio);
      }
    });
  }
  
  /**
   * 播放音效
   * @param {string} key - 音效键名
   * @param {Object} options - 播放选项
   */
  playSound(key, options = {}) {
    if (!this.enabled) return;
    
    const audio = this.audioInstances.get(key);
    if (!audio) {
      console.warn(`音效不存在: ${key}`);
      return;
    }
    
    try {
      // 设置音量
      const volume = options.volume || AUDIO_CONFIG.volume.effects;
      audio.volume = volume;
      
      // 设置循环
      if (options.loop !== undefined) {
        audio.loop = options.loop;
      }
      
      // 播放音效
      audio.play();
      
      // 如果设置了回调，添加结束监听
      if (options.onEnd) {
        audio.onEnded(options.onEnd);
      }
    } catch (error) {
      console.warn(`音效播放失败: ${key}`, error);
    }
  }
  
  /**
   * 播放背景音乐
   * @param {string} key - 音效键名
   */
  playBackgroundMusic(key = 'background') {
    if (!this.enabled) return;
    
    // 停止当前背景音乐
    this.stopBackgroundMusic();
    
    const audio = this.audioInstances.get(key);
    if (!audio) {
      console.warn(`背景音乐不存在: ${key}`);
      return;
    }
    
    try {
      audio.volume = AUDIO_CONFIG.volume.background;
      audio.loop = true;
      audio.play();
      
      this.backgroundMusic = audio;
      this.isBackgroundPlaying = true;
    } catch (error) {
      console.warn(`背景音乐播放失败: ${key}`, error);
    }
  }
  
  /**
   * 停止背景音乐
   */
  stopBackgroundMusic() {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.stop();
      } catch (error) {
        console.warn('停止背景音乐失败:', error);
      }
      this.backgroundMusic = null;
      this.isBackgroundPlaying = false;
    }
  }
  
  /**
   * 暂停背景音乐
   */
  pauseBackgroundMusic() {
    if (this.backgroundMusic && this.isBackgroundPlaying) {
      try {
        this.backgroundMusic.pause();
        this.isBackgroundPlaying = false;
      } catch (error) {
        console.warn('暂停背景音乐失败:', error);
      }
    }
  }
  
  /**
   * 恢复背景音乐
   */
  resumeBackgroundMusic() {
    if (this.backgroundMusic && !this.isBackgroundPlaying) {
      try {
        this.backgroundMusic.play();
        this.isBackgroundPlaying = true;
      } catch (error) {
        console.warn('恢复背景音乐失败:', error);
      }
    }
  }
  
  /**
   * 停止所有音效
   */
  stopAllSounds() {
    this.audioInstances.forEach((audio, key) => {
      try {
        audio.stop();
      } catch (error) {
        console.warn(`停止音效失败: ${key}`, error);
      }
    });
    
    this.stopBackgroundMusic();
  }
  
  /**
   * 设置音效开关
   * @param {boolean} enabled - 是否启用音效
   */
  setEnabled(enabled) {
    this.enabled = enabled;
    
    if (!enabled) {
      this.stopAllSounds();
    }
  }
  
  /**
   * 获取音效开关状态
   */
  isEnabled() {
    return this.enabled;
  }
  
  /**
   * 设置音量
   * @param {string} type - 音量类型 ('background' | 'effects')
   * @param {number} volume - 音量值 (0-1)
   */
  setVolume(type, volume) {
    AUDIO_CONFIG.volume[type] = Math.max(0, Math.min(1, volume));
    
    if (type === 'background' && this.backgroundMusic) {
      this.backgroundMusic.volume = AUDIO_CONFIG.volume.background;
    }
  }
  
  /**
   * 销毁音效管理器
   */
  destroy() {
    this.stopAllSounds();
    
    this.audioInstances.forEach((audio) => {
      try {
        audio.destroy && audio.destroy();
      } catch (error) {
        console.warn('销毁音效失败:', error);
      }
    });
    
    this.audioInstances.clear();
    this.audioBuffers.clear();
  }
}

// 创建单例实例
const audioManager = new AudioManager();

export default audioManager;