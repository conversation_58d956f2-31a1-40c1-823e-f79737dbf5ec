package main

import "fmt"

// Counter 结构体表示一个计数器
type Counter struct {
	Count int
}

// IncrementByValue 是一个值接收者方法
// 它接收 Counter 的一个副本，修改副本的 Count 不会影响原始 Counter
func (c Counter) IncrementByValue(amount int) {
	// c 是原始 Counter 的一个副本
	c.Count += amount
	fmt.Printf("IncrementByValue: 内部 Count = %d\n", c.Count)
}

// IncrementByPointer 是一个指针接收者方法
// 它接收 Counter 的指针，修改指针指向的 Count 会影响原始 Counter
func (c *Counter) IncrementByPointer(amount int) {
	// c 是指向原始 Counter 的指针
	c.Count += amount // Go 会自动解引用，等同于 (*c).Count += amount
	fmt.Printf("IncrementByPointer: 内部 Count = %d\n", c.Count)
}

// GetCount 是一个值接收者方法，用于获取 Count 值 (只读操作，适合值接收者)
func (c Counter) GetCount() int {
	return c.Count
}

func main() {
	// 创建一个 Counter 结构体值实例
	counterVal := Counter{Count: 10}
	fmt.Println("初始 counterVal:", counterVal.GetCount()) // 输出: 初始 counterVal: 10

	// 调用值接收者方法
	counterVal.IncrementByValue(5)
	counterVal.Count += 10
	fmt.Println("我的调用", counterVal.Count)
	fmt.Println("调用 IncrementByValue 后 counterVal:", counterVal.GetCount()) // 输出: 调用 IncrementByValue 后 counterVal: 10 (原始值未改变)

	fmt.Println("--------------------")

	// 创建一个 Counter 结构体指针实例
	counterPtr := &Counter{Count: 10}
	fmt.Println("初始 counterPtr:", counterPtr.GetCount()) // 输出: 初始 counterPtr: 10 (注意：即使接收者是值类型，也可以通过指针调用，Go 会自动处理)

	// 调用指针接收者方法
	counterPtr.IncrementByPointer(5)
	fmt.Println("调用 IncrementByPointer 后 counterPtr:", counterPtr.GetCount()) // 输出: 调用 IncrementByPointer 后 counterPtr: 15 (原始值已改变)

	fmt.Println("--------------------")

	// 即使是结构体值，也可以调用指针接收者方法
	// Go 会自动获取 counterVal 的地址并传递给方法
	counterVal.IncrementByPointer(3)
	fmt.Println("通过值调用 IncrementByPointer 后 counterVal:", counterVal.GetCount()) // 输出: 通过值调用 IncrementByPointer 后 counterVal: 13 (原始值已改变)
}
