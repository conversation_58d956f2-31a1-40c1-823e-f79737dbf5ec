"""
监控状态管理器

管理监控项和服务器的状态，提供状态查询、更新和告警功能
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Set
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json

from config.timezone_utils import TZ
from core.error_handler import get_error_handler, ErrorLevel

logger = logging.getLogger(__name__)


class MonitorStatus(Enum):
    """监控状态"""
    UNKNOWN = "unknown"
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    TIMEOUT = "timeout"
    DISABLED = "disabled"


class ServerStatus(Enum):
    """服务器状态"""
    UNKNOWN = "unknown"
    ONLINE = "online"
    OFFLINE = "offline"
    UNREACHABLE = "unreachable"
    MAINTENANCE = "maintenance"


@dataclass
class MonitorItemStatus:
    """监控项状态"""
    monitor_item_id: int
    monitor_item_name: str
    status: MonitorStatus
    last_check_time: datetime
    last_success_time: Optional[datetime] = None
    consecutive_failures: int = 0
    total_checks: int = 0
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    last_error: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    def update_success(self, execution_time: float):
        """更新成功状态"""
        self.status = MonitorStatus.HEALTHY
        self.last_check_time = TZ.now_utc()
        self.last_success_time = self.last_check_time
        self.consecutive_failures = 0
        self.total_checks += 1
        self.last_error = None
        
        # 更新平均执行时间
        if self.average_execution_time == 0:
            self.average_execution_time = execution_time
        else:
            self.average_execution_time = (self.average_execution_time + execution_time) / 2
        
        # 更新成功率
        self._update_success_rate()
    
    def update_failure(self, error_message: str, status: MonitorStatus = MonitorStatus.ERROR):
        """更新失败状态"""
        self.status = status
        self.last_check_time = TZ.now_utc()
        self.consecutive_failures += 1
        self.total_checks += 1
        self.last_error = error_message
        
        # 更新成功率
        self._update_success_rate()
    
    def _update_success_rate(self):
        """更新成功率"""
        if self.total_checks > 0:
            success_count = self.total_checks - self.consecutive_failures
            self.success_rate = (success_count / self.total_checks) * 100
    
    def is_healthy(self) -> bool:
        """检查是否健康"""
        return self.status == MonitorStatus.HEALTHY
    
    def needs_attention(self) -> bool:
        """检查是否需要关注"""
        return self.status in [MonitorStatus.ERROR, MonitorStatus.TIMEOUT] or self.consecutive_failures >= 3


@dataclass
class ServerMonitorStatus:
    """服务器监控状态"""
    server_ip: str
    server_status: ServerStatus
    last_check_time: datetime
    monitor_items: Dict[int, MonitorItemStatus] = field(default_factory=dict)
    total_monitor_items: int = 0
    healthy_items: int = 0
    warning_items: int = 0
    error_items: int = 0
    overall_health_score: float = 0.0
    context: Dict[str, Any] = field(default_factory=dict)
    
    def add_monitor_item(self, monitor_item_status: MonitorItemStatus):
        """添加监控项状态"""
        self.monitor_items[monitor_item_status.monitor_item_id] = monitor_item_status
        self._update_statistics()
    
    def update_monitor_item(self, monitor_item_id: int, status_update: Dict[str, Any]):
        """更新监控项状态"""
        if monitor_item_id in self.monitor_items:
            item_status = self.monitor_items[monitor_item_id]
            
            if status_update.get('success', False):
                item_status.update_success(status_update.get('execution_time', 0))
            else:
                error_message = status_update.get('error', 'Unknown error')
                status = MonitorStatus.ERROR
                if 'timeout' in error_message.lower():
                    status = MonitorStatus.TIMEOUT
                item_status.update_failure(error_message, status)
            
            self._update_statistics()
    
    def _update_statistics(self):
        """更新统计信息"""
        self.total_monitor_items = len(self.monitor_items)
        self.healthy_items = sum(1 for item in self.monitor_items.values() if item.status == MonitorStatus.HEALTHY)
        self.warning_items = sum(1 for item in self.monitor_items.values() if item.status == MonitorStatus.WARNING)
        self.error_items = sum(1 for item in self.monitor_items.values() if item.status in [MonitorStatus.ERROR, MonitorStatus.TIMEOUT])
        
        # 计算整体健康分数
        if self.total_monitor_items > 0:
            self.overall_health_score = (self.healthy_items / self.total_monitor_items) * 100
        else:
            self.overall_health_score = 0.0
        
        # 更新服务器状态
        if self.error_items > self.total_monitor_items * 0.5:
            self.server_status = ServerStatus.OFFLINE
        elif self.error_items > 0 or self.warning_items > 0:
            self.server_status = ServerStatus.UNREACHABLE
        else:
            self.server_status = ServerStatus.ONLINE
    
    def get_problematic_items(self) -> List[MonitorItemStatus]:
        """获取有问题的监控项"""
        return [item for item in self.monitor_items.values() if item.needs_attention()]


class MonitorStatusManager:
    """监控状态管理器"""
    
    def __init__(self):
        self.server_statuses: Dict[str, ServerMonitorStatus] = {}
        self.error_handler = get_error_handler()
        self.alert_thresholds = {
            'consecutive_failures': 3,
            'success_rate_threshold': 80.0,
            'health_score_threshold': 70.0
        }
        
        # 状态统计
        self.global_stats = {
            'total_servers': 0,
            'online_servers': 0,
            'offline_servers': 0,
            'total_monitor_items': 0,
            'healthy_items': 0,
            'warning_items': 0,
            'error_items': 0,
            'overall_health_score': 0.0,
            'last_update_time': None
        }
    
    def get_or_create_server_status(self, server_ip: str) -> ServerMonitorStatus:
        """获取或创建服务器状态"""
        if server_ip not in self.server_statuses:
            self.server_statuses[server_ip] = ServerMonitorStatus(
                server_ip=server_ip,
                server_status=ServerStatus.UNKNOWN,
                last_check_time=TZ.now_utc()
            )
        return self.server_statuses[server_ip]
    
    def update_monitor_item_status(
        self,
        server_ip: str,
        monitor_item_id: int,
        monitor_item_name: str,
        success: bool,
        error_message: Optional[str] = None,
        execution_time: float = 0.0
    ):
        """更新监控项状态"""
        server_status = self.get_or_create_server_status(server_ip)
        
        # 获取或创建监控项状态
        if monitor_item_id not in server_status.monitor_items:
            item_status = MonitorItemStatus(
                monitor_item_id=monitor_item_id,
                monitor_item_name=monitor_item_name,
                status=MonitorStatus.UNKNOWN,
                last_check_time=TZ.now_utc()
            )
            server_status.add_monitor_item(item_status)
        
        # 更新状态
        status_update = {
            'success': success,
            'error': error_message,
            'execution_time': execution_time
        }
        server_status.update_monitor_item(monitor_item_id, status_update)
        
        # 更新全局统计
        self._update_global_stats()
        
        # 检查是否需要告警
        self._check_alerts(server_ip, monitor_item_id)
    
    def update_server_status(self, server_ip: str, status: ServerStatus):
        """更新服务器状态"""
        server_status = self.get_or_create_server_status(server_ip)
        server_status.server_status = status
        server_status.last_check_time = TZ.now_utc()
        
        self._update_global_stats()
    
    def get_server_status(self, server_ip: str) -> Optional[ServerMonitorStatus]:
        """获取服务器状态"""
        return self.server_statuses.get(server_ip)
    
    def get_monitor_item_status(self, server_ip: str, monitor_item_id: int) -> Optional[MonitorItemStatus]:
        """获取监控项状态"""
        server_status = self.server_statuses.get(server_ip)
        if server_status:
            return server_status.monitor_items.get(monitor_item_id)
        return None
    
    def get_all_server_statuses(self) -> Dict[str, ServerMonitorStatus]:
        """获取所有服务器状态"""
        return self.server_statuses.copy()
    
    def get_problematic_servers(self) -> List[ServerMonitorStatus]:
        """获取有问题的服务器"""
        return [
            server for server in self.server_statuses.values()
            if server.server_status in [ServerStatus.OFFLINE, ServerStatus.UNREACHABLE]
            or server.overall_health_score < self.alert_thresholds['health_score_threshold']
        ]
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        return self.global_stats.copy()
    
    def _update_global_stats(self):
        """更新全局统计信息"""
        self.global_stats['total_servers'] = len(self.server_statuses)
        self.global_stats['online_servers'] = sum(
            1 for server in self.server_statuses.values()
            if server.server_status == ServerStatus.ONLINE
        )
        self.global_stats['offline_servers'] = sum(
            1 for server in self.server_statuses.values()
            if server.server_status in [ServerStatus.OFFLINE, ServerStatus.UNREACHABLE]
        )
        
        # 统计监控项
        total_items = 0
        healthy_items = 0
        warning_items = 0
        error_items = 0
        
        for server in self.server_statuses.values():
            total_items += server.total_monitor_items
            healthy_items += server.healthy_items
            warning_items += server.warning_items
            error_items += server.error_items
        
        self.global_stats['total_monitor_items'] = total_items
        self.global_stats['healthy_items'] = healthy_items
        self.global_stats['warning_items'] = warning_items
        self.global_stats['error_items'] = error_items
        
        # 计算整体健康分数
        if total_items > 0:
            self.global_stats['overall_health_score'] = (healthy_items / total_items) * 100
        else:
            self.global_stats['overall_health_score'] = 0.0
        
        self.global_stats['last_update_time'] = TZ.now_utc().isoformat()
    
    def _check_alerts(self, server_ip: str, monitor_item_id: int):
        """检查是否需要告警"""
        item_status = self.get_monitor_item_status(server_ip, monitor_item_id)
        if not item_status:
            return
        
        # 检查连续失败次数
        if item_status.consecutive_failures >= self.alert_thresholds['consecutive_failures']:
            self._trigger_alert(
                f"监控项连续失败",
                f"服务器 {server_ip} 的监控项 '{item_status.monitor_item_name}' 连续失败 {item_status.consecutive_failures} 次",
                ErrorLevel.HIGH,
                {
                    'server_ip': server_ip,
                    'monitor_item_id': monitor_item_id,
                    'monitor_item_name': item_status.monitor_item_name,
                    'consecutive_failures': item_status.consecutive_failures,
                    'last_error': item_status.last_error
                }
            )
        
        # 检查成功率
        if (item_status.total_checks >= 10 and 
            item_status.success_rate < self.alert_thresholds['success_rate_threshold']):
            self._trigger_alert(
                f"监控项成功率过低",
                f"服务器 {server_ip} 的监控项 '{item_status.monitor_item_name}' 成功率为 {item_status.success_rate:.1f}%",
                ErrorLevel.MEDIUM,
                {
                    'server_ip': server_ip,
                    'monitor_item_id': monitor_item_id,
                    'monitor_item_name': item_status.monitor_item_name,
                    'success_rate': item_status.success_rate,
                    'total_checks': item_status.total_checks
                }
            )
    
    def _trigger_alert(self, title: str, message: str, level: ErrorLevel, context: Dict[str, Any]):
        """触发告警"""
        logger.warning(f"ALERT: {title} - {message}")
        
        # 这里可以扩展为发送邮件、短信、webhook等告警方式
        # 目前只记录日志
        self.error_handler.handle_error(
            f"监控告警: {title}",
            context=context
        )
    
    async def cleanup_old_data(self, retention_days: int = 7):
        """清理旧数据"""
        cutoff_time = TZ.now_utc() - timedelta(days=retention_days)
        
        for server_ip, server_status in list(self.server_statuses.items()):
            if server_status.last_check_time < cutoff_time:
                # 移除长时间未更新的服务器状态
                del self.server_statuses[server_ip]
                logger.info(f"清理过期服务器状态: {server_ip}")
        
        self._update_global_stats()


# 全局状态管理器实例
_status_manager: Optional[MonitorStatusManager] = None


def get_status_manager() -> MonitorStatusManager:
    """获取全局状态管理器实例"""
    global _status_manager
    if _status_manager is None:
        _status_manager = MonitorStatusManager()
    return _status_manager
