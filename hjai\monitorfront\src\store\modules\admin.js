// 导入admin API
import {
  getAdminIpList,
  addIp,
  deleteIp,
  getMonitorItems,
  addMonitorItem,
  deleteMonitorItem,
  getIpMonitorRelations,
  bindMonitorToIp,
  unbindMonitor
} from '@/api/admin'

const state = {
  ipList: [],
  monitorItems: [],
  ipMonitorRelations: [],
  loading: false,
  error: null
}

const mutations = {
  SET_IP_LIST: (state, ipList) => {
    state.ipList = ipList
  },
  SET_MONITOR_ITEMS: (state, items) => {
    state.monitorItems = items
  },
  SET_IP_MONITOR_RELATIONS: (state, relations) => {
    state.ipMonitorRelations = relations
  },
  SET_LOADING: (state, status) => {
    state.loading = status
  },
  SET_ERROR: (state, error) => {
    state.error = error
  }
}

const actions = {
  // 获取管理界面IP列表
  fetchAdminIpList ({ commit }) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      getAdminIpList()
        .then(response => {
          commit('SET_IP_LIST', response || [])
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '获取IP列表失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 添加IP
  addIp ({ commit, dispatch }, ipData) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      addIp(ipData)
        .then(response => {
          // 添加成功后刷新列表
          dispatch('fetchAdminIpList')
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '添加IP失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 删除IP
  deleteIp ({ commit, dispatch }, ip) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      deleteIp(ip)
        .then(response => {
          // 删除成功后刷新列表
          dispatch('fetchAdminIpList')
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '删除IP失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 获取监控项列表
  fetchMonitorItems ({ commit }) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      getMonitorItems()
        .then(response => {
          commit('SET_MONITOR_ITEMS', response || [])
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '获取监控项列表失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 添加监控项
  addMonitorItem ({ commit, dispatch }, data) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      addMonitorItem(data)
        .then(response => {
          // 添加成功后刷新列表
          dispatch('fetchMonitorItems')
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '添加监控项失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 删除监控项
  deleteMonitorItem ({ commit, dispatch }, id) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      deleteMonitorItem(id)
        .then(response => {
          // 删除成功后刷新列表
          dispatch('fetchMonitorItems')
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '删除监控项失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 获取IP关联的监控项
  fetchIpMonitorRelations ({ commit }, ip) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      getIpMonitorRelations(ip)
        .then(response => {
          commit('SET_IP_MONITOR_RELATIONS', response || [])
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '获取IP关联监控项失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 绑定监控项到IP
  bindMonitorToIp ({ commit, dispatch }, data) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      bindMonitorToIp(data)
        .then(response => {
          // 绑定成功后刷新列表
          dispatch('fetchIpMonitorRelations', data.ip)
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '绑定监控项失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 解绑监控项
  unbindMonitor ({ commit, dispatch }, data) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      unbindMonitor(data)
        .then(response => {
          // 解绑成功后刷新列表
          dispatch('fetchIpMonitorRelations', data.ip)
          commit('SET_LOADING', false)
          resolve(response)
        })
        .catch(error => {
          commit('SET_ERROR', error.message || '解绑监控项失败')
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  }
}

const getters = {
  ipList: state => state.ipList,
  monitorItems: state => state.monitorItems,
  ipMonitorRelations: state => state.ipMonitorRelations,
  loading: state => state.loading,
  error: state => state.error
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
