#!/usr/bin/env python3
import subprocess
import datetime
import sys
import os
import concurrent.futures
import threading
from typing import List, Tu<PERSON>, Dict
import json
import argparse

# 配置类
class Config:
    """配置管理类"""
    def __init__(self):
        self.ROOT_IPS = [
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********1", "***********2",
            "***********3", "***********4", "***********5", "***********6", "***********7",
            "***********8", "***********7", "***********8", "***********9", "***********0",
            "***********1", "***********2", "***********3", "***********4", "************",
            "************", "************3", "************4", "************5", "***********11",
            "***********12", "***********13", "***********14", "***********15"
        ]
        
        self.KGZS_IPS = ["***********", "************"] + [f"10.102.11.{i}" for i in range(33, 57)]
        
        self.CREDENTIALS = {
            "root": {"password": "Admin@123_"},
            "kgzs": {"password": "Kgzs@2024"}
        }
        
        self.SSH_OPTIONS = "-o StrictHostKeyChecking=no -o ConnectTimeout=10 -o ServerAliveInterval=60"
        self.MAX_WORKERS = 10  # 并发连接数

class SSHManager:
    """SSH连接管理类"""
    def __init__(self, config: Config):
        self.config = config
        self.lock = threading.Lock()
        
    def run_command(self, ip: str, user: str, command: str, use_sudo: bool = False) -> Tuple[str, str, bool]:
        """
        执行SSH命令
        返回: (stdout, stderr, success)
        """
        try:
            password = self.config.CREDENTIALS[user]["password"]
            
            if user == "root":
                ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"{command}\""
            else:
                if use_sudo:
                    ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"echo '{password}' | sudo -S {command}\""
                else:
                    ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"{command}\""
            
            result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.stdout, result.stderr, result.returncode == 0
            
        except subprocess.TimeoutExpired:
            return "", f"连接超时 (IP: {ip})", False
        except Exception as e:
            return "", f"错误: {str(e)}", False

class Logger:
    """日志管理类"""
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
        self.log_filename = os.path.join(log_dir, f"{timestamp}.log")
        self.json_filename = os.path.join(log_dir, f"{timestamp}.json")
        self.lock = threading.Lock()
        self.results = []
        
    def write(self, content: str, print_console: bool = True):
        """写入日志"""
        with self.lock:
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(content)
            if print_console:
                print(content, end='')
                
    def add_result(self, result: Dict):
        """添加结果到JSON"""
        with self.lock:
            self.results.append(result)
            
    def save_json(self):
        """保存JSON结果"""
        with open(self.json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

class LustreChecker:
    """Lustre检查主类"""
    def __init__(self, config: Config, logger: Logger):
        self.config = config
        self.logger = logger
        self.ssh_manager = SSHManager(config)
        
    def check_host(self, ip: str, user: str, checks: List[str]) -> Dict:
        """检查单个主机"""
        host_result = {
            "ip": ip,
            "user": user,
            "timestamp": datetime.datetime.now().isoformat(),
            "checks": {}
        }
        
        for check in checks:
            if check == "list_nids":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "lctl list_nids", use_sudo=(user == "kgzs")
                )
                host_result["checks"]["list_nids"] = {
                    "success": success,
                    "output": stdout.strip() if success else stderr
                }
                
            elif check == "lustre_mount":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "df -h | grep -E '(lustre|o2ib)'", use_sudo=False
                )
                host_result["checks"]["lustre_mount"] = {
                    "success": success,
                    "output": stdout.strip() if success else stderr
                }
                
            elif check == "config_file":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "grep ExecStart /etc/systemd/system/exa-client-deploy.service 2>/dev/null || echo 'File not found'", 
                    use_sudo=False
                )
                host_result["checks"]["config_file"] = {
                    "success": success,
                    "output": stdout.strip() if stdout else stderr
                }
                
        return host_result
    
    def check_hosts_parallel(self, hosts: List[Tuple[str, str]], checks: List[str]):
        """并行检查多个主机"""
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.MAX_WORKERS) as executor:
            future_to_host = {
                executor.submit(self.check_host, ip, user, checks): (ip, user) 
                for ip, user in hosts
            }
            
            for future in concurrent.futures.as_completed(future_to_host):
                ip, user = future_to_host[future]
                try:
                    result = future.result()
                    self.logger.add_result(result)
                    self.format_and_log_result(result)
                except Exception as e:
                    self.logger.write(f"检查 {ip} ({user}) 时出错: {str(e)}\n")
                    
    def format_and_log_result(self, result: Dict):
        """格式化并记录结果"""
        ip = result["ip"]
        user = result["user"]
        
        self.logger.write(f"\n\033[31mIP: {ip} (用户: {user})\033[0m\n")
        
        for check_name, check_result in result["checks"].items():
            if check_result["success"]:
                self.logger.write(f"{check_name}: ✓\n")
                if check_result["output"]:
                    self.logger.write(f"{check_result['output']}\n")
            else:
                self.logger.write(f"{check_name}: ✗ - {check_result['output']}\n")
                
        self.logger.write("-" * 40 + "\n")
        
    def run_all_checks(self):
        """运行所有检查"""
        self.logger.write(f"检查开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        self.logger.write(f"日志文件: {self.logger.log_filename}\n")
        self.logger.write(f"JSON结果: {self.logger.json_filename}\n\n")
        
        # 准备主机列表
        root_hosts = [(ip, "root") for ip in self.config.ROOT_IPS]
        kgzs_hosts = [(ip, "kgzs") for ip in self.config.KGZS_IPS]
        
        # 检查1: lctl list_nids
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write("1. 检查 lctl list_nids 输出\n")
        self.logger.write("="*80 + "\n")
        
        self.logger.write("\n### Root用户设备 ###\n")
        self.check_hosts_parallel(root_hosts, ["list_nids"])
        
        self.logger.write("\n### KGZS用户设备 ###\n")
        self.check_hosts_parallel(kgzs_hosts, ["list_nids"])
        
        # 检查2: Lustre挂载
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write("2. 检查 /lustre 挂载目录\n")
        self.logger.write("="*80 + "\n")
        
        self.logger.write("\n### Root用户设备 ###\n")
        self.check_hosts_parallel(root_hosts, ["lustre_mount"])
        
        self.logger.write("\n### KGZS用户设备 ###\n")
        self.check_hosts_parallel(kgzs_hosts, ["lustre_mount"])
        
        # 检查3: 配置文件
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write("3. 检查配置文件\n")
        self.logger.write("="*80 + "\n")
        
        self.logger.write("\n### KGZS用户设备 ###\n")
        self.check_hosts_parallel(kgzs_hosts, ["config_file"])
        
        self.logger.write("\n### Root用户设备 ###\n")
        self.check_hosts_parallel(root_hosts, ["config_file"])
        
        # 保存结果
        self.logger.save_json()
        self.logger.write(f"\n检查结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        self.logger.write(f"日志已保存到: {self.logger.log_filename}\n")
        self.logger.write(f"JSON结果已保存到: {self.logger.json_filename}\n")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Lustre文件系统检查工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录")
    parser.add_argument("--max-workers", type=int, default=10, help="最大并发数")
    parser.add_argument("--config", help="配置文件路径（JSON格式）")
    
    args = parser.parse_args()
    
    # 初始化配置
    config = Config()
    if args.max_workers:
        config.MAX_WORKERS = args.max_workers
        
    # 如果提供了配置文件，从文件加载配置
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            custom_config = json.load(f)
            if "root_ips" in custom_config:
                config.ROOT_IPS = custom_config["root_ips"]
            if "kgzs_ips" in custom_config:
                config.KGZS_IPS = custom_config["kgzs_ips"]
            if "credentials" in custom_config:
                config.CREDENTIALS.update(custom_config["credentials"])
    
    # 初始化日志
    logger = Logger(args.log_dir)
    
    # 运行检查
    checker = LustreChecker(config, logger)
    checker.run_all_checks()

if __name__ == "__main__":
    main()