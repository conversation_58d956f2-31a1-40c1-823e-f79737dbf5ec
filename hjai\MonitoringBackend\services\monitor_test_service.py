"""
监控测试服务

负责监控项的测试和验证功能
"""

import logging
import re
import json
import time
import asyncio
from typing import Dict, Any, Optional, Tuple, List
import paramiko

from utils.ssh_utils import SSHConnectionManager
from .security_validator import SecurityValidator

logger = logging.getLogger(__name__)


class MonitorTestService:
    """监控测试服务类"""
    
    def __init__(self):
        self.security_validator = SecurityValidator()
    
    async def test_monitor_command(
        self,
        command: str,
        ip: str,
        credentials: Dict[str, Any],
        timeout: int = 30,
        data_type: str = "string"
    ) -> Tu<PERSON>[bool, Optional[str]]:
        """
        测试监控命令
        
        Args:
            command: 要测试的命令
            ip: 目标IP地址
            credentials: 认证凭据
            timeout: 超时时间
            data_type: 期望的数据类型
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 1. 安全性验证
            is_safe, security_error = self.security_validator.validate_command(command)
            if not is_safe:
                return False, f"命令安全验证失败: {security_error}"
            
            # 2. SSH连接测试
            ssh_client, connected = await self._test_ssh_connection(ip, credentials, timeout)
            if not connected:
                return False, f"SSH连接失败: 无法连接到 {ip}"
            
            try:
                # 3. 命令执行测试
                success, output, error, exec_time = await self._execute_test_command(
                    ssh_client, command, timeout
                )
                
                if not success:
                    return False, f"命令执行失败: {error}"
                
                # 4. 数据格式验证
                is_valid, format_error = self._validate_output_format(output, data_type)
                if not is_valid:
                    return False, f"数据格式验证失败: {format_error}"
                
                # 5. 性能检查
                if exec_time > timeout * 0.8:  # 如果执行时间超过超时时间的80%
                    logger.warning(f"命令执行时间较长: {exec_time:.2f}秒")
                
                logger.info(f"监控命令测试成功: {command} @ {ip} (耗时: {exec_time:.2f}秒)")
                return True, None
                
            finally:
                ssh_client.close()
                
        except Exception as e:
            logger.error(f"监控命令测试异常: {str(e)}")
            return False, f"测试过程异常: {str(e)}"
    
    async def _test_ssh_connection(
        self, 
        ip: str, 
        credentials: Dict[str, Any], 
        timeout: int
    ) -> Tuple[Optional[paramiko.SSHClient], bool]:
        """测试SSH连接"""
        try:
            # 构造服务器信息
            server_info = {
                'ip': ip,
                'username': credentials.get('username', 'root'),
                'password': credentials.get('password'),
                'use_ssh_key': credentials.get('use_ssh_key', False),
                'ssh_key_path': credentials.get('ssh_key_path')
            }
            
            # 在线程池中执行SSH连接
            def connect():
                return SSHConnectionManager.connect_to_server(server_info, timeout)
            
            ssh_client, connected = await asyncio.get_event_loop().run_in_executor(
                None, connect
            )
            
            return ssh_client, connected
            
        except Exception as e:
            logger.error(f"SSH连接测试失败: {str(e)}")
            return None, False
    
    async def _execute_test_command(
        self, 
        ssh_client: paramiko.SSHClient, 
        command: str, 
        timeout: int
    ) -> Tuple[bool, Optional[str], Optional[str], float]:
        """执行测试命令"""
        try:
            start_time = time.time()
            
            # 在线程池中执行命令
            def execute():
                stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
                output = stdout.read().decode('utf-8', errors='ignore').strip()
                error = stderr.read().decode('utf-8', errors='ignore').strip()
                exit_status = stdout.channel.recv_exit_status()
                return output, error, exit_status
            
            output, error, exit_status = await asyncio.get_event_loop().run_in_executor(
                None, execute
            )
            
            execution_time = time.time() - start_time
            
            if exit_status != 0:
                return False, output, error or f"命令退出状态: {exit_status}", execution_time
            
            return True, output, None, execution_time
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"命令执行异常: {str(e)}")
            return False, None, str(e), execution_time
    
    def _validate_output_format(self, output: str, data_type: str) -> Tuple[bool, Optional[str]]:
        """验证输出格式"""
        try:
            if not output:
                return False, "命令输出为空"
            
            if data_type == "string":
                # 字符串类型，基本验证
                if len(output) > 10000:  # 限制输出长度
                    return False, "输出内容过长"
                return True, None
                
            elif data_type == "number":
                # 数字类型验证
                try:
                    float(output)
                    return True, None
                except ValueError:
                    return False, f"输出不是有效数字: {output[:100]}"
                    
            elif data_type == "json":
                # JSON格式验证
                try:
                    json.loads(output)
                    return True, None
                except json.JSONDecodeError as e:
                    return False, f"输出不是有效JSON格式: {str(e)}"
                    
            elif data_type == "boolean":
                # 布尔类型验证
                if output.lower() in ['true', 'false', '1', '0', 'yes', 'no']:
                    return True, None
                else:
                    return False, f"输出不是有效布尔值: {output}"
                    
            else:
                # 未知数据类型，只做基本检查
                return True, None
                
        except Exception as e:
            return False, f"格式验证异常: {str(e)}"
    
    async def batch_test_monitor_items(
        self, 
        test_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        批量测试监控项
        
        Args:
            test_configs: 测试配置列表，每个配置包含command, ip, credentials等
            
        Returns:
            Dict[str, Any]: 测试结果汇总
        """
        results = {
            'total': len(test_configs),
            'success': 0,
            'failed': 0,
            'details': []
        }
        
        for i, config in enumerate(test_configs):
            try:
                success, error = await self.test_monitor_command(
                    command=config['command'],
                    ip=config['ip'],
                    credentials=config['credentials'],
                    timeout=config.get('timeout', 30),
                    data_type=config.get('data_type', 'string')
                )
                
                result_detail = {
                    'index': i,
                    'command': config['command'],
                    'ip': config['ip'],
                    'success': success,
                    'error': error
                }
                
                results['details'].append(result_detail)
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'index': i,
                    'command': config.get('command', 'unknown'),
                    'ip': config.get('ip', 'unknown'),
                    'success': False,
                    'error': f"测试异常: {str(e)}"
                })
        
        return results

    async def validate_monitor_config(
        self,
        name: str,
        command: str,
        data_type: str = "string",
        timeout: int = 30,
        category: Optional[str] = None
    ) -> Tuple[bool, List[str]]:
        """
        验证监控项配置

        Args:
            name: 监控项名称
            command: 监控命令
            data_type: 数据类型
            timeout: 超时时间
            category: 分类

        Returns:
            Tuple[bool, List[str]]: (是否有效, 验证消息列表)
        """
        messages = []
        is_valid = True

        # 验证名称
        if not name or not name.strip():
            messages.append("监控项名称不能为空")
            is_valid = False
        elif len(name) > 100:
            messages.append("监控项名称长度不能超过100个字符")
            is_valid = False

        # 验证命令
        if not command or not command.strip():
            messages.append("监控命令不能为空")
            is_valid = False
        else:
            # 安全性验证
            is_safe, security_error = self.security_validator.validate_command(command)
            if not is_safe:
                messages.append(f"命令安全验证失败: {security_error}")
                is_valid = False
            else:
                messages.append("命令安全验证通过")

        # 验证数据类型
        valid_data_types = ["string", "number", "json", "boolean"]
        if data_type not in valid_data_types:
            messages.append(f"数据类型必须是以下之一: {', '.join(valid_data_types)}")
            is_valid = False

        # 验证超时时间
        if timeout < 1 or timeout > 300:
            messages.append("超时时间必须在1-300秒之间")
            is_valid = False

        # 验证分类
        if category and len(category) > 50:
            messages.append("分类名称长度不能超过50个字符")
            is_valid = False

        return is_valid, messages

    def get_command_suggestions(self, category: Optional[str] = None) -> List[Dict[str, str]]:
        """
        获取命令建议

        Args:
            category: 分类过滤

        Returns:
            List[Dict[str, str]]: 命令建议列表
        """
        suggestions = [
            {
                "category": "system",
                "name": "CPU使用率",
                "command": "cat /proc/loadavg",
                "data_type": "string",
                "description": "获取系统负载信息"
            },
            {
                "category": "system",
                "name": "内存使用率",
                "command": "free -m",
                "data_type": "string",
                "description": "获取内存使用情况"
            },
            {
                "category": "system",
                "name": "磁盘使用率",
                "command": "df -h",
                "data_type": "string",
                "description": "获取磁盘使用情况"
            },
            {
                "category": "network",
                "name": "网络连接数",
                "command": "netstat -an | wc -l",
                "data_type": "number",
                "description": "获取网络连接总数"
            },
            {
                "category": "process",
                "name": "进程数量",
                "command": "ps aux | wc -l",
                "data_type": "number",
                "description": "获取运行进程数量"
            },
            {
                "category": "system",
                "name": "系统运行时间",
                "command": "uptime",
                "data_type": "string",
                "description": "获取系统运行时间"
            }
        ]

        if category:
            suggestions = [s for s in suggestions if s["category"] == category]

        return suggestions
