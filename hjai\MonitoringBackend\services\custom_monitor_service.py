"""
自定义监控项服务层

封装自定义监控项的所有业务逻辑，提供统一的服务接口
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import asyncio

from models.custom_monitor import MonitorItem, MonitorItemIP, MonitorData
from config.timezone_utils import TZ
from .monitor_test_service import MonitorTestService

logger = logging.getLogger(__name__)


class CustomMonitorService:
    """自定义监控项服务类"""
    
    def __init__(self):
        self.test_service = MonitorTestService()
    
    async def create_monitor_item(
        self, 
        name: str, 
        command: str, 
        description: Optional[str] = None,
        data_type: str = "string",
        timeout: int = 30,
        retry_count: int = 2,
        category: Optional[str] = None,
        security_level: str = "normal",
        test_ip: Optional[str] = None,
        test_credentials: Optional[Dict[str, Any]] = None
    ) -> Tuple[MonitorItem, bool, Optional[str]]:
        """
        创建新的监控项
        
        Args:
            name: 监控项名称
            command: 监控命令
            description: 描述
            data_type: 数据类型
            timeout: 超时时间
            retry_count: 重试次数
            category: 分类
            security_level: 安全级别
            test_ip: 测试IP地址
            test_credentials: 测试凭据
            
        Returns:
            Tuple[MonitorItem, bool, Optional[str]]: (监控项, 是否测试成功, 错误信息)
        """
        try:
            # 1. 检查名称是否已存在
            existing = await MonitorItem.filter(name=name).first()
            if existing:
                return None, False, f"已存在名称为 '{name}' 的监控项"

            # 2. 如果提供了测试信息，先进行测试验证
            test_success = True
            test_error = None

            if test_ip and test_credentials:
                logger.info(f"开始测试监控项 '{name}' 在服务器 {test_ip}")
                test_success, test_error = await self.test_service.test_monitor_command(
                    command=command,
                    ip=test_ip,
                    credentials=test_credentials,
                    timeout=timeout,
                    data_type=data_type
                )

                if not test_success:
                    logger.warning(f"监控项 '{name}' 测试失败: {test_error}")
                    # 根据配置决定是否允许创建测试失败的监控项
                    # 这里我们允许创建但会返回测试结果
            
            # 3. 创建监控项
            monitor_item = await MonitorItem.create(
                name=name,
                command=command,
                description=description,
                data_type=data_type,
                timeout=timeout,
                retry_count=retry_count,
                category=category,
                security_level=security_level,
                enabled=test_success  # 测试成功的监控项默认启用
            )
            
            logger.info(f"成功创建监控项: {name} (ID: {monitor_item.id})")
            return monitor_item, test_success, test_error
            
        except Exception as e:
            logger.error(f"创建监控项失败: {str(e)}")
            return None, False, f"创建监控项失败: {str(e)}"

    async def test_monitor_item_with_db_credentials(
        self,
        command: str,
        ip: str,
        timeout: int = 30,
        data_type: str = "string"
    ) -> Tuple[bool, Optional[str]]:
        """
        使用数据库中的连接信息测试监控项

        Args:
            command: 监控命令
            ip: 目标IP地址
            timeout: 超时时间
            data_type: 数据类型

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 从数据库获取服务器连接信息
            from models.ip_user import IPUser

            # 尝试使用新版表结构查询
            try:
                ip_user = await IPUser.filter(ip=ip, is_deleted=False).first()
            except Exception:
                # 如果失败，说明表中没有is_deleted列，使用旧版查询
                logger.warning("IP用户表结构未更新，使用旧版查询")
                ip_user = await IPUser.filter(ip=ip).first()

            if not ip_user:
                return False, f"未找到IP地址 {ip} 的连接信息，请先在系统中添加该服务器"

            # 构造认证凭据
            credentials = {
                'username': ip_user.username,
                'password': ip_user.password,
                'use_ssh_key': getattr(ip_user, 'use_ssh_key', not bool(ip_user.password)),
                'ssh_key_path': getattr(ip_user, 'ssh_key_path', None)
            }

            logger.info(f"使用数据库连接信息测试监控命令: {ip} (用户: {ip_user.username})")

            # 执行测试
            success, error = await self.test_service.test_monitor_command(
                command=command,
                ip=ip,
                credentials=credentials,
                timeout=timeout,
                data_type=data_type
            )

            return success, error

        except Exception as e:
            logger.error(f"使用数据库连接信息测试失败: {str(e)}")
            return False, f"测试过程异常: {str(e)}"
    
    async def update_monitor_item(
        self, 
        item_id: int, 
        **kwargs
    ) -> Tuple[bool, Optional[str]]:
        """
        更新监控项
        
        Args:
            item_id: 监控项ID
            **kwargs: 要更新的字段
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            monitor_item = await MonitorItem.filter(id=item_id).first()
            if not monitor_item:
                return False, f"未找到ID为 {item_id} 的监控项"
            
            # 更新字段
            for field, value in kwargs.items():
                if hasattr(monitor_item, field):
                    setattr(monitor_item, field, value)
            
            await monitor_item.save()
            logger.info(f"成功更新监控项: {monitor_item.name} (ID: {item_id})")
            return True, None
            
        except Exception as e:
            logger.error(f"更新监控项失败: {str(e)}")
            return False, f"更新监控项失败: {str(e)}"
    
    async def delete_monitor_item(self, item_id: int) -> Tuple[bool, Optional[str]]:
        """
        删除监控项
        
        Args:
            item_id: 监控项ID
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            monitor_item = await MonitorItem.filter(id=item_id).first()
            if not monitor_item:
                return False, f"未找到ID为 {item_id} 的监控项"
            
            # 删除相关的IP关联和监控数据
            await MonitorItemIP.filter(monitor_item_id=item_id).delete()
            await MonitorData.filter(monitor_item_id=item_id).delete()
            
            # 删除监控项
            await monitor_item.delete()
            
            logger.info(f"成功删除监控项: {monitor_item.name} (ID: {item_id})")
            return True, None
            
        except Exception as e:
            logger.error(f"删除监控项失败: {str(e)}")
            return False, f"删除监控项失败: {str(e)}"
    
    async def get_monitor_item(self, item_id: int) -> Optional[MonitorItem]:
        """获取单个监控项"""
        try:
            return await MonitorItem.filter(id=item_id).first()
        except Exception as e:
            logger.error(f"获取监控项失败: {str(e)}")
            return None
    
    async def list_monitor_items(
        self, 
        category: Optional[str] = None,
        enabled: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[MonitorItem]:
        """
        获取监控项列表
        
        Args:
            category: 分类过滤
            enabled: 启用状态过滤
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[MonitorItem]: 监控项列表
        """
        try:
            query = MonitorItem.all()
            
            if category:
                query = query.filter(category=category)
            
            if enabled is not None:
                query = query.filter(enabled=enabled)
            
            return await query.offset(offset).limit(limit)
            
        except Exception as e:
            logger.error(f"获取监控项列表失败: {str(e)}")
            return []

    async def add_monitor_item_ip(
        self,
        item_id: int,
        ip_address: str
    ) -> Tuple[bool, Optional[str]]:
        """
        为监控项添加IP地址关联

        Args:
            item_id: 监控项ID
            ip_address: IP地址

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 检查监控项是否存在
            monitor_item = await MonitorItem.filter(id=item_id).first()
            if not monitor_item:
                return False, f"未找到ID为 {item_id} 的监控项"

            # 检查是否已存在关联
            existing = await MonitorItemIP.filter(
                monitor_item_id=item_id,
                ip_address=ip_address
            ).first()
            if existing:
                return False, f"监控项 '{monitor_item.name}' 已关联IP地址 '{ip_address}'"

            # 创建关联
            await MonitorItemIP.create(
                monitor_item=monitor_item,
                ip_address=ip_address
            )

            logger.info(f"成功为监控项 '{monitor_item.name}' 添加IP关联: {ip_address}")
            return True, None

        except Exception as e:
            logger.error(f"添加IP关联失败: {str(e)}")
            return False, f"添加IP关联失败: {str(e)}"

    async def remove_monitor_item_ip(
        self,
        item_id: int,
        ip_address: str
    ) -> Tuple[bool, Optional[str]]:
        """
        移除监控项的IP地址关联

        Args:
            item_id: 监控项ID
            ip_address: IP地址

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 查找并删除关联
            deleted_count = await MonitorItemIP.filter(
                monitor_item_id=item_id,
                ip_address=ip_address
            ).delete()

            if deleted_count == 0:
                return False, f"未找到监控项ID {item_id} 与IP {ip_address} 的关联"

            logger.info(f"成功移除监控项ID {item_id} 与IP {ip_address} 的关联")
            return True, None

        except Exception as e:
            logger.error(f"移除IP关联失败: {str(e)}")
            return False, f"移除IP关联失败: {str(e)}"

    async def get_monitor_item_ips(self, item_id: int) -> List[str]:
        """
        获取监控项关联的IP地址列表

        Args:
            item_id: 监控项ID

        Returns:
            List[str]: IP地址列表
        """
        try:
            ip_relations = await MonitorItemIP.filter(monitor_item_id=item_id).all()
            return [relation.ip_address for relation in ip_relations]
        except Exception as e:
            logger.error(f"获取监控项IP列表失败: {str(e)}")
            return []

    async def save_monitor_data(
        self,
        item_id: int,
        ip: str,
        value: str,
        status: int = 0,
        execution_time: Optional[float] = None
    ) -> Tuple[bool, Optional[str]]:
        """
        保存监控数据

        Args:
            item_id: 监控项ID
            ip: IP地址
            value: 监控值
            status: 状态 (0-正常, 1-警告, 2-错误)
            execution_time: 执行时间

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 检查监控项是否存在
            monitor_item = await MonitorItem.filter(id=item_id).first()
            if not monitor_item:
                return False, f"未找到ID为 {item_id} 的监控项"

            # 创建监控数据
            await MonitorData.create_data(
                monitor_item_id=item_id,
                ip=ip,
                value=value,
                status=status
            )

            logger.debug(f"成功保存监控数据: {monitor_item.name}@{ip} = {value}")
            return True, None

        except Exception as e:
            logger.error(f"保存监控数据失败: {str(e)}")
            return False, f"保存监控数据失败: {str(e)}"

    async def get_monitor_data(
        self,
        item_id: Optional[int] = None,
        ip: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[MonitorData]:
        """
        获取监控数据

        Args:
            item_id: 监控项ID过滤
            ip: IP地址过滤
            start_time: 开始时间过滤
            end_time: 结束时间过滤
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[MonitorData]: 监控数据列表
        """
        try:
            query = MonitorData.all()

            if item_id:
                query = query.filter(monitor_item_id=item_id)

            if ip:
                query = query.filter(ip=ip)

            if start_time:
                query = query.filter(timestamp__gte=start_time)

            if end_time:
                query = query.filter(timestamp__lte=end_time)

            return await query.order_by('-timestamp').offset(offset).limit(limit)

        except Exception as e:
            logger.error(f"获取监控数据失败: {str(e)}")
            return []

    async def get_monitor_statistics(
        self,
        item_id: int,
        hours: int = 24
    ) -> Dict[str, Any]:
        """
        获取监控项统计信息

        Args:
            item_id: 监控项ID
            hours: 统计时间范围(小时)

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 计算时间范围
            end_time = TZ.now_utc()
            start_time = end_time.replace(hour=end_time.hour - hours)

            # 获取监控数据
            data_list = await MonitorData.filter(
                monitor_item_id=item_id,
                timestamp__gte=start_time,
                timestamp__lte=end_time
            ).all()

            if not data_list:
                return {
                    'total_count': 0,
                    'success_count': 0,
                    'warning_count': 0,
                    'error_count': 0,
                    'success_rate': 0.0,
                    'avg_execution_time': 0.0,
                    'last_execution': None
                }

            # 统计各种状态的数量
            total_count = len(data_list)
            success_count = sum(1 for d in data_list if d.status == 0)
            warning_count = sum(1 for d in data_list if d.status == 1)
            error_count = sum(1 for d in data_list if d.status == 2)

            # 计算成功率
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0.0

            # 获取最后执行时间
            last_execution = max(data_list, key=lambda x: x.timestamp).timestamp

            return {
                'total_count': total_count,
                'success_count': success_count,
                'warning_count': warning_count,
                'error_count': error_count,
                'success_rate': round(success_rate, 2),
                'avg_execution_time': 0.0,  # TODO: 需要添加执行时间记录
                'last_execution': last_execution
            }

        except Exception as e:
            logger.error(f"获取监控统计信息失败: {str(e)}")
            return {}

    async def enable_monitor_item(self, item_id: int) -> Tuple[bool, Optional[str]]:
        """启用监控项"""
        return await self.update_monitor_item(item_id, enabled=True)

    async def disable_monitor_item(self, item_id: int) -> Tuple[bool, Optional[str]]:
        """禁用监控项"""
        return await self.update_monitor_item(item_id, enabled=False)

    async def batch_enable_monitor_items(self, item_ids: List[int]) -> Dict[str, Any]:
        """批量启用监控项"""
        results = {'success': [], 'failed': []}

        for item_id in item_ids:
            success, error = await self.enable_monitor_item(item_id)
            if success:
                results['success'].append(item_id)
            else:
                results['failed'].append({'id': item_id, 'error': error})

        return results

    async def batch_disable_monitor_items(self, item_ids: List[int]) -> Dict[str, Any]:
        """批量禁用监控项"""
        results = {'success': [], 'failed': []}

        for item_id in item_ids:
            success, error = await self.disable_monitor_item(item_id)
            if success:
                results['success'].append(item_id)
            else:
                results['failed'].append({'id': item_id, 'error': error})

        return results
