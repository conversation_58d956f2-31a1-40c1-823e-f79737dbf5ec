{"name": "flight-chess-game", "version": "1.0.0", "description": "一个基于HTML5 Canvas的飞行棋游戏，支持单机和多人对战", "main": "js/main.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node server.js", "serve": "node server.js --port 3000", "build": "echo \"构建功能待实现\" && exit 0", "test": "echo \"测试功能待实现\" && exit 0", "lint": "echo \"代码检查功能待实现\" && exit 0"}, "keywords": ["飞行棋", "游戏", "HTML5", "<PERSON><PERSON>", "JavaScript", "微信小游戏", "棋类游戏", "多人游戏"], "author": {"name": "开发者", "email": "<EMAIL>"}, "license": "MIT", "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/username/flight-chess-game.git"}, "bugs": {"url": "https://github.com/username/flight-chess-game/issues"}, "homepage": "https://github.com/username/flight-chess-game#readme", "devDependencies": {}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <= 11"], "files": ["js/", "assets/", "index.html", "README.md"], "gameConfig": {"name": "飞行棋游戏", "version": "1.0.0", "description": "经典飞行棋游戏，支持2-4人对战", "features": ["完整的飞行棋规则实现", "支持单机AI对战", "支持本地多人游戏", "精美的游戏界面", "流畅的动画效果", "音效支持", "触屏操作优化", "响应式设计"], "requirements": {"browser": "现代浏览器（支持ES6+）", "canvas": "HTML5 Canvas支持", "javascript": "JavaScript ES6+支持"}, "controls": {"mouse": "鼠标点击操作", "touch": "触屏点击操作", "keyboard": "键盘快捷键支持"}}, "gameRules": {"players": "2-4人", "pieces": "每人4枚棋子", "objective": "率先将所有棋子送到终点", "dice": "6面骰子", "specialRules": ["投出6点可以起飞棋子或再投一次", "连续3次投出6点跳过回合", "棋子可以击落对手棋子", "同色棋子可以叠子保护", "特定格子可以跳跃或飞行"]}}