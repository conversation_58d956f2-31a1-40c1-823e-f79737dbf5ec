<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElButton, ElCard, ElTable, ElTableColumn, ElTag, ElDialog, ElForm, ElFormItem, ElInput, ElSwitch, ElMessage, ElMessageBox, ElPagination } from 'element-plus'
import { Plus, Edit, Delete, Connection, Warning } from '@element-plus/icons-vue'
import { serverAPI, type IPUser, type AddIPUserRequest, type UserListRequest } from '@/api/server'

const loading = ref(false)
const tableData = ref<IPUser[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// Dialog states
const dialogVisible = ref(false)
const editDialogVisible = ref(false)
const isEditing = ref(false)

// Form data
const form = ref<AddIPUserRequest>({
  ip: '',
  user: '',
  password: '',
  use_ssh_key: false,
  ssh_key_path: ''
})

const editForm = ref<Partial<IPUser>>({})

const formRules = {
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入有效的IP地址格式', trigger: 'blur' }
  ],
  user: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
}

const fetchUserList = async () => {
  try {
    loading.value = true
    const params: UserListRequest = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    const response = await serverAPI.getUserList(params)
    tableData.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取服务器列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  form.value = {
    ip: '',
    user: '',
    password: '',
    use_ssh_key: false,
    ssh_key_path: ''
  }
  isEditing.value = false
  dialogVisible.value = true
}

const handleEdit = (row: IPUser) => {
  editForm.value = { ...row }
  editDialogVisible.value = true
}

const handleDelete = async (row: IPUser) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务器 ${row.ip} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await serverAPI.deleteIPUser(row.ip)
    ElMessage.success('删除成功')
    fetchUserList()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const handleSubmit = async () => {
  try {
    loading.value = true
    await serverAPI.addIPUser(form.value)
    ElMessage.success('添加成功')
    dialogVisible.value = false
    fetchUserList()
  } catch (error) {
    ElMessage.error('添加失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  editDialogVisible.value = false
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchUserList()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUserList()
}

const getStatusType = (row: IPUser) => {
  if (row.is_deleted) return 'info'
  if (!row.is_connectable) return 'danger'
  return 'success'
}

const getStatusText = (row: IPUser) => {
  if (row.is_deleted) return '已删除'
  if (!row.is_connectable) return '连接失败'
  return '正常'
}

onMounted(() => {
  fetchUserList()
})
</script>

<template>
  <div class="server-management">
    <div class="page-header">
      <div class="header-content">
        <h2>服务器管理</h2>
        <p>管理监控服务器的连接信息和认证方式</p>
      </div>
      <ElButton type="primary" :icon="Plus" @click="handleAdd">
        添加服务器
      </ElButton>
    </div>

    <ElCard class="table-card">
      <ElTable 
        :data="tableData" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <ElTableColumn prop="ip" label="IP地址" width="140">
          <template #default="{ row }">
            <span class="ip-address">{{ row.ip }}</span>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="username" label="用户名" width="120" />
        
        <ElTableColumn label="认证方式" width="100">
          <template #default="{ row }">
            <ElTag :type="row.use_ssh_key ? 'success' : 'primary'" size="small">
              {{ row.use_ssh_key ? 'SSH密钥' : '密码' }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="连接状态" width="100">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row)" size="small">
              {{ getStatusText(row) }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="ssh_key_path" label="SSH密钥路径" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.ssh_key_path" class="key-path">{{ row.ssh_key_path }}</span>
            <span v-else class="no-key">-</span>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <ElButton size="small" type="primary" :icon="Edit" @click="handleEdit(row)">
                编辑
              </ElButton>
              <ElButton size="small" type="danger" :icon="Delete" @click="handleDelete(row)">
                删除
              </ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <div class="pagination-container">
        <ElPagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>

    <!-- Add/Edit Dialog -->
    <ElDialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑服务器' : '添加服务器'"
      width="500px"
      :before-close="handleCancel"
    >
      <ElForm :model="form" :rules="formRules" label-width="100px">
        <ElFormItem label="IP地址" prop="ip">
          <ElInput v-model="form.ip" placeholder="请输入服务器IP地址" />
        </ElFormItem>
        
        <ElFormItem label="用户名" prop="user">
          <ElInput v-model="form.user" placeholder="请输入用户名" />
        </ElFormItem>
        
        <ElFormItem label="认证方式">
          <ElSwitch
            v-model="form.use_ssh_key"
            active-text="SSH密钥"
            inactive-text="密码"
          />
        </ElFormItem>
        
        <ElFormItem v-if="!form.use_ssh_key" label="密码" prop="password">
          <ElInput 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码" 
            show-password 
          />
        </ElFormItem>
        
        <ElFormItem v-if="form.use_ssh_key" label="SSH密钥路径">
          <ElInput 
            v-model="form.ssh_key_path" 
            placeholder="留空使用默认路径 (~/.ssh/id_rsa)" 
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" :loading="loading" @click="handleSubmit">
            {{ isEditing ? '更新' : '添加' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.server-management {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ip-address {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 500;
  color: #3b82f6;
}

.key-path {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #6b7280;
}

.no-key {
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-content h2 {
    font-size: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    font-size: 12px;
    padding: 6px 8px;
  }
}
</style>