version: '3'
networks:
  go-ldap-admin:
    driver: bridge
services:
  # go-ldap-admin:
  #   image: registry.cn-hangzhou.aliyuncs.com/eryajf/go-ldap-admin
  #   container_name: go-ldap-admin
  #   hostname: go-ldap-admin
  #   restart: always
  #   environment:
  #     WAIT_HOSTS: mysql:3306, openldap:389
  #   ports:
  #     - 8888:8888
  #   volumes:
  #     - ./data/go-ldap-admin:/app/data
  #   depends_on:
  #     - mysql
  #     - openldap
  #   links:
  #     - mysql:go-ldap-admin-mysql
  #     - openldap:go-ldap-admin-openldap
  #   networks:
  #     - go-ldap-admin

  mysql:
    image: registry.cn-hangzhou.aliyuncs.com/eryajf/mysql:8.3
    container_name: go-ldap-admin-mysql
    hostname: go-ldap-admin-mysql
    restart: always
    ports:
      - '3306:3306'
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_ROOT_HOST: "%"
      MYSQL_DATABASE: go_ldap_admin
    volumes:
      - ./data/mysql:/var/lib/mysql
    networks:
      - go-ldap-admin

  openldap:
    image: registry.cn-hangzhou.aliyuncs.com/eryajf/openldap:1.4.1
    container_name: go-ldap-admin-openldap
    hostname: go-ldap-admin-openldap
    restart: always
    environment:
      TZ: Asia/Shanghai
      LDAP_ORGANISATION: "eryajf.net"
      LDAP_DOMAIN: "eryajf.net"
      LDAP_ADMIN_PASSWORD: "123456"
    command: [ '--copy-service' ]
    volumes:
      - ./data/openldap/database:/var/lib/ldap
      - ./data/openldap/config:/etc/ldap/slapd.d
    ports:
      - 389:389
    networks:
      - go-ldap-admin

  phpldapadmin:
    image: registry.cn-hangzhou.aliyuncs.com/eryajf/phpldapadmin:0.9.0
    container_name: go-ldap-admin-phpldapadmin
    hostname: go-ldap-admin-phpldapadmin
    restart: always
    environment:
      TZ: Asia/Shanghai
      PHPLDAPADMIN_HTTPS: "false"
      PHPLDAPADMIN_LDAP_HOSTS: go-ldap-admin-openldap
    ports:
      - 8091:80
    volumes:
      - ./data/phpadmin:/var/www/phpldapadmin
    depends_on:
      - openldap
    links:
      - openldap:go-ldap-admin-openldap
    networks:
      - go-ldap-admin