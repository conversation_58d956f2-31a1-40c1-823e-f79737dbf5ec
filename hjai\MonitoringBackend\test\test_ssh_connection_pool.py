"""
SSH连接池测试

测试SSH连接池的各项功能
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
import paramiko

from utils.ssh_connection_pool import SSHConnectionPool, SSHConnectionInfo


class TestSSHConnectionPool:
    """SSH连接池测试类"""
    
    @pytest.fixture
    async def pool(self):
        """创建连接池实例"""
        pool = SSHConnectionPool(
            max_connections_per_server=2,
            max_total_connections=10,
            max_idle_time=60,
            max_lifetime=300,
            health_check_interval=30
        )
        await pool.start()
        yield pool
        await pool.stop()
    
    @pytest.fixture
    def mock_ssh_client(self):
        """模拟SSH客户端"""
        client = Mock(spec=paramiko.SSHClient)
        transport = Mock()
        transport.is_active.return_value = True
        client.get_transport.return_value = transport
        client.close = Mock()
        client.exec_command.return_value = (<PERSON><PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>())
        return client
    
    @pytest.fixture
    def server_info(self):
        """服务器信息"""
        return {
            'ip': '*************',
            'username': 'test',
            'password': 'test123'
        }
    
    def test_generate_server_key(self, pool, server_info):
        """测试服务器密钥生成"""
        key = pool._generate_server_key(server_info)
        assert key == "*************:test"
        
        # 测试默认用户名
        server_no_username = {'ip': '*************'}
        key = pool._generate_server_key(server_no_username)
        assert key == "*************:root"
    
    @pytest.mark.asyncio
    async def test_pool_start_stop(self):
        """测试连接池启动和停止"""
        pool = SSHConnectionPool()
        
        # 测试启动
        await pool.start()
        assert pool._running is True
        assert pool._health_check_task is not None
        
        # 测试停止
        await pool.stop()
        assert pool._running is False
        assert len(pool.pools) == 0
        assert pool.stats['total_connections'] == 0
    
    @pytest.mark.asyncio
    async def test_get_connection_success(self, pool, server_info, mock_ssh_client):
        """测试成功获取连接"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            async with pool.get_connection(server_info) as ssh_client:
                assert ssh_client is mock_ssh_client
                assert pool.stats['total_connections'] == 1
                assert pool.stats['pool_misses'] == 1
    
    @pytest.mark.asyncio
    async def test_get_connection_failure(self, pool, server_info):
        """测试连接获取失败"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (None, False)
            
            with pytest.raises(Exception, match="无法获取SSH连接"):
                async with pool.get_connection(server_info) as ssh_client:
                    pass
    
    @pytest.mark.asyncio
    async def test_connection_reuse(self, pool, server_info, mock_ssh_client):
        """测试连接复用"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            # 第一次获取连接
            async with pool.get_connection(server_info) as ssh_client1:
                assert ssh_client1 is mock_ssh_client
            
            # 第二次获取连接应该复用
            async with pool.get_connection(server_info) as ssh_client2:
                assert ssh_client2 is mock_ssh_client
                assert pool.stats['pool_hits'] == 1
                assert pool.stats['pool_misses'] == 1  # 只有第一次是miss
    
    @pytest.mark.asyncio
    async def test_max_connections_per_server(self, pool, server_info, mock_ssh_client):
        """测试单服务器最大连接数限制"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            # 获取最大数量的连接
            connections = []
            for i in range(pool.max_connections_per_server):
                conn_context = pool.get_connection(server_info)
                connections.append(conn_context)
            
            # 尝试获取超出限制的连接
            with pytest.raises(Exception):
                async with pool.get_connection(server_info) as ssh_client:
                    pass
    
    @pytest.mark.asyncio
    async def test_max_total_connections(self, server_info, mock_ssh_client):
        """测试总连接数限制"""
        pool = SSHConnectionPool(max_total_connections=2, max_connections_per_server=2)
        await pool.start()
        
        try:
            with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
                mock_connect.return_value = (mock_ssh_client, True)
                
                # 创建两个不同的服务器
                server1 = {'ip': '*************', 'username': 'test'}
                server2 = {'ip': '*************', 'username': 'test'}
                
                # 获取最大数量的连接
                async with pool.get_connection(server1):
                    async with pool.get_connection(server2):
                        # 尝试获取超出总限制的连接
                        with pytest.raises(Exception):
                            async with pool.get_connection(server1):
                                pass
        finally:
            await pool.stop()
    
    def test_connection_info_expiry(self):
        """测试连接信息过期检查"""
        mock_client = Mock()
        
        # 创建连接信息
        conn_info = SSHConnectionInfo(
            client=mock_client,
            server_key="test:user",
            created_at=time.time() - 3700  # 超过1小时
        )
        
        # 测试生命周期过期
        assert conn_info.is_expired(max_idle_time=300, max_lifetime=3600) is True
        
        # 测试空闲时间过期
        conn_info.created_at = time.time()
        conn_info.last_used = time.time() - 400  # 超过5分钟空闲
        assert conn_info.is_expired(max_idle_time=300, max_lifetime=3600) is True
        
        # 测试未过期
        conn_info.last_used = time.time()
        assert conn_info.is_expired(max_idle_time=300, max_lifetime=3600) is False
    
    def test_connection_info_active_check(self, mock_ssh_client):
        """测试连接活跃状态检查"""
        conn_info = SSHConnectionInfo(
            client=mock_ssh_client,
            server_key="test:user",
            created_at=time.time()
        )
        
        # 测试活跃连接
        assert conn_info.is_active() is True
        
        # 测试非活跃连接
        mock_ssh_client.get_transport.return_value.is_active.return_value = False
        assert conn_info.is_active() is False
        
        # 测试异常情况
        mock_ssh_client.get_transport.side_effect = Exception("Connection error")
        assert conn_info.is_active() is False
    
    def test_connection_info_usage_update(self, mock_ssh_client):
        """测试连接使用信息更新"""
        conn_info = SSHConnectionInfo(
            client=mock_ssh_client,
            server_key="test:user",
            created_at=time.time()
        )
        
        initial_use_count = conn_info.use_count
        initial_last_used = conn_info.last_used
        
        time.sleep(0.1)  # 确保时间差异
        conn_info.update_usage()
        
        assert conn_info.use_count == initial_use_count + 1
        assert conn_info.last_used > initial_last_used
    
    @pytest.mark.asyncio
    async def test_health_check(self, pool, server_info, mock_ssh_client):
        """测试健康检查"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            # 设置健康检查模拟
            stdout_mock = Mock()
            stdout_mock.read.return_value = b"health_check"
            stdout_mock.channel.recv_exit_status.return_value = 0
            mock_ssh_client.exec_command.return_value = (Mock(), stdout_mock, Mock())
            
            # 获取连接
            async with pool.get_connection(server_info) as ssh_client:
                pass
            
            # 执行健康检查
            await pool._perform_health_check()
            
            # 验证连接仍然存在（健康检查通过）
            server_key = pool._generate_server_key(server_info)
            assert server_key in pool.pools
            assert len(pool.pools[server_key]) == 1
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, pool, server_info, mock_ssh_client):
        """测试健康检查失败"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            # 设置健康检查失败
            mock_ssh_client.exec_command.side_effect = Exception("Health check failed")
            
            # 获取连接
            async with pool.get_connection(server_info) as ssh_client:
                pass
            
            # 执行健康检查
            await pool._perform_health_check()
            
            # 验证不健康的连接被移除
            server_key = pool._generate_server_key(server_info)
            assert server_key not in pool.pools or len(pool.pools[server_key]) == 0
            assert pool.stats['health_check_failures'] > 0
    
    @pytest.mark.asyncio
    async def test_clear_expired_connections(self, pool, server_info, mock_ssh_client):
        """测试清理过期连接"""
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            # 获取连接
            async with pool.get_connection(server_info) as ssh_client:
                pass
            
            # 手动设置连接为过期
            server_key = pool._generate_server_key(server_info)
            conn_info = pool.pools[server_key][0]
            conn_info.created_at = time.time() - 4000  # 设置为过期
            
            # 清理过期连接
            await pool.clear_expired_connections()
            
            # 验证过期连接被清理
            assert server_key not in pool.pools or len(pool.pools[server_key]) == 0
    
    @pytest.mark.asyncio
    async def test_warm_up_connections(self, pool, mock_ssh_client):
        """测试连接预热"""
        servers = [
            {'ip': '*************', 'username': 'test'},
            {'ip': '*************', 'username': 'test'}
        ]
        
        with patch('utils.ssh_utils.SSHConnectionManager.connect_to_server') as mock_connect:
            mock_connect.return_value = (mock_ssh_client, True)
            
            # 预热连接
            await pool.warm_up_connections(servers, connections_per_server=1)
            
            # 验证连接被创建
            assert len(pool.pools) == 2
            for server in servers:
                server_key = pool._generate_server_key(server)
                assert server_key in pool.pools
                assert len(pool.pools[server_key]) == 1
    
    def test_get_stats(self, pool):
        """测试获取统计信息"""
        stats = pool.get_stats()
        
        required_keys = [
            'total_connections', 'active_connections', 'pool_hits', 'pool_misses',
            'connection_errors', 'health_check_failures', 'pool_info', 'total_servers'
        ]
        
        for key in required_keys:
            assert key in stats
        
        assert isinstance(stats['pool_info'], dict)
        assert isinstance(stats['total_servers'], int)
