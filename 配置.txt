#创建vlan
vlan 32

#interface vlan
DUT2 (config)# interface vlan 32
DUT2 (config-if)# ip address ************/24
Switch(config-if)# no switchport     


DUT2 (config)# interface vlan 32
DUT2 (config-if)# ip address ************/24
Switch(config-if)# no switchport     



#vrrp

Switch# configure terminal
Switch(config)# router vrrp 32
Switch(config-router)#virtual-ip  ************
Switch(config-router)#interface  vlan32
Switch(config-router)# preempt-mode true
Switch(config-router)# advertisement-interval 5
Switch(config-router)# enable


Switch# configure terminal
Switch(config)# router vrrp 32
Switch(config-router)#virtual-ip  ************
Switch(config-router)#interface   vlan32  
Switch(config-router)# preempt-mode true
Switch(config-router)# advertisement-interval 5
Switch(config-router)# enable


#1
Switch1 (config)# interface eth-0-1
Switch1(config-if)# static-channel-group 1
Switch1(config-if)# no shutdown
Switch1(config-if)# exit

Switch1 (config)# interface agg32
Switch1(config-if)# switchport mode trunk
Switch1(config-if)# switchport trunk allowed vlan add 32
Switch1(config-if)# mlag 32
Switch1(config-if)# exit


#2
Switch2 (config)# interface eth-0-1
Switch2(config-if)# static-channel-group 1
Switch2(config-if)# no shutdown
Switch2(config-if)# exit

Switch2 (config)# interface agg1
Switch2(config-if)# switchport mode trunk
Switch2(config-if)# switchport trunk allowed vlan add 32
Switch2(config-if)# mlag 32
Switch2(config-if)# exit


