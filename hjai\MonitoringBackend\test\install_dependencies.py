import os
import sys
import subprocess
import platform

def install_dependencies():
    """安装项目所需的依赖"""
    print("检查并安装项目依赖...")
    
    # 要安装的依赖
    dependencies = [
        "fastapi==0.109.2",
        "uvicorn==0.27.1",
        "tortoise-orm==0.20.0",
        "aiomysql==0.2.0",
        "pydantic==2.5.3",
        "pytz==2023.3",
        "python-dotenv==1.0.0",
        "asyncio==3.4.3",
        "aiohttp==3.9.1",
    ]
    
    # 根据操作系统选择合适的命令
    system = platform.system()
    pip_command = "pip"
    
    if system == "Windows":
        # Windows可能需要使用 python -m pip
        pip_command = "python -m pip"
    else:
        # Linux/Mac可能需要使用 pip3
        pip_command = "pip3"
    
    # 执行安装命令
    try:
        print(f"使用 {pip_command} 安装依赖...")
        
        # 构建完整的命令
        full_command = f"{pip_command} install --upgrade " + " ".join(dependencies)
        
        # 输出将要执行的命令
        print(f"执行: {full_command}")
        
        # 执行命令
        result = subprocess.run(full_command, shell=True, check=True)
        
        print("依赖安装完成!")
        
    except subprocess.CalledProcessError as e:
        print(f"安装依赖时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    install_dependencies() 