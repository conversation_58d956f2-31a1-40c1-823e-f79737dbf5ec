// 简单的Toast工具，不依赖Vue实例

// 创建样式
const style = document.createElement('style')
style.textContent = `
  .toast-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.75);
    color: #fff;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
    z-index: 9999;
    max-width: 80%;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: opacity 0.3s, transform 0.3s;
    opacity: 1;
  }
  
  .toast-message.toast-fade-enter {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  
  .toast-message.toast-fade-leave {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
`
document.head.appendChild(style)

// 现有的toast元素
let toastElement = null
let hideTimer = null

// 纯JS实现的toast函数
function showToast (message, duration = 3000) {
  // 如果已经存在toast，先移除旧的
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }

  if (toastElement) {
    document.body.removeChild(toastElement)
    toastElement = null
  }

  // 创建新的toast元素
  toastElement = document.createElement('div')
  toastElement.className = 'toast-message toast-fade-enter'
  toastElement.textContent = message
  document.body.appendChild(toastElement)

  // 触发重绘以应用初始样式
  // eslint-disable-next-line no-unused-expressions
  toastElement.offsetHeight

  // 移除进入动画类
  setTimeout(() => {
    if (toastElement) {
      toastElement.classList.remove('toast-fade-enter')
    }
  }, 20)

  // 设置定时器隐藏toast
  hideTimer = setTimeout(() => {
    if (toastElement) {
      toastElement.classList.add('toast-fade-leave')

      // 动画结束后移除元素
      setTimeout(() => {
        if (toastElement && toastElement.parentNode) {
          document.body.removeChild(toastElement)
          toastElement = null
        }
      }, 300) // 与CSS过渡时间一致
    }
  }, duration)

  // 返回清除函数，便于立即清除
  return clearToast
}

// 清除所有toast的函数
function clearToast () {
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }

  if (toastElement) {
    toastElement.classList.add('toast-fade-leave')

    setTimeout(() => {
      if (toastElement && toastElement.parentNode) {
        document.body.removeChild(toastElement)
        toastElement = null
      }
    }, 300)
  }
}

// 导出toast函数和clear函数
export const toast = showToast
export const clear = clearToast

// 导出Vue插件
export default {
  install (Vue) {
    Vue.prototype.$toast = showToast
    Vue.prototype.$clearToast = clearToast
  }
}
