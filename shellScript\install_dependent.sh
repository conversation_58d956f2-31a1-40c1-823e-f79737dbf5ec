#!/bin/bash
# 安装依赖脚本

# 重置终端设置，确保退格键正常工作
stty sane
stty erase ^H

# 全局变量
LOG_FILE="/tmp/install_dependent.log"
VERSION="1.0.0"

# 日志函数
log() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

# 错误处理函数
handle_error() {
    log "错误: $1"
    exit 1
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -v, --version  显示版本信息"
    exit 0
}

# 显示版本信息
show_version() {
    echo "安装依赖脚本 v$VERSION"
    exit 0
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                show_help
                ;;
            -v|--version)
                show_version
                ;;
            *)
                log "未知选项: $1"
                show_help
                ;;
        esac
        shift
    done
}

# 配置时区
configure_timezone() {
    log "检查时区配置..."
    local current_timezone=$(timedatectl | grep "Time zone" | awk '{print $3}')

    if [ "$current_timezone" != "Asia/Shanghai" ]; then
        log "设置时区为 Asia/Shanghai"
        sudo timedatectl set-timezone Asia/Shanghai || handle_error "无法设置时区"
        log "时区已更改为 Asia/Shanghai"
    else
        log "当前时区已经是 Asia/Shanghai"
    fi
}

# 安装依赖包
install_dependencies() {
    log "更新软件包列表..."
    sudo apt update || handle_error "apt update 失败"
    
    log "安装依赖包..."
    sudo apt install -y nfs-common pkg-config flex libltdl-dev libgfortran5 gfortran m4 libfuse2 dkms \
    graphviz debhelper libnl-3-dev chrpath quilt automake tk autotools-dev swig bison autoconf \
    libnl-route-3-dev libtool libboost-program-options-dev gnupg curl lldpad apt-utils gcc || handle_error "安装依赖包失败"
    
    log "依赖包安装完成"
}

# 配置NFS
configure_nfs() {
    log "配置NFS..."
    
    # 检查是否已安装nfs-common
    if ! dpkg -l | grep -q nfs-common; then
        log "安装 nfs-common 客户端..."
        sudo apt install -y nfs-common || handle_error "安装 nfs-common 失败"
    else
        log "nfs-common 已安装"
    fi

    # 检查目录是否存在
    local nfs_dir="/home/<USER>/nfs"
    if [ ! -d "$nfs_dir" ]; then
        log "创建目录 $nfs_dir"
        mkdir -p "$nfs_dir" || handle_error "无法创建目录 $nfs_dir"
    fi

    # 检查是否已经挂载
    if ! mount | grep -q "$nfs_dir"; then
        log "挂载 NFS 共享..."
        sudo mount 10.64.0.5:/home/<USER>/nvme/nfs "$nfs_dir" || handle_error "挂载 NFS 失败"
        log "NFS 挂载成功"
    else
        log "NFS 已挂载"
    fi
    
    # 检查挂载是否成功
    if ! mount | grep -q "$nfs_dir"; then
        handle_error "NFS 挂载验证失败"
    fi
}

# 主函数
main() {
    # 创建日志文件
    touch "$LOG_FILE" || echo "警告: 无法创建日志文件"
    
    log "开始执行安装依赖脚本..."
    
    # 解析命令行参数
    parse_args "$@"
    
    # 配置时区
    configure_timezone
    
    # 安装依赖包
    install_dependencies
    
    # 配置NFS
    configure_nfs
    
    log "安装依赖脚本执行完成"
}

# 执行主函数
main "$@"
