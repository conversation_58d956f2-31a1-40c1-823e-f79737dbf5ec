# -*- coding: utf-8 -*-
import os
import time
import psutil
import json
from fastapi import FastAPI, WebSocket, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn
from typing import List

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    print("警告: 未安装GPUtil库，无法监控GPU。安装请使用: pip install gputil")

class SystemMonitor:
    def __init__(self, interval=1):
        """初始化系统监控器
        
        Args:
            interval: 监控间隔时间（秒）
        """
        self.interval = interval
        self.prev_net_io = psutil.net_io_counters()
        self.prev_time = time.time()
    
    def get_gpu_info(self):
        """获取GPU信息"""
        if not GPU_AVAILABLE:
            return {"available": False, "message": "GPU监控不可用", "data": []}
        
        result = []
        try:
            gpus = GPUtil.getGPUs()
            for i, gpu in enumerate(gpus):
                result.append({
                    "id": i,
                    "name": gpu.name,
                    "load": round(gpu.load*100, 1),
                    "memoryUsed": gpu.memoryUsed,
                    "memoryTotal": gpu.memoryTotal,
                    "memoryPercent": round(gpu.memoryUtil*100, 1),
                    "temperature": gpu.temperature
                })
            return {"available": True, "data": result}
        except Exception as e:
            return {"available": False, "message": f"获取GPU信息失败: {e}", "data": []}
    
    def get_cpu_info(self):
        """获取CPU信息"""
        total = psutil.cpu_percent()
        per_cpu = psutil.cpu_percent(percpu=True)
        
        return {
            "total": total,
            "cores": [{"id": i, "percent": percent} for i, percent in enumerate(per_cpu)]
        }
    
    def get_memory_info(self):
        """获取内存信息"""
        mem = psutil.virtual_memory()
        return {
            "total": round(mem.total / (1024**3), 2),
            "used": round(mem.used / (1024**3), 2),
            "percent": mem.percent,
            "available": round(mem.available / (1024**3), 2)
        }
    
    def get_network_info(self):
        """获取网络带宽信息"""
        current_net_io = psutil.net_io_counters()
        current_time = time.time()
        
        # 计算时间差
        time_diff = current_time - self.prev_time
        
        # 计算下载和上传速度 (bytes/s)
        download_speed = (current_net_io.bytes_recv - self.prev_net_io.bytes_recv) / time_diff
        upload_speed = (current_net_io.bytes_sent - self.prev_net_io.bytes_sent) / time_diff
        
        # 更新之前的数据
        self.prev_net_io = current_net_io
        self.prev_time = current_time
        
        return {
            "download": round(download_speed / (1024**2), 2),
            "upload": round(upload_speed / (1024**2), 2)
        }
    
    def get_all_info(self):
        """获取所有系统信息"""
        return {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "gpu": self.get_gpu_info(),
            "cpu": self.get_cpu_info(),
            "memory": self.get_memory_info(),
            "network": self.get_network_info()
        }
    
    def monitor(self):
        """监控系统各项指标 (命令行版本)"""
        try:
            while True:
                os.system('cls' if os.name == 'nt' else 'clear')
                print("\n" + "="*50)
                print("系统监控 - " + time.strftime("%Y-%m-%d %H:%M:%S"))
                print("="*50)
                
                # 显示GPU信息
                print("\n[GPU信息]")
                gpu_info = self.get_gpu_info()
                if not gpu_info["available"]:
                    print(gpu_info["message"])
                else:
                    for gpu in gpu_info["data"]:
                        print(f"GPU {gpu['id']}: {gpu['name']}")
                        print(f"  负载: {gpu['load']}%")
                        print(f"  内存使用: {gpu['memoryUsed']}/{gpu['memoryTotal']} MB ({gpu['memoryPercent']}%)")
                        print(f"  温度: {gpu['temperature']}°C")
                
                # 显示CPU信息
                print("\n[CPU信息]")
                cpu_info = self.get_cpu_info()
                print(f"CPU总体使用率: {cpu_info['total']}%")
                for core in cpu_info["cores"]:
                    print(f"  核心 {core['id']}: {core['percent']}%")
                
                # 显示内存信息
                print("\n[内存信息]")
                mem_info = self.get_memory_info()
                print(f"内存总量: {mem_info['total']} GB")
                print(f"已使用: {mem_info['used']} GB ({mem_info['percent']}%)")
                print(f"可用: {mem_info['available']} GB")
                
                # 显示网络信息
                print("\n[网络信息]")
                net_info = self.get_network_info()
                print(f"网络下载速度: {net_info['download']} MB/s")
                print(f"网络上传速度: {net_info['upload']} MB/s")
                
                print("\n" + "="*50)
                print(f"按Ctrl+C停止监控 (每{self.interval}秒更新一次)")
                
                time.sleep(self.interval)
        except KeyboardInterrupt:
            print("\n监控已停止")

# FastAPI应用
app = FastAPI(title="系统监控")
monitor = SystemMonitor(interval=2)

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.update_interval = 2  # 默认更新间隔
        self.running = False

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        if not self.running:
            self.running = True
            await self.broadcast_info()

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        if not self.active_connections:
            self.running = False

    async def broadcast_info(self):
        while self.active_connections and self.running:
            info = monitor.get_all_info()
            for connection in self.active_connections:
                try:
                    await connection.send_json(info)
                except:
                    # 连接可能已关闭
                    pass
            await asyncio.sleep(self.update_interval)
    
    def set_update_interval(self, seconds: int):
        self.update_interval = max(1, min(seconds, 60))  # 限制在1-60秒之间
        return {"status": "success", "interval": self.update_interval}

# 创建连接管理器
manager = ConnectionManager()

# HTML页面
@app.get("/", response_class=HTMLResponse)
async def get_html():
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>系统监控</title>
        <style>
            :root {
                --primary-color: #3498db;
                --background-color: #f5f7fa;
                --card-bg: #ffffff;
                --text-color: #2c3e50;
                --border-color: #e0e6ed;
            }
            
            body {
                font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
                background-color: var(--background-color);
                color: var(--text-color);
                margin: 0;
                padding: 20px;
                line-height: 1.6;
            }
            
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            
            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid var(--border-color);
            }
            
            h1 {
                margin: 0;
                color: var(--primary-color);
            }
            
            .settings {
                display: flex;
                align-items: center;
            }
            
            .card {
                background-color: var(--card-bg);
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
                padding: 20px;
                margin-bottom: 20px;
                transition: transform 0.2s;
            }
            
            .card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
            
            .card h2 {
                margin-top: 0;
                color: var(--primary-color);
                font-size: 1.3rem;
                display: flex;
                align-items: center;
            }
            
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
            
            .progress {
                height: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 15px;
            }
            
            .progress-bar {
                height: 100%;
                background-color: var(--primary-color);
                transition: width 0.3s ease;
            }
            
            .cpu-core {
                margin-bottom: 10px;
            }
            
            .core-label {
                display: flex;
                justify-content: space-between;
                font-size: 0.9rem;
                margin-bottom: 4px;
            }
            
            .network-stats {
                display: flex;
                justify-content: space-between;
            }
            
            .stat-item {
                text-align: center;
                flex: 1;
            }
            
            .stat-value {
                font-size: 1.5rem;
                font-weight: bold;
                color: var(--primary-color);
            }
            
            .stat-label {
                font-size: 0.9rem;
                color: #7f8c8d;
            }
            
            .gpu-item {
                border: 1px solid var(--border-color);
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .gpu-item:last-child {
                margin-bottom: 0;
            }
            
            input[type="number"] {
                width: 60px;
                padding: 8px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                margin-right: 10px;
            }
            
            button {
                background-color: var(--primary-color);
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            button:hover {
                background-color: #2980b9;
            }
            
            .timestamp {
                font-size: 0.9rem;
                color: #7f8c8d;
                text-align: center;
                margin-top: 20px;
            }
            
            .error-message {
                color: #e74c3c;
                font-style: italic;
            }
            
            .icon {
                margin-right: 8px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>系统监控</h1>
                <div class="settings">
                    刷新间隔: 
                    <input type="number" id="intervalInput" min="1" max="60" value="2">
                    秒
                    <button id="setIntervalBtn">设置</button>
                </div>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h2>📊 CPU 信息</h2>
                    <div class="progress">
                        <div class="progress-bar" id="cpuTotalBar" style="width: 0%"></div>
                    </div>
                    <div id="cpuTotalText">加载中...</div>
                    <div id="cpuCores"></div>
                </div>
                
                <div class="card">
                    <h2>💾 内存信息</h2>
                    <div class="progress">
                        <div class="progress-bar" id="memBar" style="width: 0%"></div>
                    </div>
                    <div id="memText">加载中...</div>
                </div>
            </div>
            
            <div class="card">
                <h2>📡 网络信息</h2>
                <div class="network-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="downloadSpeed">0.00</div>
                        <div class="stat-label">下载速度 (MB/s)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="uploadSpeed">0.00</div>
                        <div class="stat-label">上传速度 (MB/s)</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>🎮 GPU 信息</h2>
                <div id="gpuInfo">
                    <p>加载中...</p>
                </div>
            </div>
            
            <div class="timestamp" id="updateTime">最后更新时间: -</div>
        </div>
        
        <script>
            let ws;
            let reconnectInterval = 1000; // 重连间隔(ms)
            
            function connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
                
                ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                    document.getElementById('updateTime').innerText = '已连接到服务器...';
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    updateUI(data);
                };
                
                ws.onclose = () => {
                    console.log('WebSocket连接已关闭，尝试重连...');
                    document.getElementById('updateTime').innerText = '连接断开，尝试重连中...';
                    setTimeout(connectWebSocket, reconnectInterval);
                };
                
                ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    ws.close();
                };
            }
            
            function updateUI(data) {
                // 更新时间戳
                document.getElementById('updateTime').innerText = '最后更新时间: ' + data.timestamp;
                
                // 更新CPU信息
                const cpuInfo = data.cpu;
                document.getElementById('cpuTotalText').innerText = `CPU总体使用率: ${cpuInfo.total}%`;
                document.getElementById('cpuTotalBar').style.width = `${cpuInfo.total}%`;
                
                let coresHTML = '';
                cpuInfo.cores.forEach(core => {
                    coresHTML += `
                        <div class="cpu-core">
                            <div class="core-label">
                                <span>核心 ${core.id}</span>
                                <span>${core.percent}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: ${core.percent}%"></div>
                            </div>
                        </div>
                    `;
                });
                document.getElementById('cpuCores').innerHTML = coresHTML;
                
                // 更新内存信息
                const memInfo = data.memory;
                document.getElementById('memText').innerHTML = `
                    内存总量: ${memInfo.total} GB<br>
                    已使用: ${memInfo.used} GB (${memInfo.percent}%)<br>
                    可用: ${memInfo.available} GB
                `;
                document.getElementById('memBar').style.width = `${memInfo.percent}%`;
                
                // 更新网络信息
                const netInfo = data.network;
                document.getElementById('downloadSpeed').innerText = netInfo.download.toFixed(2);
                document.getElementById('uploadSpeed').innerText = netInfo.upload.toFixed(2);
                
                // 更新GPU信息
                const gpuInfo = data.gpu;
                let gpuHTML = '';
                
                if (!gpuInfo.available) {
                    gpuHTML = `<p class="error-message">${gpuInfo.message || 'GPU信息不可用'}</p>`;
                } else if (gpuInfo.data.length === 0) {
                    gpuHTML = '<p>未检测到GPU设备</p>';
                } else {
                    gpuInfo.data.forEach(gpu => {
                        gpuHTML += `
                            <div class="gpu-item">
                                <h3>GPU ${gpu.id}: ${gpu.name}</h3>
                                <div class="core-label">
                                    <span>GPU负载</span>
                                    <span>${gpu.load}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: ${gpu.load}%"></div>
                                </div>
                                <div class="core-label">
                                    <span>显存使用</span>
                                    <span>${gpu.memoryPercent}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: ${gpu.memoryPercent}%"></div>
                                </div>
                                <p>显存: ${gpu.memoryUsed}/${gpu.memoryTotal} MB | 温度: ${gpu.temperature}°C</p>
                            </div>
                        `;
                    });
                }
                document.getElementById('gpuInfo').innerHTML = gpuHTML;
            }
            
            document.getElementById('setIntervalBtn').addEventListener('click', () => {
                const newInterval = parseInt(document.getElementById('intervalInput').value);
                if (newInterval && newInterval > 0 && newInterval <= 60) {
                    fetch('/set_interval', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({interval: newInterval})
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            alert(`刷新间隔已设置为 ${data.interval} 秒`);
                        } else {
                            alert('设置失败');
                        }
                    });
                } else {
                    alert('请输入1-60之间的数值');
                }
            });
            
            // 初始化连接
            connectWebSocket();
        </script>
    </body>
    </html>
    """

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # 等待客户端消息
            await websocket.receive_text()
    except:
        manager.disconnect(websocket)

# 设置刷新间隔
@app.post("/set_interval")
async def set_interval(request: Request):
    data = await request.json()
    interval = data.get("interval", 2)
    return manager.set_update_interval(interval)

# 异步支持
import asyncio

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='系统监控')
    parser.add_argument('--web', action='store_true', help='启动Web界面')
    parser.add_argument('--port', type=int, default=8000, help='Web服务端口')
    args = parser.parse_args()
    
    if args.web:
        print(f"启动Web监控服务，访问 http://localhost:{args.port}")
        uvicorn.run(app, host="0.0.0.0", port=args.port)
    else:
        print("启动系统监控...")
        monitor = SystemMonitor(interval=2)  # 每2秒更新一次
        monitor.monitor()
