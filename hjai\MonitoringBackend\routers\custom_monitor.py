from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from models.custom_monitor import MonitorItem, MonitorItemIP, MonitorData
# 导入新的时区处理工具
from config.timezone_utils import TZ
from datetime import datetime

# 导入服务层
from services.custom_monitor_service import CustomMonitorService
from services.monitor_test_service import MonitorTestService


# 定义请求和响应模型
class MonitorItemCreate(BaseModel):
    """创建监控项的请求模型"""
    name: str
    command: str
    description: Optional[str] = None
    data_type: str = "string"  # string, number, json等
    timeout: int = 30
    retry_count: int = 2
    category: Optional[str] = None
    security_level: str = "normal"
    # 测试相关字段
    test_ip: Optional[str] = None  # 用于测试的IP地址，将从数据库获取连接信息


class MonitorItemIPCreate(BaseModel):
    """创建监控项与IP关联的请求模型"""
    monitor_item_id: int
    ip_address: str


class MonitorTestRequest(BaseModel):
    """监控项测试请求模型"""
    command: str
    ip: str
    username: str
    password: Optional[str] = None
    use_ssh_key: bool = False
    ssh_key_path: Optional[str] = None
    timeout: int = 30
    data_type: str = "string"


class MonitorTestWithDBRequest(BaseModel):
    """使用数据库连接信息测试监控项的请求模型"""
    command: str
    ip: str
    timeout: int = 30
    data_type: str = "string"


class MonitorTestResponse(BaseModel):
    """监控项测试响应模型"""
    success: bool
    error: Optional[str] = None
    execution_time: Optional[float] = None
    output_sample: Optional[str] = None
    security_level: Optional[str] = None
    recommendations: List[str] = []


class MonitorValidationRequest(BaseModel):
    """监控项配置验证请求模型"""
    name: str
    command: str
    data_type: str = "string"
    timeout: int = 30
    category: Optional[str] = None


class MonitorValidationResponse(BaseModel):
    """监控项配置验证响应模型"""
    is_valid: bool
    messages: List[str] = []
    security_level: Optional[str] = None
    suggestions: List[Dict[str, str]] = []


class MonitorItemResponse(BaseModel):
    """监控项响应模型"""
    id: int
    name: str
    command: str
    description: Optional[str]
    data_type: str
    timeout: int = 30
    retry_count: int = 2
    enabled: bool = True
    category: Optional[str] = None
    security_level: str = "normal"
    created_at: datetime
    warning: Optional[str] = None  # 用于返回测试失败等警告信息

    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


class MonitorItemIPResponse(BaseModel):
    """监控项与IP关联响应模型"""
    id: int
    monitor_item_id: int
    monitor_item_name: str
    ip_address: str
    created_at: datetime

    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


class MonitorDataResponse(BaseModel):
    """监控数据响应模型"""
    id: int
    monitor_item_id: int
    monitor_item_name: str
    ip: str
    timestamp: datetime
    value: str
    status: int

    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


class MonitorDataCreate(BaseModel):
    """创建监控数据的请求模型"""
    monitor_item_id: int
    ip: str
    value: str
    status: int = 0


class IPQuery(BaseModel):
    """IP查询请求模型"""
    ip_address: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: Optional[int] = None
    limit: int = 20
    offset: int = 0
    
    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


# 创建路由
router = APIRouter(
    prefix="/monitor",
    tags=["自定义监控"]
)

# 创建服务实例
custom_monitor_service = CustomMonitorService()
monitor_test_service = MonitorTestService()


@router.post("/items", response_model=MonitorItemResponse)
async def create_monitor_item(item: MonitorItemCreate):
    """
    创建新的自定义监控项

    参数:
        item: 监控项信息，包含名称、命令、描述和数据类型
        如果提供了test_ip，将使用数据库中的连接信息进行测试

    返回:
        创建的监控项信息
    """
    # 如果提供了测试IP，先使用数据库连接信息进行测试
    test_success = True
    test_error = None

    if item.test_ip:
        test_success, test_error = await custom_monitor_service.test_monitor_item_with_db_credentials(
            command=item.command,
            ip=item.test_ip,
            timeout=item.timeout,
            data_type=item.data_type
        )

    # 使用服务层创建监控项（不再传递测试参数，因为我们已经在上面测试过了）
    monitor_item, _, creation_error = await custom_monitor_service.create_monitor_item(
        name=item.name,
        command=item.command,
        description=item.description,
        data_type=item.data_type,
        timeout=item.timeout,
        retry_count=item.retry_count,
        category=item.category,
        security_level=item.security_level
    )

    if not monitor_item:
        raise HTTPException(status_code=400, detail=creation_error)

    # 返回创建的监控项，包含测试结果信息
    response = {
        "id": monitor_item.id,
        "name": monitor_item.name,
        "command": monitor_item.command,
        "description": monitor_item.description,
        "data_type": monitor_item.data_type,
        "timeout": monitor_item.timeout,
        "retry_count": monitor_item.retry_count,
        "enabled": monitor_item.enabled,
        "category": monitor_item.category,
        "security_level": monitor_item.security_level,
        "created_at": monitor_item.created_at
    }

    # 如果有测试结果，添加到响应中
    if item.test_ip and not test_success and test_error:
        response["warning"] = f"监控项已创建但在 {item.test_ip} 上测试失败: {test_error}"
    elif item.test_ip and test_success:
        response["warning"] = f"监控项已创建并在 {item.test_ip} 上测试成功"

    return response


class MonitorItemUpdate(BaseModel):
    """更新监控项的请求模型"""
    name: Optional[str] = None
    command: Optional[str] = None
    description: Optional[str] = None
    data_type: Optional[str] = None
    timeout: Optional[int] = None
    retry_count: Optional[int] = None
    enabled: Optional[bool] = None
    category: Optional[str] = None
    security_level: Optional[str] = None


@router.put("/items/{item_id}", response_model=MonitorItemResponse)
async def update_monitor_item(item_id: int, item: MonitorItemUpdate):
    """
    更新监控项

    参数:
        item_id: 监控项ID
        item: 要更新的监控项信息

    返回:
        更新后的监控项信息
    """
    # 构建更新字典，只包含非None的字段
    update_data = {k: v for k, v in item.dict().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="至少需要提供一个要更新的字段")

    # 使用服务层更新监控项
    success, error = await custom_monitor_service.update_monitor_item(item_id, **update_data)

    if not success:
        if "未找到" in error:
            raise HTTPException(status_code=404, detail=error)
        else:
            raise HTTPException(status_code=400, detail=error)

    # 获取更新后的监控项
    monitor_item = await MonitorItem.filter(id=item_id).first()

    return {
        "id": monitor_item.id,
        "name": monitor_item.name,
        "command": monitor_item.command,
        "description": monitor_item.description,
        "data_type": monitor_item.data_type,
        "timeout": monitor_item.timeout,
        "retry_count": monitor_item.retry_count,
        "enabled": monitor_item.enabled,
        "category": monitor_item.category,
        "security_level": monitor_item.security_level,
        "created_at": monitor_item.created_at
    }


@router.delete("/items/{item_id}")
async def delete_monitor_item(item_id: int):
    """
    删除监控项

    参数:
        item_id: 监控项ID

    返回:
        删除结果
    """
    # 使用服务层删除监控项
    success, error = await custom_monitor_service.delete_monitor_item(item_id)

    if not success:
        if "未找到" in error:
            raise HTTPException(status_code=404, detail=error)
        else:
            raise HTTPException(status_code=400, detail=error)

    return {"message": "监控项删除成功"}


@router.get("/items/{item_id}", response_model=MonitorItemResponse)
async def get_monitor_item(item_id: int):
    """
    获取单个监控项

    参数:
        item_id: 监控项ID

    返回:
        监控项信息
    """
    monitor_item = await custom_monitor_service.get_monitor_item(item_id)

    if not monitor_item:
        raise HTTPException(status_code=404, detail=f"未找到ID为 {item_id} 的监控项")

    return {
        "id": monitor_item.id,
        "name": monitor_item.name,
        "command": monitor_item.command,
        "description": monitor_item.description,
        "data_type": monitor_item.data_type,
        "timeout": monitor_item.timeout,
        "retry_count": monitor_item.retry_count,
        "enabled": monitor_item.enabled,
        "category": monitor_item.category,
        "security_level": monitor_item.security_level,
        "created_at": monitor_item.created_at
    }


@router.post("/items/ip", response_model=MonitorItemIPResponse)
async def add_monitor_item_to_ip(relation: MonitorItemIPCreate):
    """
    将监控项添加到指定IP地址

    参数:
        relation: 监控项与IP的关联信息，包含监控项ID和IP地址

    返回:
        创建的关联信息
    """
    # 使用服务层添加IP关联
    success, error = await custom_monitor_service.add_monitor_item_ip(
        relation.monitor_item_id,
        relation.ip_address
    )

    if not success:
        if "未找到" in error:
            raise HTTPException(status_code=404, detail=error)
        else:
            raise HTTPException(status_code=400, detail=error)

    # 获取创建的关联信息
    monitor_item = await MonitorItem.filter(id=relation.monitor_item_id).first()
    monitor_item_ip = await MonitorItemIP.filter(
        monitor_item_id=relation.monitor_item_id,
        ip_address=relation.ip_address
    ).first()

    return {
        "id": monitor_item_ip.id,
        "monitor_item_id": monitor_item.id,
        "monitor_item_name": monitor_item.name,
        "ip_address": monitor_item_ip.ip_address,
        "created_at": monitor_item_ip.created_at
    }


@router.get("/items", response_model=List[MonitorItemResponse])
async def get_monitor_items():
    """
    获取所有监控项

    返回:
        监控项列表
    """
    items = await MonitorItem.all()

    return [
        {
            "id": item.id,
            "name": item.name,
            "command": item.command,
            "description": item.description,
            "data_type": item.data_type,
            "timeout": item.timeout,
            "retry_count": item.retry_count,
            "enabled": item.enabled,
            "category": item.category,
            "security_level": item.security_level,
            "created_at": item.created_at
        }
        for item in items
    ]


@router.get("/items/ip/{ip_address}", response_model=List[MonitorItemResponse])
async def get_monitor_items_by_ip(ip_address: str):
    """
    获取指定IP地址关联的所有监控项
    
    参数:
        ip_address: IP地址
        
    返回:
        监控项列表
    """
    # 查询与该IP关联的所有监控项关联
    relations = await MonitorItemIP.filter(ip_address=ip_address).prefetch_related("monitor_item")
    
    # 提取监控项信息
    items = [relation.monitor_item for relation in relations]
    
    return [
        {
            "id": item.id,
            "name": item.name,
            "command": item.command,
            "description": item.description,
            "data_type": item.data_type,
            "timeout": item.timeout,
            "retry_count": item.retry_count,
            "enabled": item.enabled,
            "category": item.category,
            "security_level": item.security_level,
            "created_at": item.created_at
        }
        for item in items
    ]


@router.get("/data/ip/{ip_address}", response_model=List[MonitorDataResponse])
async def get_monitor_data_by_ip(
    ip_address: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    status: Optional[int] = None,
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0)
):
    """
    获取指定IP地址的所有监控数据
    
    参数:
        ip_address: IP地址
        start_time: 开始时间（可选）
        end_time: 结束时间（可选）
        status: 状态过滤（可选）
        limit: 每页记录数（默认20）
        offset: 分页偏移（默认0）
        
    返回:
        监控数据列表
    """
    # 构建查询条件
    query = MonitorData.filter(ip=ip_address)
    
    # 添加时间范围过滤 - 转换为UTC时间进行查询
    if start_time:
        start_time_utc = TZ.from_input(start_time)
        query = query.filter(timestamp__gte=start_time_utc)
    if end_time:
        end_time_utc = TZ.from_input(end_time)
        query = query.filter(timestamp__lte=end_time_utc)
    
    # 添加状态过滤
    if status is not None:
        query = query.filter(status=status)
    
    # 查询总记录数
    total_count = await query.count()
    
    # 添加分页和排序
    query = query.order_by("-timestamp").offset(offset).limit(limit)
    
    # 查询数据并预加载关联的监控项
    data_list = await query.prefetch_related("monitor_item")
    
    # 返回结果
    return [
        {
            "id": data.id,
            "monitor_item_id": data.monitor_item.id,
            "monitor_item_name": data.monitor_item.name,
            "ip": data.ip,
            "timestamp": data.get_timestamp(),
            "value": data.value,
            "status": data.status
        }
        for data in data_list
    ]


@router.post("/data", response_model=MonitorDataResponse)
async def create_monitor_data(data: MonitorDataCreate):
    """
    上报监控数据
    
    参数:
        data: 监控数据，包含监控项ID、IP地址、数值和状态
        
    返回:
        创建的监控数据
    """
    # 使用服务层保存监控数据
    success, error = await custom_monitor_service.save_monitor_data(
        item_id=data.monitor_item_id,
        ip=data.ip,
        value=data.value,
        status=data.status
    )

    if not success:
        if "未找到" in error:
            raise HTTPException(status_code=404, detail=error)
        else:
            raise HTTPException(status_code=400, detail=error)

    # 获取监控项信息用于响应
    monitor_item = await MonitorItem.filter(id=data.monitor_item_id).first()

    # 获取最新的监控数据
    monitor_data = await MonitorData.filter(
        monitor_item_id=data.monitor_item_id,
        ip=data.ip
    ).order_by('-timestamp').first()

    return {
        "id": monitor_data.id,
        "monitor_item_id": monitor_item.id,
        "monitor_item_name": monitor_item.name,
        "ip": monitor_data.ip,
        "timestamp": monitor_data.get_timestamp(),
        "value": monitor_data.value,
        "status": monitor_data.status
    }


@router.post("/data/ip", response_model=MonitorDataResponse)
async def create_monitor_data_by_ip(ip: str, data: dict):
    """
    简化的监控数据上报，只需要IP地址
    
    参数:
        ip: IP地址
        data: 包含 monitor_item_id, value, status(可选) 的字典
        
    返回:
        创建的监控数据
    """
    # 检查必要参数
    if "monitor_item_id" not in data:
        raise HTTPException(status_code=400, detail="必须提供监控项ID (monitor_item_id)")
    
    if "value" not in data:
        raise HTTPException(status_code=400, detail="必须提供监控值 (value)")
    
    # 提取参数
    monitor_item_id = data["monitor_item_id"]
    value = data["value"]
    status = data.get("status", 0)  # 默认状态为0(正常)
    
    # 使用服务层保存监控数据
    success, error = await custom_monitor_service.save_monitor_data(
        item_id=monitor_item_id,
        ip=ip,
        value=value,
        status=status
    )

    if not success:
        if "未找到" in error:
            raise HTTPException(status_code=404, detail=error)
        else:
            raise HTTPException(status_code=400, detail=error)

    # 获取监控项信息用于响应
    monitor_item = await MonitorItem.filter(id=monitor_item_id).first()

    # 获取最新的监控数据
    monitor_data = await MonitorData.filter(
        monitor_item_id=monitor_item_id,
        ip=ip
    ).order_by('-timestamp').first()

    return {
        "id": monitor_data.id,
        "monitor_item_id": monitor_item.id,
        "monitor_item_name": monitor_item.name,
        "ip": monitor_data.ip,
        "timestamp": monitor_data.get_timestamp(),
        "value": monitor_data.value,
        "status": monitor_data.status
    }


@router.post("/query", response_model=List[MonitorDataResponse])
async def query_monitor_data_by_ip(query: IPQuery):
    """
    通过IP地址查询监控数据（简化版，只需要提供IP）
    
    参数:
        query: IP查询请求，只需包含ip_address字段
        
    返回:
        监控数据列表
    """
    # 构建查询条件
    db_query = MonitorData.filter(ip=query.ip_address)
    
    # 添加时间范围过滤 - 转换为UTC时间进行查询
    if query.start_time:
        start_time_utc = TZ.from_input(query.start_time)
        db_query = db_query.filter(timestamp__gte=start_time_utc)
    if query.end_time:
        end_time_utc = TZ.from_input(query.end_time)
        db_query = db_query.filter(timestamp__lte=end_time_utc)
    
    # 添加状态过滤
    if query.status is not None:
        db_query = db_query.filter(status=query.status)
    
    # 添加分页和排序
    db_query = db_query.order_by("-timestamp").offset(query.offset).limit(query.limit)
    
    # 查询数据并预加载关联的监控项
    data_list = await db_query.prefetch_related("monitor_item")
    
    # 返回结果
    return [
        {
            "id": data.id,
            "monitor_item_id": data.monitor_item.id,
            "monitor_item_name": data.monitor_item.name,
            "ip": data.ip,
            "timestamp": data.get_timestamp(),
            "value": data.value,
            "status": data.status
        }
        for data in data_list
    ]


@router.post("/test", response_model=MonitorTestResponse)
async def test_monitor_command(request: MonitorTestRequest):
    """
    测试监控命令

    参数:
        request: 测试请求，包含命令、服务器信息等

    返回:
        测试结果，包含成功状态、错误信息、执行时间等
    """
    try:
        # 构造认证凭据
        credentials = {
            'username': request.username,
            'password': request.password,
            'use_ssh_key': request.use_ssh_key,
            'ssh_key_path': request.ssh_key_path
        }

        # 执行测试
        success, error = await monitor_test_service.test_monitor_command(
            command=request.command,
            ip=request.ip,
            credentials=credentials,
            timeout=request.timeout,
            data_type=request.data_type
        )

        # 获取安全级别和建议
        security_level = monitor_test_service.security_validator.get_command_risk_level(request.command)
        recommendations = monitor_test_service.security_validator.get_security_recommendations(request.command)

        return {
            "success": success,
            "error": error,
            "execution_time": None,  # TODO: 添加执行时间记录
            "output_sample": None,   # TODO: 添加输出样例
            "security_level": security_level,
            "recommendations": recommendations
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试过程异常: {str(e)}")


@router.post("/test-with-db", response_model=MonitorTestResponse)
async def test_monitor_command_with_db(request: MonitorTestWithDBRequest):
    """
    使用数据库中的连接信息测试监控命令

    参数:
        request: 测试请求，包含命令和IP地址，连接信息从数据库获取

    返回:
        测试结果，包含成功状态、错误信息等
    """
    try:
        # 使用数据库连接信息执行测试
        success, error = await custom_monitor_service.test_monitor_item_with_db_credentials(
            command=request.command,
            ip=request.ip,
            timeout=request.timeout,
            data_type=request.data_type
        )

        # 获取安全级别和建议
        security_level = monitor_test_service.security_validator.get_command_risk_level(request.command)
        recommendations = monitor_test_service.security_validator.get_security_recommendations(request.command)

        return {
            "success": success,
            "error": error,
            "execution_time": None,  # TODO: 添加执行时间记录
            "output_sample": None,   # TODO: 添加输出样例
            "security_level": security_level,
            "recommendations": recommendations
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试过程异常: {str(e)}")


@router.post("/validate", response_model=MonitorValidationResponse)
async def validate_monitor_config(request: MonitorValidationRequest):
    """
    验证监控项配置

    参数:
        request: 验证请求，包含监控项配置信息

    返回:
        验证结果，包含是否有效、验证消息、安全级别等
    """
    try:
        # 执行配置验证
        is_valid, messages = await monitor_test_service.validate_monitor_config(
            name=request.name,
            command=request.command,
            data_type=request.data_type,
            timeout=request.timeout,
            category=request.category
        )

        # 获取安全级别
        security_level = monitor_test_service.security_validator.get_command_risk_level(request.command)

        # 获取命令建议
        suggestions = monitor_test_service.get_command_suggestions(request.category)

        return {
            "is_valid": is_valid,
            "messages": messages,
            "security_level": security_level,
            "suggestions": suggestions
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"验证过程异常: {str(e)}")


@router.get("/suggestions")
async def get_command_suggestions(category: Optional[str] = Query(None, description="分类过滤")):
    """
    获取监控命令建议

    参数:
        category: 可选的分类过滤

    返回:
        命令建议列表
    """
    try:
        suggestions = monitor_test_service.get_command_suggestions(category)
        return {"suggestions": suggestions}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取建议失败: {str(e)}")


# 批量操作相关的请求和响应模型
class BatchOperationRequest(BaseModel):
    """批量操作请求模型"""
    item_ids: List[int]
    operation: str  # enable, disable, delete


class BatchOperationResponse(BaseModel):
    """批量操作响应模型"""
    success_count: int
    failed_count: int
    success_items: List[int] = []
    failed_items: List[Dict[str, Any]] = []


class MonitorExecutionRequest(BaseModel):
    """监控执行请求模型"""
    monitor_item_id: int
    server_ips: Optional[List[str]] = None


class MonitorExecutionResponse(BaseModel):
    """监控执行响应模型"""
    success: bool
    message: str
    results: Optional[Dict[str, Any]] = None


@router.post("/batch", response_model=BatchOperationResponse)
async def batch_operations(request: BatchOperationRequest):
    """
    批量操作监控项

    参数:
        request: 批量操作请求，包含项目ID列表和操作类型

    返回:
        批量操作结果
    """
    try:
        if request.operation == "enable":
            results = await custom_monitor_service.batch_enable_monitor_items(request.item_ids)
        elif request.operation == "disable":
            results = await custom_monitor_service.batch_disable_monitor_items(request.item_ids)
        elif request.operation == "delete":
            results = {'success': [], 'failed': []}
            for item_id in request.item_ids:
                success, error = await custom_monitor_service.delete_monitor_item(item_id)
                if success:
                    results['success'].append(item_id)
                else:
                    results['failed'].append({'id': item_id, 'error': error})
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作类型: {request.operation}")

        return {
            "success_count": len(results['success']),
            "failed_count": len(results['failed']),
            "success_items": results['success'],
            "failed_items": results['failed']
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")


@router.post("/execute", response_model=MonitorExecutionResponse)
async def execute_monitor_item(request: MonitorExecutionRequest):
    """
    立即执行监控项

    参数:
        request: 执行请求，包含监控项ID和可选的服务器IP列表

    返回:
        执行结果
    """
    try:
        from scripts.custom_monitor_scheduler import get_custom_monitor_scheduler

        scheduler = get_custom_monitor_scheduler()
        result = await scheduler.execute_monitor_item_now(
            monitor_item_id=request.monitor_item_id,
            server_ips=request.server_ips
        )

        return {
            "success": result.get('success', False),
            "message": result.get('error', 'Execution completed') if not result.get('success') else 'Execution successful',
            "results": result.get('results')
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行监控项失败: {str(e)}")


@router.get("/status")
async def get_monitor_status():
    """
    获取监控状态概览

    返回:
        监控状态统计信息
    """
    try:
        from core.monitor_status_manager import get_status_manager

        status_manager = get_status_manager()
        global_stats = status_manager.get_global_stats()

        # 获取有问题的服务器
        problematic_servers = status_manager.get_problematic_servers()

        return {
            "global_stats": global_stats,
            "problematic_servers_count": len(problematic_servers),
            "problematic_servers": [
                {
                    "server_ip": server.server_ip,
                    "server_status": server.server_status.value,
                    "health_score": server.overall_health_score,
                    "error_items": server.error_items,
                    "total_items": server.total_monitor_items
                }
                for server in problematic_servers[:10]  # 只返回前10个
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取监控状态失败: {str(e)}")


@router.get("/status/server/{server_ip}")
async def get_server_monitor_status(server_ip: str):
    """
    获取指定服务器的监控状态

    参数:
        server_ip: 服务器IP地址

    返回:
        服务器监控状态详情
    """
    try:
        from core.monitor_status_manager import get_status_manager

        status_manager = get_status_manager()
        server_status = status_manager.get_server_status(server_ip)

        if not server_status:
            raise HTTPException(status_code=404, detail=f"未找到服务器 {server_ip} 的监控状态")

        # 获取有问题的监控项
        problematic_items = server_status.get_problematic_items()

        return {
            "server_ip": server_status.server_ip,
            "server_status": server_status.server_status.value,
            "last_check_time": server_status.last_check_time.isoformat(),
            "total_monitor_items": server_status.total_monitor_items,
            "healthy_items": server_status.healthy_items,
            "warning_items": server_status.warning_items,
            "error_items": server_status.error_items,
            "overall_health_score": server_status.overall_health_score,
            "problematic_items": [
                {
                    "monitor_item_id": item.monitor_item_id,
                    "monitor_item_name": item.monitor_item_name,
                    "status": item.status.value,
                    "consecutive_failures": item.consecutive_failures,
                    "success_rate": item.success_rate,
                    "last_error": item.last_error,
                    "last_check_time": item.last_check_time.isoformat()
                }
                for item in problematic_items
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务器监控状态失败: {str(e)}")


@router.get("/stats")
async def get_monitor_statistics():
    """
    获取监控性能统计信息

    返回:
        监控系统性能统计
    """
    try:
        from scripts.custom_monitor_scheduler import get_custom_monitor_scheduler
        from core.error_handler import get_error_handler

        scheduler = get_custom_monitor_scheduler()
        error_handler = get_error_handler()

        # 获取调度器统计
        scheduler_stats = scheduler.get_stats()

        # 获取错误统计
        error_stats = error_handler.get_error_stats()

        return {
            "scheduler_stats": scheduler_stats,
            "error_stats": error_stats,
            "system_health": {
                "scheduler_running": scheduler_stats.get('running', False),
                "success_rate": scheduler_stats.get('success_rate', 0),
                "average_cycle_time": scheduler_stats.get('average_cycle_time', 0),
                "total_errors": error_stats.get('total_errors', 0)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/items/{item_id}/statistics")
async def get_monitor_item_statistics(item_id: int, hours: int = Query(24, description="统计时间范围(小时)")):
    """
    获取监控项统计信息

    参数:
        item_id: 监控项ID
        hours: 统计时间范围(小时)

    返回:
        监控项统计信息
    """
    try:
        stats = await custom_monitor_service.get_monitor_statistics(item_id, hours)

        if not stats:
            raise HTTPException(status_code=404, detail=f"未找到监控项 {item_id} 的统计信息")

        return stats

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取监控项统计失败: {str(e)}")