"""
SSH命令执行器

基于连接池的高性能SSH命令执行器，支持重试、超时、并发控制等功能
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from contextlib import asynccontextmanager

from .ssh_connection_pool import get_connection_pool

logger = logging.getLogger(__name__)


@dataclass
class ExecutionResult:
    """命令执行结果"""
    success: bool
    output: Optional[str] = None
    error: Optional[str] = None
    exit_code: Optional[int] = None
    execution_time: float = 0.0
    retry_count: int = 0
    server_ip: Optional[str] = None


class SSHExecutor:
    """SSH命令执行器"""
    
    def __init__(
        self,
        default_timeout: int = 30,
        max_retries: int = 2,
        retry_delay: float = 1.0,
        max_concurrent_executions: int = 10
    ):
        self.default_timeout = default_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.semaphore = asyncio.Semaphore(max_concurrent_executions)
        self.connection_pool = get_connection_pool()
    
    async def execute_command(
        self,
        server: Dict[str, Any],
        command: str,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        capture_output: bool = True
    ) -> ExecutionResult:
        """
        执行SSH命令
        
        Args:
            server: 服务器信息
            command: 要执行的命令
            timeout: 超时时间
            max_retries: 最大重试次数
            capture_output: 是否捕获输出
            
        Returns:
            ExecutionResult: 执行结果
        """
        timeout = timeout or self.default_timeout
        max_retries = max_retries if max_retries is not None else self.max_retries
        
        async with self.semaphore:  # 控制并发数
            return await self._execute_with_retry(
                server, command, timeout, max_retries, capture_output
            )
    
    async def _execute_with_retry(
        self,
        server: Dict[str, Any],
        command: str,
        timeout: int,
        max_retries: int,
        capture_output: bool
    ) -> ExecutionResult:
        """带重试的命令执行"""
        last_error = None
        
        for retry_count in range(max_retries + 1):
            try:
                if retry_count > 0:
                    # 重试前等待
                    await asyncio.sleep(self.retry_delay * retry_count)
                    logger.debug(f"重试执行命令 ({retry_count}/{max_retries}): {server['ip']}")
                
                result = await self._execute_single_command(
                    server, command, timeout, capture_output
                )
                result.retry_count = retry_count
                
                if result.success:
                    return result
                
                last_error = result.error
                
                # 如果是超时错误，不重试
                if "timeout" in (result.error or "").lower():
                    break
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"命令执行异常 (重试 {retry_count}/{max_retries}): {e}")
        
        # 所有重试都失败
        return ExecutionResult(
            success=False,
            error=last_error or "命令执行失败",
            retry_count=max_retries,
            server_ip=server.get('ip')
        )
    
    async def _execute_single_command(
        self,
        server: Dict[str, Any],
        command: str,
        timeout: int,
        capture_output: bool
    ) -> ExecutionResult:
        """执行单个命令"""
        start_time = time.time()
        
        try:
            async with self.connection_pool.get_connection(server) as ssh_client:
                # 在线程池中执行命令以避免阻塞
                def execute():
                    stdin, stdout, stderr = ssh_client.exec_command(command, timeout=timeout)
                    
                    if capture_output:
                        output = stdout.read().decode('utf-8', errors='ignore').strip()
                        error = stderr.read().decode('utf-8', errors='ignore').strip()
                    else:
                        output = None
                        error = None
                    
                    exit_code = stdout.channel.recv_exit_status()
                    return output, error, exit_code
                
                # 使用asyncio.wait_for添加额外的超时保护
                output, error, exit_code = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(None, execute),
                    timeout=timeout + 5  # 额外5秒缓冲
                )
                
                execution_time = time.time() - start_time
                
                return ExecutionResult(
                    success=exit_code == 0,
                    output=output,
                    error=error if exit_code != 0 else None,
                    exit_code=exit_code,
                    execution_time=execution_time,
                    server_ip=server.get('ip')
                )
                
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            return ExecutionResult(
                success=False,
                error=f"命令执行超时 ({timeout}秒)",
                execution_time=execution_time,
                server_ip=server.get('ip')
            )
        except Exception as e:
            execution_time = time.time() - start_time
            return ExecutionResult(
                success=False,
                error=f"命令执行异常: {str(e)}",
                execution_time=execution_time,
                server_ip=server.get('ip')
            )
    
    async def execute_batch_commands(
        self,
        servers: List[Dict[str, Any]],
        command: str,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        max_concurrent: Optional[int] = None
    ) -> List[ExecutionResult]:
        """
        批量执行命令
        
        Args:
            servers: 服务器列表
            command: 要执行的命令
            timeout: 超时时间
            max_retries: 最大重试次数
            max_concurrent: 最大并发数
            
        Returns:
            List[ExecutionResult]: 执行结果列表
        """
        if max_concurrent:
            # 临时调整并发数
            original_semaphore = self.semaphore
            self.semaphore = asyncio.Semaphore(max_concurrent)
        
        try:
            tasks = [
                self.execute_command(server, command, timeout, max_retries)
                for server in servers
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(ExecutionResult(
                        success=False,
                        error=f"任务异常: {str(result)}",
                        server_ip=servers[i].get('ip') if i < len(servers) else None
                    ))
                else:
                    processed_results.append(result)
            
            return processed_results
            
        finally:
            if max_concurrent:
                # 恢复原始并发数
                self.semaphore = original_semaphore
    
    async def execute_command_on_multiple_servers(
        self,
        servers: List[Dict[str, Any]],
        commands: Dict[str, str],  # {server_ip: command}
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None
    ) -> Dict[str, ExecutionResult]:
        """
        在多个服务器上执行不同的命令
        
        Args:
            servers: 服务器列表
            commands: 服务器IP到命令的映射
            timeout: 超时时间
            max_retries: 最大重试次数
            
        Returns:
            Dict[str, ExecutionResult]: 服务器IP到执行结果的映射
        """
        tasks = {}
        
        for server in servers:
            server_ip = server['ip']
            if server_ip in commands:
                tasks[server_ip] = self.execute_command(
                    server, commands[server_ip], timeout, max_retries
                )
        
        results = await asyncio.gather(*tasks.values(), return_exceptions=True)
        
        # 构建结果映射
        result_map = {}
        for server_ip, result in zip(tasks.keys(), results):
            if isinstance(result, Exception):
                result_map[server_ip] = ExecutionResult(
                    success=False,
                    error=f"任务异常: {str(result)}",
                    server_ip=server_ip
                )
            else:
                result_map[server_ip] = result
        
        return result_map
    
    def get_stats(self) -> Dict[str, Any]:
        """获取执行器统计信息"""
        return {
            'default_timeout': self.default_timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'max_concurrent_executions': self.semaphore._value,
            'current_concurrent_executions': self.semaphore._value - len(self.semaphore._waiters) if hasattr(self.semaphore, '_waiters') and self.semaphore._waiters is not None else 0,
            'connection_pool_stats': self.connection_pool.get_stats()
        }


# 全局执行器实例
_ssh_executor: Optional[SSHExecutor] = None


def get_ssh_executor() -> SSHExecutor:
    """获取全局SSH执行器实例"""
    global _ssh_executor
    if _ssh_executor is None:
        _ssh_executor = SSHExecutor()
    return _ssh_executor


def initialize_ssh_executor(**kwargs):
    """初始化SSH执行器"""
    global _ssh_executor
    _ssh_executor = SSHExecutor(**kwargs)
