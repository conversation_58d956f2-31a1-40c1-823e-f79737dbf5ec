import axios from 'axios'

// 创建axios实例
const service = axios.create({
  baseURL: 'http://smart-shop.itheima.net', // 基础URL
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 可以在这里添加请求头等配置
    config.headers.platform = 'H5' // 添加平台标识
    return config
  },
  error => {
    console.log('请求错误：', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 直接返回响应数据
    return response.data
  },
  error => {
    console.log('响应错误：', error)
    return Promise.reject(error)
  }
)

export default service
