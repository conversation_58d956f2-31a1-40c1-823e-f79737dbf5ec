import asyncio
import logging
import time
import os
import psutil
import paramiko
from typing import List, Dict, Any, Set, Tuple, Optional, Callable
from models.ip_user import IPUser
from tortoise.exceptions import OperationalError
# 导入SSH连接工具
from utils.ssh_utils import SSHConnectionManager
from utils.ssh_connection_pool import get_connection_pool
from utils.ssh_executor import get_ssh_executor
import pytz
from datetime import datetime
from contextlib import asynccontextmanager
import concurrent.futures
from functools import partial
from fastapi.concurrency import run_in_threadpool

# 设置上海时区
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')

# 获取日志记录器
logger = logging.getLogger(__name__)

# 创建一个线程池执行器，用于执行阻塞的SSH操作
_thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=20, thread_name_prefix="ssh_worker")

# 用于在线程池中执行阻塞操作的辅助函数
async def run_in_threadpool(func, *args, **kwargs):
    """在线程池中执行阻塞函数，避免阻塞事件循环"""
    # 创建可调用的偏函数
    func_call = partial(func, *args, **kwargs)
    # 在线程池中执行
    return await asyncio.get_event_loop().run_in_executor(_thread_pool, func_call)

class ServerCache:
    """服务器信息缓存类，减少数据库查询"""
    def __init__(self, ttl_seconds: int = 300):
        self.servers: List[Dict[str, Any]] = []
        self.last_update: float = 0
        self.ttl_seconds: int = ttl_seconds
        self.failed_servers: Set[str] = set()  # 记录失败的服务器IP
    
    def needs_refresh(self) -> bool:
        """检查缓存是否需要刷新"""
        return time.time() - self.last_update > self.ttl_seconds or not self.servers
    
    async def refresh_cache(self, only_connectable: bool = False):
        """
        刷新服务器列表缓存
        
        Args:
            only_connectable: 是否只获取可连通的服务器
        """
        self.servers = await fetch_servers_from_db(only_connectable)
        self.last_update = time.time()
    
    def mark_failed(self, server_ip: str):
        """标记失败的服务器"""
        self.failed_servers.add(server_ip)
    
    def mark_success(self, server_ip: str):
        """标记成功的服务器，从失败集合中移除"""
        if server_ip in self.failed_servers:
            self.failed_servers.remove(server_ip)
    
    def prioritize_servers(self) -> List[Dict[str, Any]]:
        """对服务器进行优先级排序，失败的服务器优先检查"""
        failed_servers = [s for s in self.servers if s['ip'] in self.failed_servers]
        other_servers = [s for s in self.servers if s['ip'] not in self.failed_servers]
        return failed_servers + other_servers

# 创建全局缓存实例
server_cache = ServerCache()

async def fetch_servers_from_db(only_connectable: bool = False) -> List[Dict[str, Any]]:
    """
    从数据库获取服务器凭据
    
    Args:
        only_connectable: 是否只获取可连通的服务器
    
    Returns:
        List[Dict]: 服务器信息列表
    """
    try:
        # 尝试使用新版表结构查询
        try:
            if only_connectable:
                # 只查询未删除且可连通的服务器
                servers = await IPUser.filter(is_deleted=False, is_connectable=True).all()
            else:
                # 查询所有未删除的服务器，不管连通状态
                servers = await IPUser.filter(is_deleted=False).all()
                
        except OperationalError:
            # 如果失败，说明表中没有is_deleted或is_connectable列，使用旧版查询
            logger.warning("表结构未更新，使用旧版查询")
            servers = await IPUser.all()
        
        return [
            {
                "id": server.id,
                "ip": server.ip,
                "username": server.username, 
                "password": server.password,
                "is_connectable": getattr(server, "is_connectable", True)
            } 
            for server in servers
        ]
    except Exception as e:
        logger.error(f"获取服务器凭据失败: {str(e)}")
        return []

async def get_all_servers(only_connectable: bool = False) -> List[Dict[str, Any]]:
    """
    获取所有服务器，优先使用缓存
    
    Args:
        only_connectable: 是否只获取可连通的服务器
    
    Returns:
        List[Dict]: 服务器信息列表
    """
    if server_cache.needs_refresh():
        logger.debug("服务器缓存过期，从数据库刷新数据")
        await server_cache.refresh_cache(only_connectable)
    elif only_connectable:
        # 从缓存中筛选可连通的服务器
        return [s for s in server_cache.servers if s.get('is_connectable', 0) == 1]
    return server_cache.servers

def get_optimal_concurrency() -> int:
    """
    根据系统资源获取最优并发数
    
    Returns:
        int: 推荐的并发连接数
    """
    try:
        # 获取系统信息
        cpu_count = os.cpu_count() or 4
        memory = psutil.virtual_memory()
        memory_free_gb = memory.available / (1024 ** 3)  # 可用内存(GB)
        
        # 基于CPU和内存计算并发数
        # 每个SSH连接大约使用5-10MB内存
        cpu_based = cpu_count * 5  # 每CPU核心5个连接
        memory_based = int(memory_free_gb * 10)  # 每GB可用内存10个连接
        
        # 取两者较小值，确保不超出系统资源
        concurrency = min(cpu_based, memory_based)
        
        # 限制在合理范围内
        concurrency = max(min(concurrency, 50), 10)  # 最少10，最多50
        
        logger.debug(f"系统状态: CPU核心数={cpu_count}, 可用内存={memory_free_gb:.1f}GB, 推荐并发数={concurrency}")
        return concurrency
        
    except Exception as e:
        logger.warning(f"获取系统资源信息失败: {str(e)}，使用默认并发数20")
        return 20

@asynccontextmanager
async def ssh_connection(server: Dict[str, Any], timeout: int = 15) -> paramiko.SSHClient:
    """
    SSH连接上下文管理器，确保连接正确关闭
    
    Args:
        server: 服务器信息
        timeout: 连接超时时间(秒)
        
    Yields:
        paramiko.SSHClient: SSH客户端连接
    """
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        yield ssh_client
    finally:
        ssh_client.close()

async def update_server_status(server_ip: str, is_connectable: bool) -> bool:
    """
    更新服务器连通状态
    
    Args:
        server_ip: 服务器IP
        is_connectable: 连通状态 (False:不可连通, True:可连通)
    
    Returns:
        bool: 更新是否成功
    """
    try:
        await IPUser.filter(ip=server_ip).update(is_connectable=is_connectable)
        
        # 更新缓存中服务器的状态
        for server in server_cache.servers:
            if server["ip"] == server_ip:
                server["is_connectable"] = is_connectable
                break
                
        if is_connectable:
            server_cache.mark_success(server_ip)
        else:
            server_cache.mark_failed(server_ip)
            
        return True
    except Exception as e:
        logger.error(f"更新服务器 {server_ip} 连通状态失败: {str(e)}")
        return False

async def batch_update_server_status(status_updates: List[Tuple[str, bool]]) -> None:
    """
    批量更新服务器状态
    
    Args:
        status_updates: 包含(服务器IP, 状态)元组的列表
    """
    try:
        # 按批处理更新，避免一次性提交过多数据
        batch_size = 100
        for i in range(0, len(status_updates), batch_size):
            batch = status_updates[i:i+batch_size]
            
            # 直接批量更新，不使用事务
            for server_ip, status in batch:
                await IPUser.filter(ip=server_ip).update(is_connectable=status)
                
                # 更新缓存中服务器的状态
                for server in server_cache.servers:
                    if server["ip"] == server_ip:
                        server["is_connectable"] = status
                        break
                
                # 更新失败/成功服务器集合
                if status:
                    server_cache.mark_success(server_ip)
                else:
                    server_cache.mark_failed(server_ip)
            
            logger.debug(f"已批量更新 {len(batch)} 台服务器状态")
            
    except Exception as e:
        logger.error(f"批量更新服务器状态失败: {str(e)}")

class BaseMonitor:
    """基础监控类，提供共享功能"""
    
    def __init__(self, monitor_name: str, default_interval: int = 300):
        """
        初始化监控
        
        Args:
            monitor_name: 监控名称
            default_interval: 默认监控间隔(秒)
        """
        self.monitor_name = monitor_name
        self.default_interval = default_interval
        
    async def connect_to_server(self, server: Dict[str, Any], max_retries: int = 2) -> Tuple[Optional[paramiko.SSHClient], bool]:
        """
        连接到服务器，支持SSH密钥认证和密码认证

        Args:
            server: 服务器信息
            max_retries: 最大重试次数

        Returns:
            Tuple[Optional[SSHClient], bool]: (SSH客户端, 是否连接成功)
        """
        retry_count = 0
        last_error = None

        while retry_count <= max_retries:
            try:
                # 尝试连接服务器
                if retry_count == 0:
                    logger.info(f"正在连接到服务器 {server['ip']}...")
                else:
                    logger.debug(f"正在重试({retry_count}/{max_retries})连接服务器 {server['ip']}...")

                # 在线程池中执行SSH连接操作，避免阻塞事件循环
                connect_timeout = 10  # 减少默认超时时间从15秒到10秒

                # 定义将在线程池中执行的SSH连接函数
                def ssh_connect():
                    return SSHConnectionManager.connect_to_server(server, connect_timeout)

                # 在线程池中执行SSH连接，并设置超时时间
                connect_task = run_in_threadpool(ssh_connect)
                ssh_client, connected = await asyncio.wait_for(connect_task, timeout=connect_timeout + 2)  # 额外加2秒缓冲

                if connected and ssh_client:
                    # 连接成功，更新服务器状态
                    await update_server_status(server["ip"], True)
                    logger.debug(f"服务器 {server['ip']} 连接成功")

                    # 测试连接是否有效
                    transport = ssh_client.get_transport()
                    if transport and transport.is_active():
                        return ssh_client, True
                    else:
                        logger.warning(f"服务器 {server['ip']} 连接不活跃")
                        ssh_client.close()
                        retry_count += 1
                        continue
                else:
                    # 连接失败，重试
                    retry_count += 1
                    last_error = Exception("SSH连接失败")
                    continue

            except asyncio.TimeoutError:
                # 连接超时
                logger.warning(f"连接到服务器 {server['ip']} 超时")
                last_error = TimeoutError("SSH连接超时")
                retry_count += 1

            except Exception as e:
                # 其他错误
                logger.warning(f"服务器 {server['ip']} 连接出现错误: {str(e)}")
                last_error = e
                retry_count += 1

                if retry_count <= max_retries:
                    # 指数退避，第1次等待1秒，第2次等待4秒
                    wait_time = 2 ** retry_count
                    logger.debug(f"连接失败: {str(e)}，{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.warning(f"服务器 {server['ip']} 在 {max_retries} 次尝试后仍然连接失败")
                    break

        # 所有重试都失败，更新状态为不可连接
        await update_server_status(server["ip"], False)

        if last_error:
            logger.warning(f"服务器 {server['ip']} 连接失败: {str(last_error)}")

        return None, False
    
    async def monitor_server(self, server: Dict[str, Any], data_collector: Callable, data_saver: Callable) -> bool:
        """
        监控单个服务器
        
        Args:
            server: 服务器信息
            data_collector: 收集数据的函数，接收SSH客户端作为参数
            data_saver: 保存数据的函数，接收服务器IP和收集的数据作为参数
        
        Returns:
            bool: 监控是否成功
        """
        ssh_client = None
        try:
            # 建立SSH连接
            ssh_client, connected = await self.connect_to_server(server)
            
            if not connected or ssh_client is None:
                logger.warning(f"无法连接到服务器 {server['ip']}，跳过监控")
                return False
            
            # 检查SSH连接是否有效
            transport = ssh_client.get_transport()
            if not transport or not transport.is_active():
                logger.warning(f"服务器 {server['ip']} 的SSH连接不活跃，无法监控")
                return False
                
            # 获取监控数据
            logger.info(f"正在获取服务器 {server['ip']} 的{self.monitor_name}信息...")
            
            # 在线程池中执行数据收集操作，避免阻塞事件循环
            try:
                # 设置较短的超时时间，防止单个服务器监控阻塞整个系统
                data = await asyncio.wait_for(
                    run_in_threadpool(data_collector, ssh_client),
                    timeout=45 if self.monitor_name == "网卡" else 30  # 为网卡监控增加超时时间
                )
            except asyncio.TimeoutError:
                logger.warning(f"获取服务器 {server['ip']} 的{self.monitor_name}数据超时")
                return False
            except Exception as e:
                logger.error(f"获取服务器 {server['ip']} 的{self.monitor_name}数据失败: {str(e)}")
                import traceback
                logger.error(f"异常堆栈: {traceback.format_exc()}")
                return False
            
            # 保存监控数据
            if data:
                try:
                    await data_saver(server["ip"], data)
                    return True
                except Exception as e:
                    logger.error(f"保存服务器 {server['ip']} 的{self.monitor_name}数据失败: {str(e)}")
                    import traceback
                    logger.error(f"异常堆栈: {traceback.format_exc()}")
                    return False
            else:
                logger.warning(f"服务器 {server['ip']} 没有返回{self.monitor_name}信息")
                return False
                
        except Exception as e:
            logger.error(f"监控服务器 {server['ip']} {self.monitor_name}失败: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return False
            
        finally:
            # 确保SSH连接关闭
            if ssh_client:
                try:
                    ssh_client.close()
                    logger.debug(f"已关闭服务器 {server['ip']} 的SSH连接")
                except Exception as e:
                    logger.debug(f"关闭SSH连接时出错: {str(e)}")
    
    async def process_server_batch(self, servers: List[Dict[str, Any]], 
                                  data_collector: Callable, 
                                  data_saver: Callable,
                                  semaphore: asyncio.Semaphore) -> List[bool]:
        """
        处理一批服务器的监控
        
        Args:
            servers: 服务器列表
            data_collector: 收集数据的函数
            data_saver: 保存数据的函数
            semaphore: 控制并发的信号量
            
        Returns:
            List[bool]: 每台服务器的监控结果
        """
        async def monitor_with_semaphore(server):
            """使用信号量控制并发的监控函数"""
            async with semaphore:
                return await self.monitor_server(server, data_collector, data_saver)
        
        # 创建任务并并行执行
        tasks = [monitor_with_semaphore(server) for server in servers]
        return await asyncio.gather(*tasks)
    
    async def monitor_all_servers(self, data_collector: Callable, data_saver: Callable,
                                 only_connectable: bool = True) -> None:
        """
        监控所有服务器
        
        Args:
            data_collector: 收集数据的函数
            data_saver: 保存数据的函数
            only_connectable: 是否只监控可连通的服务器
        """
        # 获取服务器列表，只获取可连通的服务器
        servers = await get_all_servers(only_connectable)
        # 不需要优先失败的服务器，因为我们只要可连通的
        if not only_connectable and server_cache.failed_servers:
            servers = server_cache.prioritize_servers()
        
        if not servers:
            logger.warning(f"未找到{'可连通的' if only_connectable else ''}服务器")
            return
        
        # 确定最佳并发数和批量大小
        concurrency = get_optimal_concurrency()
        batch_size = min(50, len(servers))  # 减少每批服务器数量，避免长时间阻塞
        semaphore = asyncio.Semaphore(concurrency)
        
        logger.info(f"开始监控 {len(servers)} 台服务器的{self.monitor_name}状态，并发数: {concurrency}, 批处理大小: {batch_size}")
        
        # 分批处理服务器
        success_count = 0
        
        # 按批次处理服务器
        for i in range(0, len(servers), batch_size):
            batch = servers[i:i+batch_size]
            logger.debug(f"开始处理第 {i//batch_size + 1} 批服务器，共 {len(batch)} 台")
            
            # 处理当前批次并收集结果，添加超时控制
            try:
                # 设置批处理超时，防止某一批服务器阻塞太久
                batch_timeout = 120 if self.monitor_name == "网卡" else 60  # 网卡监控每批最多处理120秒
                results = await asyncio.wait_for(
                    self.process_server_batch(batch, data_collector, data_saver, semaphore),
                    timeout=batch_timeout
                )
                
                # 统计成功的数量
                batch_success = sum(1 for result in results if result)
                success_count += batch_success
                
                logger.debug(f"第 {i//batch_size + 1} 批处理完成，{batch_success}/{len(batch)} 台服务器监控成功")
                
                # 在批次之间添加短暂暂停，避免连续高负载
                if i + batch_size < len(servers):
                    await asyncio.sleep(0.5)
                    
            except asyncio.TimeoutError:
                # 如果批处理超时，记录日志但继续处理下一批
                logger.warning(f"处理第 {i//batch_size + 1} 批服务器监控超时，继续下一批")
                
            except Exception as e:
                # 处理其他异常，但不中断整个监控流程
                logger.error(f"处理第 {i//batch_size + 1} 批服务器时出错: {str(e)}")
        
        # 记录最终统计结果
        logger.info(f"{self.monitor_name}监控完成，共 {len(servers)} 台服务器，其中 {success_count} 台监控成功")
    
    async def start_monitoring(self, data_collector: Callable, data_saver: Callable,
                              only_connectable: bool = True, interval_seconds: int = None) -> None:
        """
        开始定时监控
        
        Args:
            data_collector: 收集数据的函数
            data_saver: 保存数据的函数
            only_connectable: 是否只监控可连通的服务器
            interval_seconds: 监控间隔(秒)，如果为None则使用默认值
        """
        interval = interval_seconds if interval_seconds is not None else self.default_interval
        logger.info(f"{self.monitor_name}监控服务已启动，监控间隔: {interval}秒")
        
        consecutive_errors = 0
        max_consecutive_errors = 5  # 最多允许连续5次错误
        
        while True:
            try:
                # 记录当前时间
                now = datetime.now(SHANGHAI_TZ)
                logger.debug(f"开始{self.monitor_name}监控周期 - {now.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 执行监控，带全局超时保护
                cycle_timeout = max(interval // 2, 300)  # 单次监控周期的超时时间，最少5分钟，最多不超过间隔的一半
                try:
                    # 添加超时控制，确保监控周期不会无限期运行
                    await asyncio.wait_for(
                        self.monitor_all_servers(data_collector, data_saver, only_connectable),
                        timeout=cycle_timeout
                    )
                    # 成功完成，重置错误计数
                    consecutive_errors = 0
                except asyncio.TimeoutError:
                    logger.error(f"{self.monitor_name}监控周期超时，已运行超过 {cycle_timeout} 秒")
                    consecutive_errors += 1
                
                # 等待下一次监控前检查是否应该继续
                if consecutive_errors >= max_consecutive_errors:
                    logger.critical(f"{self.monitor_name}监控连续 {consecutive_errors} 次出错，暂停服务10分钟后重试")
                    await asyncio.sleep(600)  # 遇到严重问题，暂停10分钟
                    consecutive_errors = 0  # 重置错误计数
                else:
                    # 正常等待下一个监控周期
                    logger.debug(f"等待 {interval} 秒后进行下一次{self.monitor_name}监控...")
                    await asyncio.sleep(interval)
                    
            except asyncio.CancelledError:
                # 优雅地处理取消操作
                logger.info(f"{self.monitor_name}监控服务被取消")
                break
                
            except Exception as e:
                # 处理其他未预期的错误
                logger.error(f"{self.monitor_name}监控过程出错: {str(e)}")
                # 记录完整的堆栈跟踪信息
                import traceback
                logger.error(traceback.format_exc())
                
                # 增加连续错误计数
                consecutive_errors += 1
                
                # 发生错误时等待，时间根据连续错误次数指数增长
                wait_time = min(60 * (2 ** min(consecutive_errors - 1, 3)), 600)  # 最多等待10分钟
                logger.warning(f"{self.monitor_name}监控出错后等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time) 