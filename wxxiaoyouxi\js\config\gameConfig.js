/**
 * 飞行棋游戏配置
 */

// 玩家颜色配置
export const PLAYER_COLORS = {
  RED: 'red',
  YELLOW: 'yellow',
  BLUE: 'blue',
  GREEN: 'green'
};

// 棋子状态
export const PIECE_STATUS = {
  BASE: 'base',      // 在基地
  FLYING: 'flying',  // 飞行中
  RUNWAY: 'runway',  // 在跑道上
  SAFE: 'safe',      // 在安全区域
  FINISHED: 'finished' // 已完成
};

// 游戏状态
export const GAME_STATUS = {
  WAITING: 'waiting',  // 等待开始
  PLAYING: 'playing',  // 游戏中
  FINISHED: 'finished' // 游戏结束
};

// 棋盘配置
export const BOARD_CONFIG = {
  // 棋盘总格子数
  totalCells: 52,
  
  // 安全跑道长度
  safeRunwayLength: 6,
  
  // 每种颜色的格子位置
  coloredCells: {
    [PLAYER_COLORS.RED]: [1, 14, 27, 40],
    [PLAYER_COLORS.YELLOW]: [4, 17, 30, 43],
    [PLAYER_COLORS.BLUE]: [7, 20, 33, 46],
    [PLAYER_COLORS.GREEN]: [10, 23, 36, 49]
  },
  
  // 飞行格子位置
  flyingCells: {
    [PLAYER_COLORS.RED]: [5, 18],
    [PLAYER_COLORS.YELLOW]: [18, 31],
    [PLAYER_COLORS.BLUE]: [31, 44],
    [PLAYER_COLORS.GREEN]: [44, 5]
  },
  
  // 起点位置
  startPoints: {
    [PLAYER_COLORS.RED]: 1,
    [PLAYER_COLORS.YELLOW]: 14,
    [PLAYER_COLORS.BLUE]: 27,
    [PLAYER_COLORS.GREEN]: 40
  }
};

// 棋盘布局配置 - 相对坐标 (将在渲染时根据屏幕尺寸进行缩放)
export const BOARD_LAYOUT = {
  // 棋盘中心点
  centerX: 375,
  centerY: 667,
  
  // 格子大小
  cellSize: 40,
  
  // 基地区域配置
  baseAreas: {
    [PLAYER_COLORS.RED]: { x: 150, y: 150, radius: 100 },
    [PLAYER_COLORS.YELLOW]: { x: 600, y: 150, radius: 100 },
    [PLAYER_COLORS.BLUE]: { x: 600, y: 1184, radius: 100 },
    [PLAYER_COLORS.GREEN]: { x: 150, y: 1184, radius: 100 }
  },
  
  // 棋子在基地的初始位置
  basePositions: {
    [PLAYER_COLORS.RED]: [
      { x: 120, y: 120 }, { x: 180, y: 120 },
      { x: 120, y: 180 }, { x: 180, y: 180 }
    ],
    [PLAYER_COLORS.YELLOW]: [
      { x: 570, y: 120 }, { x: 630, y: 120 },
      { x: 570, y: 180 }, { x: 630, y: 180 }
    ],
    [PLAYER_COLORS.BLUE]: [
      { x: 570, y: 1154 }, { x: 630, y: 1154 },
      { x: 570, y: 1214 }, { x: 630, y: 1214 }
    ],
    [PLAYER_COLORS.GREEN]: [
      { x: 120, y: 1154 }, { x: 180, y: 1154 },
      { x: 120, y: 1214 }, { x: 180, y: 1214 }
    ]
  }
};

// 游戏规则配置
export const GAME_RULES = {
  // 骰子点数为6时可以重复投掷
  repeatOnSix: true,
  
  // 连续投掷6点的最大次数 (超过则失去回合)
  maxConsecutiveSix: 3,
  
  // 必须投掷到6才能起飞
  requireSixToTakeOff: true,
  
  // 是否允许叠子
  allowStacking: true,
  
  // 是否自动选择棋子 (当只有一个可移动棋子时)
  autoSelectSinglePiece: true,
  
  // 进入安全区域的精确点数要求
  exactNumberToEnter: false
};

// AI难度配置
export const AI_DIFFICULTY = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
};

// 游戏模式
export const GAME_MODES = {
  SINGLE_PLAYER: 'single_player',  // 单人对战AI
  LOCAL_MULTIPLAYER: 'local_multiplayer',  // 本地多人
  ONLINE_MULTIPLAYER: 'online_multiplayer'  // 在线多人
};

// 动画配置
export const ANIMATION_CONFIG = {
  // 棋子移动动画
  pieceMove: {
    duration: 300,
    easing: 'easeInOutQuad'
  },
  // 骰子投掷动画
  diceRoll: {
    duration: 1000,
    rotations: 3
  },
  // 特效动画
  effects: {
    hit: { duration: 500 },
    takeOff: { duration: 800 },
    win: { duration: 2000 }
  }
};

// 音效配置
export const AUDIO_CONFIG = {
  // 音效文件路径
  files: {
    background: 'audio/bg_music.mp3',
    diceRoll: 'audio/dice_roll.mp3',
    move: 'audio/piece_move.mp3',
    takeOff: 'audio/take_off.mp3',
    jump: 'audio/jump.mp3',
    fly: 'audio/fly.mp3',
    hit: 'audio/hit.mp3',
    win: 'audio/win.mp3',
    buttonClick: 'audio/button_click.mp3'
  },
  // 音量设置
  volume: {
    background: 0.3,
    effects: 0.7
  }
};

// 资源路径
export const ASSETS = {
  // 棋盘背景
  boardBackground: 'images/board_bg.svg',
  
  // 骰子图片 (1-6点)
  dice: [
    'images/dice/dice_1.svg',
    'images/dice/dice_2.svg',
    'images/dice/dice_3.svg',
    'images/dice/dice_4.svg',
    'images/dice/dice_5.svg',
    'images/dice/dice_6.svg'
  ],
  
  // 棋子图片
  pieces: {
    [PLAYER_COLORS.RED]: 'images/pieces/red_piece.svg',
    [PLAYER_COLORS.YELLOW]: 'images/pieces/yellow_piece.svg',
    [PLAYER_COLORS.BLUE]: 'images/pieces/blue_piece.svg',
    [PLAYER_COLORS.GREEN]: 'images/pieces/green_piece.svg'
  },
  
  // 音效
  audio: AUDIO_CONFIG.files,
  
  // 按钮
  buttons: {
    start: 'images/buttons/start_btn.svg',
    settings: 'images/buttons/settings_btn.svg',
    roll: 'images/buttons/roll_btn.svg',
    back: 'images/buttons/back_btn.svg'
  }
};