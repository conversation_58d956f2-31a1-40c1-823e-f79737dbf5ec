<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElCard, ElTable, ElTableColumn, ElTag, ElButton, ElDialog, ElForm, ElFormItem, ElSelect, ElOption, ElMessage, ElCheckbox, ElAlert, ElProgress, ElIcon } from 'element-plus'
import { Operation, CaretRight, Delete, Check, Close } from '@element-plus/icons-vue'
import { monitorAPI, type MonitorItem, type BatchOperationRequest, type BatchOperationResponse, type ExecuteMonitorRequest, type ExecuteMonitorResponse } from '@/api/monitor'
import { serverAPI } from '@/api/server'

const loading = ref(false)
const executing = ref(false)
const monitorItems = ref<MonitorItem[]>([])
const serverList = ref<string[]>([])
const selectedItems = ref<number[]>([])

// Dialog states
const batchDialogVisible = ref(false)
const executeDialogVisible = ref(false)

// Form data
const batchForm = ref<BatchOperationRequest>({
  item_ids: [],
  operation: 'enable'
})

const executeForm = ref<ExecuteMonitorRequest>({
  monitor_item_id: 0,
  server_ips: []
})

// Results
const batchResult = ref<BatchOperationResponse | null>(null)
const executeResult = ref<ExecuteMonitorResponse | null>(null)

const operationOptions = [
  { label: '启用监控项', value: 'enable' },
  { label: '禁用监控项', value: 'disable' },
  { label: '删除监控项', value: 'delete' }
]

const fetchMonitorItems = async () => {
  try {
    loading.value = true
    const response = await monitorAPI.getMonitorItems()
    monitorItems.value = response
  } catch (error) {
    ElMessage.error('获取监控项列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const fetchServerList = async () => {
  try {
    const response = await serverAPI.getIPList()
    serverList.value = response
  } catch (error) {
    console.error('获取服务器列表失败:', error)
  }
}

const handleSelectionChange = (selection: MonitorItem[]) => {
  selectedItems.value = selection.map(item => item.id)
}

const handleBatchOperation = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要操作的监控项')
    return
  }
  
  batchForm.value.item_ids = [...selectedItems.value]
  batchDialogVisible.value = true
  batchResult.value = null
}

const handleExecuteMonitor = () => {
  if (selectedItems.value.length !== 1) {
    ElMessage.warning('请选择一个监控项进行执行')
    return
  }
  
  executeForm.value.monitor_item_id = selectedItems.value[0]
  executeForm.value.server_ips = []
  executeDialogVisible.value = true
  executeResult.value = null
}

const handleBatchSubmit = async () => {
  try {
    loading.value = true
    const response = await monitorAPI.batchOperation(batchForm.value)
    batchResult.value = response
    
    if (response.success_count > 0) {
      ElMessage.success(`成功操作 ${response.success_count} 个监控项`)
      fetchMonitorItems() // Refresh the list
    }
    
    if (response.failed_count > 0) {
      ElMessage.warning(`${response.failed_count} 个监控项操作失败`)
    }
  } catch (error) {
    ElMessage.error('批量操作失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleExecuteSubmit = async () => {
  try {
    executing.value = true
    const response = await monitorAPI.executeMonitor(executeForm.value)
    executeResult.value = response
    
    if (response.success) {
      ElMessage.success('监控项执行成功')
    } else {
      ElMessage.warning('监控项执行完成，但部分服务器失败')
    }
  } catch (error) {
    ElMessage.error('执行监控项失败')
    console.error(error)
  } finally {
    executing.value = false
  }
}

const handleCancel = () => {
  batchDialogVisible.value = false
  executeDialogVisible.value = false
  batchResult.value = null
  executeResult.value = null
}

const selectAll = () => {
  const tableRef = document.querySelector('.el-table') as any
  if (tableRef) {
    monitorItems.value.forEach(item => {
      tableRef.toggleRowSelection(item, true)
    })
  }
}

const clearSelection = () => {
  const tableRef = document.querySelector('.el-table') as any
  if (tableRef) {
    tableRef.clearSelection()
  }
}

const getOperationText = (operation: string) => {
  const textMap: Record<string, string> = {
    enable: '启用',
    disable: '禁用',
    delete: '删除'
  }
  return textMap[operation] || operation
}

const getOperationColor = (operation: string) => {
  const colorMap: Record<string, string> = {
    enable: '#10b981',
    disable: '#f59e0b',
    delete: '#ef4444'
  }
  return colorMap[operation] || '#6b7280'
}

const getDataTypeTag = (dataType: string) => {
  const typeMap: Record<string, { type: string; text: string }> = {
    string: { type: 'primary', text: '字符串' },
    number: { type: 'success', text: '数字' },
    json: { type: 'warning', text: 'JSON' }
  }
  return typeMap[dataType] || { type: 'info', text: dataType }
}

const getExecutionResultIcon = (success: boolean) => {
  return success ? 'Check' : 'Close'
}

const getExecutionResultColor = (success: boolean) => {
  return success ? '#10b981' : '#ef4444'
}

onMounted(() => {
  fetchMonitorItems()
  fetchServerList()
})
</script>

<template>
  <div class="batch-operations">
    <div class="page-header">
      <div class="header-content">
        <h2>批量操作</h2>
        <p>对多个监控项进行批量管理和执行操作</p>
      </div>
      <div class="header-actions">
        <ElButton @click="selectAll">全选</ElButton>
        <ElButton @click="clearSelection">清空选择</ElButton>
      </div>
    </div>

    <!-- Selection Info -->
    <ElAlert
      v-if="selectedItems.length > 0"
      :title="`已选择 ${selectedItems.length} 个监控项`"
      type="info"
      :closable="false"
      show-icon
      class="selection-alert"
    >
      <template #default>
        <div class="selection-actions">
          <ElButton type="primary" :icon="Operation" @click="handleBatchOperation">
            批量操作
          </ElButton>
          <ElButton 
            type="success" 
            :icon="CaretRight" 
            @click="handleExecuteMonitor"
            :disabled="selectedItems.length !== 1"
          >
            立即执行
          </ElButton>
        </div>
      </template>
    </ElAlert>

    <!-- Monitor Items Table -->
    <ElCard class="table-card">
      <ElTable 
        :data="monitorItems" 
        :loading="loading"
        stripe
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <ElTableColumn type="selection" width="55" />
        
        <ElTableColumn prop="name" label="监控项名称" width="180">
          <template #default="{ row }">
            <div class="item-name">
              <span class="name-text">{{ row.name }}</span>
              <ElTag v-if="!row.enabled" type="info" size="small">已禁用</ElTag>
            </div>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="command" label="监控命令" show-overflow-tooltip>
          <template #default="{ row }">
            <code class="command-text">{{ row.command }}</code>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="category" label="分类" width="100">
          <template #default="{ row }">
            <ElTag v-if="row.category" size="small">{{ row.category }}</ElTag>
            <span v-else class="no-category">-</span>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="数据类型" width="100">
          <template #default="{ row }">
            <ElTag :type="getDataTypeTag(row.data_type).type" size="small">
              {{ getDataTypeTag(row.data_type).text }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="timeout" label="超时(秒)" width="90" />
        
        <ElTableColumn label="状态" width="80">
          <template #default="{ row }">
            <ElTag :type="row.enabled ? 'success' : 'info'" size="small">
              {{ row.enabled ? '启用' : '禁用' }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="created_at" label="创建时间" width="140">
          <template #default="{ row }">
            <span class="time-text">{{ new Date(row.created_at).toLocaleDateString() }}</span>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>

    <!-- Batch Operation Dialog -->
    <ElDialog
      v-model="batchDialogVisible"
      title="批量操作"
      width="600px"
      :before-close="handleCancel"
    >
      <ElForm :model="batchForm" label-width="100px">
        <ElFormItem label="操作类型">
          <ElSelect v-model="batchForm.operation" placeholder="请选择操作类型">
            <ElOption
              v-for="option in operationOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>
        
        <ElFormItem label="选择的项目">
          <div class="selected-items">
            <ElTag 
              v-for="id in batchForm.item_ids" 
              :key="id"
              size="small"
              style="margin-right: 8px; margin-bottom: 4px;"
            >
              {{ monitorItems.find(item => item.id === id)?.name || `ID: ${id}` }}
            </ElTag>
          </div>
        </ElFormItem>
        
        <ElAlert
          v-if="batchForm.operation === 'delete'"
          title="警告：删除操作不可恢复"
          description="删除监控项将永久移除其配置和历史数据，请谨慎操作"
          type="warning"
          :closable="false"
          show-icon
        />
      </ElForm>

      <!-- Batch Result -->
      <div v-if="batchResult" class="batch-result">
        <ElAlert
          :title="`操作完成：成功 ${batchResult.success_count} 项，失败 ${batchResult.failed_count} 项`"
          :type="batchResult.failed_count === 0 ? 'success' : 'warning'"
          :closable="false"
          show-icon
        />
        
        <div v-if="batchResult.failed_items.length" class="failed-items">
          <h4>失败项目：</h4>
          <div class="failed-list">
            <div v-for="failed in batchResult.failed_items" :key="failed.id" class="failed-item">
              <span class="failed-name">{{ monitorItems.find(item => item.id === failed.id)?.name || `ID: ${failed.id}` }}</span>
              <span class="failed-error">{{ failed.error }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancel">
            {{ batchResult ? '关闭' : '取消' }}
          </ElButton>
          <ElButton 
            v-if="!batchResult"
            type="primary" 
            :loading="loading" 
            @click="handleBatchSubmit"
          >
            执行操作
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- Execute Monitor Dialog -->
    <ElDialog
      v-model="executeDialogVisible"
      title="立即执行监控项"
      width="600px"
      :before-close="handleCancel"
    >
      <ElForm :model="executeForm" label-width="100px">
        <ElFormItem label="监控项">
          <span class="monitor-item-name">
            {{ monitorItems.find(item => item.id === executeForm.monitor_item_id)?.name }}
          </span>
        </ElFormItem>
        
        <ElFormItem label="目标服务器">
          <ElSelect 
            v-model="executeForm.server_ips" 
            multiple 
            placeholder="留空执行所有关联服务器"
            style="width: 100%"
          >
            <ElOption
              v-for="ip in serverList"
              :key="ip"
              :label="ip"
              :value="ip"
            />
          </ElSelect>
        </ElFormItem>
      </ElForm>

      <!-- Execute Result -->
      <div v-if="executeResult" class="execute-result">
        <ElAlert
          :title="executeResult.success ? '执行成功' : '执行完成（部分失败）'"
          :type="executeResult.success ? 'success' : 'warning'"
          :closable="false"
          show-icon
        />
        
        <div class="execution-results">
          <h4>执行结果：</h4>
          <div class="result-list">
            <div 
              v-for="(result, ip) in executeResult.results" 
              :key="ip"
              class="result-item"
            >
              <div class="result-header">
                <span class="server-ip">{{ ip }}</span>
                <ElIcon :color="getExecutionResultColor(result.success)">
                  <component :is="getExecutionResultIcon(result.success)" />
                </ElIcon>
              </div>
              <div v-if="result.success" class="result-data">
                <span class="data-label">输出：</span>
                <code class="data-value">{{ result.data }}</code>
              </div>
              <div v-if="result.error" class="result-error">
                <span class="error-label">错误：</span>
                <span class="error-message">{{ result.error }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancel">
            {{ executeResult ? '关闭' : '取消' }}
          </ElButton>
          <ElButton 
            v-if="!executeResult"
            type="success" 
            :loading="executing" 
            @click="handleExecuteSubmit"
          >
            立即执行
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.batch-operations {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.selection-alert {
  margin-bottom: 24px;
}

.selection-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.item-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  color: #1f2937;
}

.command-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  color: #374151;
}

.no-category {
  color: #9ca3af;
}

.time-text {
  font-size: 12px;
  color: #6b7280;
}

.selected-items {
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
}

.batch-result {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.failed-items {
  margin-top: 12px;
}

.failed-items h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 14px;
}

.failed-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.failed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #fef2f2;
  border-radius: 4px;
  border-left: 3px solid #ef4444;
}

.failed-name {
  font-weight: 500;
  color: #374151;
}

.failed-error {
  color: #ef4444;
  font-size: 12px;
}

.monitor-item-name {
  font-weight: 500;
  color: #1f2937;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
}

.execute-result {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.execution-results h4 {
  margin: 12px 0 8px 0;
  color: #374151;
  font-size: 14px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #fafafa;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.server-ip {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 500;
  color: #3b82f6;
}

.result-data,
.result-error {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.data-label,
.error-label {
  font-weight: 500;
  color: #374151;
  min-width: 40px;
}

.data-value {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  color: #374151;
  flex: 1;
}

.error-message {
  color: #ef4444;
  flex: 1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-content h2 {
    font-size: 24px;
  }
  
  .header-actions {
    justify-content: flex-start;
  }
  
  .selection-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .result-item {
    padding: 8px;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>