<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="monitoring@localhost" uuid="5cc1b3b7-edb3-43ef-a322-c0dcd3b04628">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>监控</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>**************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="监控数据库" uuid="ed693f03-7784-42eb-8fdc-517588f89f1f">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>jdbc:mysql://***********:3306/monitoring</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>