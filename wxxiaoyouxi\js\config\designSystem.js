/**
 * 设计系统配置
 * 统一的扁平化设计风格配置
 */

// 颜色系统
export const COLORS = {
  // 主色调
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e'
  },
  
  // 玩家颜色
  players: {
    red: {
      light: '#fef2f2',
      main: '#ef4444',
      dark: '#dc2626',
      darker: '#991b1b'
    },
    yellow: {
      light: '#fefce8',
      main: '#eab308',
      dark: '#ca8a04',
      darker: '#854d0e'
    },
    blue: {
      light: '#eff6ff',
      main: '#3b82f6',
      dark: '#2563eb',
      darker: '#1d4ed8'
    },
    green: {
      light: '#f0fdf4',
      main: '#22c55e',
      dark: '#16a34a',
      darker: '#15803d'
    }
  },
  
  // 中性色
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  },
  
  // 语义化颜色
  semantic: {
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6'
  },
  
  // 背景色
  background: {
    primary: '#ffffff',
    secondary: '#f8fafc',
    tertiary: '#f1f5f9'
  }
};

// 阴影系统
export const SHADOWS = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
};

// 圆角系统
export const BORDER_RADIUS = {
  none: '0',
  sm: '2px',
  base: '4px',
  md: '6px',
  lg: '8px',
  xl: '12px',
  '2xl': '16px',
  '3xl': '24px',
  full: '9999px'
};

// 间距系统
export const SPACING = {
  0: '0',
  1: '4px',
  2: '8px',
  3: '12px',
  4: '16px',
  5: '20px',
  6: '24px',
  8: '32px',
  10: '40px',
  12: '48px',
  16: '64px',
  20: '80px',
  24: '96px',
  32: '128px'
};

// 字体系统
export const TYPOGRAPHY = {
  fontFamily: {
    sans: '"Microsoft YaHei", "Segoe UI", system-ui, -apple-system, sans-serif',
    mono: '"Consolas", "Monaco", "Courier New", monospace'
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px',
    '4xl': '36px'
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75'
  }
};

// 动画系统
export const ANIMATIONS = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
};

// 组件样式预设
export const COMPONENT_STYLES = {
  button: {
    primary: {
      backgroundColor: COLORS.primary[500],
      color: '#ffffff',
      borderRadius: BORDER_RADIUS.md,
      padding: `${SPACING[3]} ${SPACING[6]}`,
      fontSize: TYPOGRAPHY.fontSize.base,
      fontWeight: TYPOGRAPHY.fontWeight.medium,
      boxShadow: SHADOWS.sm,
      border: 'none'
    },
    secondary: {
      backgroundColor: COLORS.neutral[100],
      color: COLORS.neutral[700],
      borderRadius: BORDER_RADIUS.md,
      padding: `${SPACING[3]} ${SPACING[6]}`,
      fontSize: TYPOGRAPHY.fontSize.base,
      fontWeight: TYPOGRAPHY.fontWeight.medium,
      boxShadow: SHADOWS.sm,
      border: `1px solid ${COLORS.neutral[200]}`
    }
  },
  
  panel: {
    default: {
      backgroundColor: COLORS.background.primary,
      borderRadius: BORDER_RADIUS.lg,
      padding: SPACING[6],
      boxShadow: SHADOWS.md,
      border: `1px solid ${COLORS.neutral[200]}`
    },
    elevated: {
      backgroundColor: COLORS.background.primary,
      borderRadius: BORDER_RADIUS.xl,
      padding: SPACING[8],
      boxShadow: SHADOWS.lg,
      border: 'none'
    }
  },
  
  cell: {
    normal: {
      backgroundColor: COLORS.background.primary,
      borderRadius: BORDER_RADIUS.base,
      border: `1px solid ${COLORS.neutral[200]}`,
      boxShadow: SHADOWS.sm
    },
    highlighted: {
      backgroundColor: COLORS.primary[50],
      borderRadius: BORDER_RADIUS.base,
      border: `2px solid ${COLORS.primary[400]}`,
      boxShadow: SHADOWS.md
    }
  }
};

// 工具函数
export const DESIGN_UTILS = {
  /**
   * 获取玩家颜色
   */
  getPlayerColor(playerColor, variant = 'main') {
    const colorMap = {
      'red': COLORS.players.red,
      'yellow': COLORS.players.yellow,
      'blue': COLORS.players.blue,
      'green': COLORS.players.green
    };
    
    return colorMap[playerColor]?.[variant] || COLORS.neutral[500];
  },
  
  /**
   * 创建渐变色
   */
  createGradient(ctx, x1, y1, x2, y2, colorStops) {
    const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
    colorStops.forEach(({ offset, color }) => {
      gradient.addColorStop(offset, color);
    });
    return gradient;
  },
  
  /**
   * 绘制圆角矩形
   */
  drawRoundRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  },
  
  /**
   * 应用阴影效果
   */
  applyShadow(ctx, shadowType = 'base') {
    const shadow = SHADOWS[shadowType];
    if (shadow && shadow !== 'none') {
      // 解析阴影字符串并应用到canvas
      ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
      ctx.shadowBlur = 4;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 2;
    }
  }
};
