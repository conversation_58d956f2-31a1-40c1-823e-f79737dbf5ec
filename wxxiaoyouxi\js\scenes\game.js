import Scene from './scene';
import Board from '../game/board';
import Piece from '../game/piece';
import Dice from '../game/dice';
import Button from '../ui/button';
import GameRules from '../game/rules';
import { GAME_STATUS, PLAYER_COLORS, ASSETS, PIECE_STATUS } from '../config/gameConfig';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';

/**
 * 游戏场景类
 * 实现棋盘渲染和游戏流程控制
 */
export default class GameScene extends Scene {
  // 棋盘
  board = null;
  // 骰子
  dice = null;
  // 棋子列表
  pieces = {};
  // 当前选中的棋子
  selectedPiece = null;
  // 可移动的棋子
  movablePieces = [];
  // 游戏状态文本
  statusText = '';
  // 返回按钮
  backButton = null;
  // 投掷骰子按钮
  rollButton = null;
  // 游戏结束回调
  onGameOver = null;
  // 返回大厅回调
  onBack = null;

  /**
   * 构造函数
   * @param {Object} options - 场景选项
   */
  constructor(options = {}) {
    super('game');
    
    this.onGameOver = options.onGameOver || null;
    this.onBack = options.onBack || null;
  }
  
  /**
   * 初始化场景
   */
  init() {
    super.init();
    
    // 初始化游戏状态
    this.initGameState();
    
    // 初始化游戏组件
    this.initBoard();
    this.initDice();
    this.initUI();
    
    // 绑定事件
    this.bindEvents();
  }
  
  /**
   * 初始化游戏状态
   */
  initGameState() {
    this.gameState = GAME_STATUS.WAITING;
    this.currentPlayerIndex = 0;
    this.diceValue = 0;
    this.consecutiveSix = 0;
    this.selectedPiece = null;
    this.movablePieces = [];
    this.isWaitingForMove = false;
    this.turnTimeLeft = 30; // 30秒倒计时
    this.turnTimer = null;
  }

  /**
   * 初始化棋盘
   */
  initBoard() {
    this.board = new Board();
    this.addElement(this.board);
  }

  /**
   * 初始化骰子
   */
  initDice() {
    const diceSize = 80;
    this.dice = new Dice(
      SCREEN_WIDTH / 2 - diceSize / 2,
      SCREEN_HEIGHT - diceSize - 20,
      diceSize
    );
    this.dice.setClickable(true);
    this.addElement(this.dice);
  }

  /**
   * 初始化UI
   */
  initUI() {
    // 创建返回按钮
    this.backButton = new Button({
      x: 20,
      y: 20,
      width: 80,
      height: 40,
      text: '返回',
      style: {
        backgroundColor: '#E74C3C',
        hoverColor: '#C0392B',
        pressedColor: '#A93226',
        textColor: '#FFFFFF',
        fontSize: 16,
        fontWeight: 'bold',
        borderRadius: 15,
        borderWidth: 2,
        borderColor: '#F1948A',
        shadowColor: 'rgba(231, 76, 60, 0.3)',
        shadowBlur: 8,
        shadowOffsetX: 0,
        shadowOffsetY: 4,
        gradient: true,
        glowEffect: false
      },
      onClick: () => {
        this.showExitConfirm();
      }
    });
    this.addElement(this.backButton);
    
    // 创建投掷骰子按钮
    this.rollButton = new Button({
      x: SCREEN_WIDTH / 2 - 60,
      y: SCREEN_HEIGHT - 150,
      width: 120,
      height: 50,
      text: '投掷骰子',
      style: {
        backgroundColor: '#27AE60',
        hoverColor: '#229954',
        pressedColor: '#1E8449',
        textColor: '#FFFFFF',
        fontSize: 18,
        fontWeight: 'bold',
        borderRadius: 25,
        borderWidth: 3,
        borderColor: '#58D68D',
        shadowColor: 'rgba(39, 174, 96, 0.4)',
        shadowBlur: 12,
        shadowOffsetX: 0,
        shadowOffsetY: 6,
        gradient: true,
        glowEffect: true
      },
      onClick: () => {
        this.rollDice();
      }
    });
    this.addElement(this.rollButton);
    
    // 初始化玩家信息面板
    this.initPlayerPanels();
  }
  
  /**
   * 初始化玩家信息面板
   */
  initPlayerPanels() {
    this.playerPanels = [];
    
    const panelWidth = 150;
    const panelHeight = 80;
    const margin = 20;
    
    // 为每个玩家创建信息面板
    for (let i = 0; i < 4; i++) {
      const panel = {
        x: margin + i * (panelWidth + margin),
        y: margin,
        width: panelWidth,
        height: panelHeight,
        isActive: false,
        player: null
      };
      
      this.playerPanels.push(panel);
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 事件绑定在父类中处理
  }
  
  /**
   * 处理点击事件
   */
  handleClick(x, y) {
    super.handleClick(x, y);
    
    // 如果游戏未开始或已结束，不处理点击
    if (this.gameState !== GAME_STATUS.PLAYING) {
      return;
    }
    
    // 检查是否点击了骰子
    if (this.dice && this.dice.checkClick(x, y)) {
      this.handleDiceClick();
      return;
    }
    
    // 检查是否点击了棋子
    if (this.pieces) {
      const currentPlayer = this.getCurrentPlayer();
      if (currentPlayer && this.pieces[currentPlayer.color]) {
        for (let piece of this.pieces[currentPlayer.color]) {
          if (piece.checkClick(x, y)) {
            this.handlePieceClick(piece);
            return;
          }
        }
      }
    }
    
    // 检查是否点击了棋盘（用于取消选择）
    if (this.board && this.board.checkClick(x, y)) {
      this.clearSelection();
    }
  }
  
  /**
   * 处理骰子点击
   */
  handleDiceClick() {
    // 只有在等待投掷骰子时才能点击
    if (this.gameState === GAME_STATUS.PLAYING && !this.isWaitingForMove) {
      const currentPlayer = this.getCurrentPlayer();
      if (currentPlayer && !currentPlayer.isAI) {
        this.rollDice();
      }
    }
  }
  
  /**
   * 处理棋子点击
   */
  handlePieceClick(piece) {
    // 只有在等待移动时才能点击棋子
    if (!this.isWaitingForMove) {
      return;
    }
    
    const currentPlayer = this.getCurrentPlayer();
    if (!currentPlayer || piece.color !== currentPlayer.color) {
      return;
    }
    
    // 检查棋子是否可移动
    const movablePieceData = this.movablePieces.find(p => p.id === piece.id);
    if (movablePieceData) {
      this.selectPiece(piece.id);
    }
  }
  
  /**
   * 处理触摸开始事件
   */
  handleTouchStart(x, y) {
    this.handleClick(x, y);
  }
  
  /**
   * 处理触摸移动事件
   */
  handleTouchMove(x, y) {
    // 可以在这里添加拖拽逻辑
  }
  
  /**
   * 处理触摸结束事件
   */
  handleTouchEnd(x, y) {
    // 触摸结束处理
  }
  
  /**
   * 处理键盘事件
   */
  handleKeyDown(keyCode) {
    switch (keyCode) {
      case 32: // 空格键 - 投掷骰子
        if (this.gameState === GAME_STATUS.PLAYING && !this.isWaitingForMove) {
          const currentPlayer = this.getCurrentPlayer();
          if (currentPlayer && !currentPlayer.isAI) {
            this.rollDice();
          }
        }
        break;
      case 27: // ESC键 - 显示退出确认
        this.showExitConfirm();
        break;
    }
  }
  
  /**
   * 获取当前玩家
   */
  getCurrentPlayer() {
    if (GameGlobal && GameGlobal.databus && GameGlobal.databus.players) {
      return GameGlobal.databus.players[GameGlobal.databus.currentPlayerIndex];
    }
    return null;
  }
  
  /**
   * 更新状态文本
   */
  updateStatusText(text) {
    this.statusText = text;
  }
  
  /**
   * 开始回合计时器
   */
  startTurnTimer() {
    this.clearTurnTimer();
    this.turnTimeLeft = 30;
    
    this.turnTimer = setInterval(() => {
      this.turnTimeLeft--;
      
      if (this.turnTimeLeft <= 0) {
        this.handleTurnTimeout();
      }
    }, 1000);
  }
  
  /**
   * 清除回合计时器
   */
  clearTurnTimer() {
    if (this.turnTimer) {
      clearInterval(this.turnTimer);
      this.turnTimer = null;
    }
  }
  
  /**
   * 处理回合超时
   */
  handleTurnTimeout() {
    this.clearTurnTimer();
    
    if (this.isWaitingForMove) {
      // 如果在等待移动，自动选择第一个可移动的棋子
      if (this.movablePieces.length > 0) {
        this.selectPiece(this.movablePieces[0].id);
      }
    } else {
      // 如果在等待投掷骰子，自动投掷
      this.rollDice();
    }
  }
  
  /**
   * 显示退出确认
   */
  showExitConfirm() {
    // 这里可以显示确认对话框
    // 暂时直接退出
    if (this.onBack) {
      this.onBack();
    }
  }
  
  /**
   * 开始新游戏
   * @param {Object} gameData - 游戏数据
   */
  startGame(gameData) {
    // 重置游戏状态
    this.resetGame();
    
    // 创建棋子
    this.createPieces(gameData.players);
    
    // 设置初始状态
    this.statusText = `${gameData.players[gameData.currentPlayerIndex].nickname} 的回合`;
    this.updateButtons(gameData);
  }
  
  /**
   * 重置游戏状态
   */
  resetGame() {
    this.pieces = {};
    this.selectedPiece = null;
    this.movablePieces = [];
    this.statusText = '';
  }
  
  /**
   * 创建棋子
   * @param {Array} players - 玩家数组
   */
  createPieces(players) {
    players.forEach(player => {
      // 为每个玩家创建棋子
      this.pieces[player.color] = [];
      
      player.pieces.forEach((pieceData, index) => {
        // 创建棋子实例
        const piece = new Piece(player.color, index);
        
        // 设置棋子状态
        piece.updateState(pieceData);
        
        // 设置棋子位置
        if (pieceData.status === PIECE_STATUS.BASE) {
          // 如果在基地，使用基地位置
          const basePosition = this.board.getPieceBasePosition(player.color, index);
          if (basePosition) {
            piece.setPosition(basePosition.x, basePosition.y, true);
          }
        } else if (pieceData.status === PIECE_STATUS.RUNWAY) {
          // 如果在跑道上，使用跑道位置
          const cell = this.board.getCellByPosition(pieceData.position);
          if (cell) {
            piece.setPosition(
              cell.x + this.board.cellSize / 2 - piece.width / 2,
              cell.y + this.board.cellSize / 2 - piece.height / 2,
              true
            );
          }
        } else if (pieceData.status === PIECE_STATUS.SAFE) {
          // 如果在安全区域，使用安全跑道位置
          const cell = this.board.getSafeRunwayCell(player.color, pieceData.position);
          if (cell) {
            piece.setPosition(
              cell.x + this.board.cellSize / 2 - piece.width / 2,
              cell.y + this.board.cellSize / 2 - piece.height / 2,
              true
            );
          }
        } else if (pieceData.status === PIECE_STATUS.FINISHED) {
          // 如果已完成，使用终点位置
          piece.setPosition(
            this.board.finishArea.x + this.board.cellSize / 2 - piece.width / 2,
            this.board.finishArea.y + this.board.cellSize / 2 - piece.height / 2,
            true
          );
        }
        
        // 添加到棋子列表
        this.pieces[player.color].push(piece);
        
        // 添加到场景元素
        this.addElement(piece);
      });
    });
  }
  
  /**
   * 更新游戏状态
   * @param {Object} gameData - 游戏数据
   */
  updateGame(gameData) {
    // 更新棋子状态
    this.updatePieces(gameData.players);
    
    // 更新游戏状态文本
    if (gameData.gameStatus === GAME_STATUS.FINISHED) {
      // 游戏结束
      const winner = gameData.players.find(p => 
        p.pieces.every(piece => piece.status === PIECE_STATUS.FINISHED)
      );
      
      if (winner) {
        this.statusText = `${winner.nickname} 获胜！`;
      } else {
        this.statusText = '游戏结束！';
      }
      
      // 禁用骰子按钮
      this.rollButton.setEnabled(false);
    } else {
      // 游戏进行中
      const currentPlayer = gameData.players[gameData.currentPlayerIndex];
      this.statusText = `${currentPlayer.nickname} 的回合`;
      
      // 更新按钮状态
      this.updateButtons(gameData);
      
      // 更新可移动棋子
      if (gameData.diceValue > 0) {
        this.updateMovablePieces(currentPlayer, gameData.diceValue);
      }
    }
  }
  
  /**
   * 更新棋子状态
   * @param {Array} players - 玩家数组
   */
  updatePieces(players) {
    players.forEach(player => {
      player.pieces.forEach((pieceData, index) => {
        const piece = this.pieces[player.color][index];
        
        // 更新棋子状态
        piece.updateState(pieceData);
        
        // 更新棋子位置
        this.updatePiecePosition(piece, pieceData);
      });
    });
  }
  
  /**
   * 更新棋子位置
   * @param {Object} piece - 棋子对象
   * @param {Object} pieceData - 棋子数据
   */
  updatePiecePosition(piece, pieceData) {
    if (pieceData.status === PIECE_STATUS.BASE) {
      // 如果在基地，使用基地位置
      const basePosition = this.board.getPieceBasePosition(piece.color, piece.index);
      if (basePosition) {
        piece.setPosition(basePosition.x, basePosition.y);
      }
    } else if (pieceData.status === PIECE_STATUS.RUNWAY) {
      // 如果在跑道上，使用跑道位置
      const cell = this.board.getCellByPosition(pieceData.position);
      if (cell) {
        piece.setPosition(
          cell.x + this.board.cellSize / 2 - piece.width / 2,
          cell.y + this.board.cellSize / 2 - piece.height / 2
        );
      }
    } else if (pieceData.status === PIECE_STATUS.SAFE) {
      // 如果在安全区域，使用安全跑道位置
      const cell = this.board.getSafeRunwayCell(piece.color, pieceData.position);
      if (cell) {
        piece.setPosition(
          cell.x + this.board.cellSize / 2 - piece.width / 2,
          cell.y + this.board.cellSize / 2 - piece.height / 2
        );
      }
    } else if (pieceData.status === PIECE_STATUS.FINISHED) {
      // 如果已完成，使用终点位置
      piece.setPosition(
        this.board.finishArea.x + this.board.cellSize / 2 - piece.width / 2,
        this.board.finishArea.y + this.board.cellSize / 2 - piece.height / 2
      );
    }
  }
  
  /**
   * 更新按钮状态
   * @param {Object} gameData - 游戏数据
   */
  updateButtons(gameData) {
    const currentPlayer = gameData.players[gameData.currentPlayerIndex];
    const isCurrentPlayerHuman = !currentPlayer.isAI;
    
    // 更新骰子按钮
    this.rollButton.setEnabled(isCurrentPlayerHuman && gameData.diceValue === 0);
    this.dice.setClickable(isCurrentPlayerHuman && gameData.diceValue === 0);
  }
  
  /**
   * 更新可移动棋子
   * @param {Object} player - 当前玩家
   * @param {number} diceValue - 骰子点数
   */
  updateMovablePieces(player, diceValue) {
    // 清除之前的选中状态
    this.clearSelection();
    
    // 添加空值检查，确保 player 和 player.pieces 有效
    if (!player || !player.pieces) {
      console.log('无效的玩家数据，无法更新可移动棋子');
      return;
    }
    
    // 获取可移动的棋子
    this.movablePieces = GameRules.getMovablePieces(player.pieces, diceValue);
    
    // 高亮显示可移动的棋子
    this.movablePieces.forEach(pieceData => {
      const piece = this.pieces[player.color].find(p => p.id === pieceData.id);
      if (piece) {
        piece.setHighlight(true);
      }
    });
    
    // 如果只有一个可移动的棋子且启用了自动选择，则自动选择该棋子
    if (this.movablePieces.length === 1 && GameGlobal.databus.autoSelectSinglePiece) {
      this.selectPiece(this.movablePieces[0].id);
    }
  }
  
  /**
   * 清除选中状态
   */
  clearSelection() {
    // 清除所有棋子的高亮和选中状态
    Object.values(this.pieces).forEach(colorPieces => {
      colorPieces.forEach(piece => {
        piece.setHighlight(false);
        piece.setSelected(false);
      });
    });
    
    // 清除选中的棋子
    this.selectedPiece = null;
    this.movablePieces = [];
  }
  
  /**
   * 投掷骰子
   */
  rollDice() {
    if (this.dice.isRolling || this.isWaitingForMove) return;
    
    // 禁用投掷按钮
    this.rollButton.setEnabled(false);
    this.dice.setClickable(false);
    
    // 投掷骰子
    const diceValue = GameGlobal.databus.rollDice();
    this.dice.roll(diceValue, (value) => {
      // 骰子投掷完成后的回调
      // 通知数据总线骰子点数
      GameGlobal.databus.diceValue = value;
      
      // 获取当前玩家并验证
      const currentPlayer = GameGlobal.databus.getCurrentPlayer();
      if (!currentPlayer) {
        console.log('无法获取当前玩家，跳过更新可移动棋子');
        return;
      }
      
      // 处理骰子结果
      this.handleDiceResult(value, currentPlayer);
    });
  }
  
  /**
   * 处理骰子结果
   */
  handleDiceResult(value, currentPlayer) {
    // 更新状态文本
    this.updateStatusText(`${currentPlayer.nickname}投出了${value}点`);
    
    // 检查连续投掷6的情况
    if (value === 6) {
      this.consecutiveSix++;
      if (this.consecutiveSix >= 3) {
        // 连续投掷3次6，跳过回合
        this.updateStatusText('连续投出3个6，跳过回合');
        this.consecutiveSix = 0;
        this.nextTurn();
        return;
      }
    } else {
      this.consecutiveSix = 0;
    }
    
    // 更新可移动棋子
    this.updateMovablePieces(currentPlayer, value);
    
    if (this.movablePieces.length === 0) {
      // 没有可移动的棋子
      this.updateStatusText('没有可移动的棋子');
      setTimeout(() => {
        this.nextTurn();
      }, 1500);
    } else if (this.movablePieces.length === 1) {
      // 只有一个可移动的棋子，自动选择
      const pieceToMove = this.movablePieces[0];
      this.selectPiece(pieceToMove.id);
      this.movePiece(this.selectedPiece, value);
    } else {
      // 多个可移动的棋子，等待玩家选择
      this.isWaitingForMove = true;
      this.updateStatusText('请选择要移动的棋子');
      
      // 如果是AI玩家，自动选择
      if (currentPlayer.isAI) {
        this.handleAIMove(currentPlayer, value);
      }
    }
  }
  
  /**
   * 检查游戏胜利
   */
  checkGameWin() {
    const currentPlayer = GameGlobal.databus.getCurrentPlayer();
    if (!currentPlayer) return false;
    
    // 检查当前玩家是否所有棋子都到达终点
    const finishedCount = currentPlayer.pieces.filter(
      piece => piece.status === PIECE_STATUS.FINISHED
    ).length;
    
    if (finishedCount === 4) {
      this.handleGameWin(currentPlayer);
      return true;
    }
    
    return false;
  }
  
  /**
   * 处理游戏胜利
   */
  handleGameWin(winner) {
    this.gameState = GAME_STATUS.FINISHED;
    this.clearTurnTimer();
    
    this.updateStatusText(`${winner.nickname}获胜！`);
    
    // 播放胜利音效
    if (window.audioManager) {
      window.audioManager.playSound('win');
    }
    
    // 延迟显示结果
    setTimeout(() => {
      if (this.onGameOver) {
        this.onGameOver(winner);
      }
    }, 3000);
  }
  
  /**
   * 下一个玩家回合
   */
  nextTurn() {
    // 重置回合状态
    this.consecutiveSix = 0;
    GameGlobal.databus.diceValue = 0;
    this.isWaitingForMove = false;
    this.clearSelection();
    
    // 切换到下一个玩家
    GameGlobal.databus.nextPlayer();
    
    // 更新游戏状态
    this.updateGame(GameGlobal.databus);
    
    // 开始新回合计时
    this.startTurnTimer();
    
    // 如果是AI玩家，自动开始
    const currentPlayer = GameGlobal.databus.getCurrentPlayer();
    if (currentPlayer && currentPlayer.isAI) {
      setTimeout(() => {
        this.rollDice();
      }, 1000);
    }
  }
  
  /**
   * 处理AI移动
   * @param {Object} player - AI玩家
   * @param {number} diceValue - 骰子点数
   */
  handleAIMove(player, diceValue) {
    // 简单AI实现：随机选择一个可移动的棋子
    setTimeout(() => {
      if (this.movablePieces.length > 0) {
        const randomIndex = Math.floor(Math.random() * this.movablePieces.length);
        const pieceToMove = this.movablePieces[randomIndex];
        this.selectPiece(pieceToMove.id);
      } else {
        // 如果没有可移动的棋子，切换到下一个玩家
        GameGlobal.databus.nextPlayer();
        this.updateGame(GameGlobal.databus);
      }
    }, 1000); // 延迟1秒，模拟思考时间
  }
  
  /**
   * 选择棋子
   * @param {string} pieceId - 棋子ID
   */
  selectPiece(pieceId) {
    // 查找棋子
    let selectedPiece = null;
    
    Object.values(this.pieces).forEach(colorPieces => {
      colorPieces.forEach(piece => {
        if (piece.id === pieceId) {
          selectedPiece = piece;
          // 设置选中状态
          piece.setSelected(true);
        } else {
          // 清除其他棋子的选中状态
          piece.setSelected(false);
        }
      });
    });
    
    // 保存选中的棋子
    this.selectedPiece = selectedPiece;
    
    // 如果选中了棋子，移动它
    if (this.selectedPiece) {
      this.movePiece(this.selectedPiece, GameGlobal.databus.diceValue);
    }
  }
  
  /**
   * 移动棋子
   * @param {Object} piece - 棋子对象
   * @param {number} steps - 移动步数
   */
  movePiece(piece, steps) {
    // 获取棋子数据
    const pieceData = GameGlobal.databus.players
      .flatMap(player => player.pieces)
      .find(p => p.id === piece.id);
    
    if (!pieceData) return;
    
    // 使用规则引擎移动棋子
    const moveResult = GameRules.movePiece(pieceData, steps);
    
    if (moveResult.success) {
      // 更新棋子状态
      pieceData.status = moveResult.newState.status;
      pieceData.position = moveResult.newState.position;
      pieceData.isStacked = moveResult.newState.isStacked;
      pieceData.stackWith = moveResult.newState.stackWith;
      
      // 更新棋子位置
      this.updatePiecePosition(piece, pieceData);
      
      // 检查碰撞
      const allPieces = GameGlobal.databus.players.flatMap(player => player.pieces);
      const collision = GameRules.checkCollision(pieceData, pieceData.position, allPieces);
      
      // 处理碰撞
      if (collision.hasCollision) {
        const collisionResult = GameRules.handleCollision(pieceData, collision);
        
        // 更新被击落的棋子
        collisionResult.hitPieces.forEach(hitPieceData => {
          const hitPiece = this.findPieceById(hitPieceData.id);
          if (hitPiece) {
            // 更新状态
            const originalPieceData = GameGlobal.databus.players
              .flatMap(player => player.pieces)
              .find(p => p.id === hitPieceData.id);
            
            if (originalPieceData) {
              originalPieceData.status = hitPieceData.status;
              originalPieceData.position = hitPieceData.position;
              originalPieceData.isStacked = hitPieceData.isStacked;
              originalPieceData.stackWith = hitPieceData.stackWith;
              
              // 播放被击落动画
              this.playHitAnimation(hitPiece, originalPieceData);
            }
          }
        });
        
        // 更新当前棋子的叠子状态
        pieceData.isStacked = collisionResult.piece.isStacked;
        pieceData.stackWith = collisionResult.piece.stackWith;
      }
      
      // 检查游戏是否结束
      const currentPlayer = GameGlobal.databus.getCurrentPlayer();
      if (GameRules.checkGameOver(currentPlayer)) {
        // 游戏结束
        GameGlobal.databus.gameOver(currentPlayer.id);
        
        // 调用游戏结束回调
        if (this.onGameOver) {
          this.onGameOver(currentPlayer);
        }
      } else {
        // 检查是否可以继续投掷
        if (GameRules.canRollAgain(steps, GameGlobal.databus.consecutiveSix)) {
          // 重置骰子点数，允许再次投掷
          GameGlobal.databus.diceValue = 0;
          this.rollButton.setEnabled(true);
          this.dice.setClickable(true);
        } else {
          // 切换到下一个玩家
          GameGlobal.databus.diceValue = 0;
          GameGlobal.databus.nextPlayer();
          
          // 更新游戏状态
          this.updateGame(GameGlobal.databus);
        }
      }
    }
    
    // 清除选中状态
    this.clearSelection();
  }
  
  /**
   * 播放被击落动画
   * @param {Object} piece - 棋子对象
   * @param {Object} pieceData - 棋子数据
   */
  playHitAnimation(piece, pieceData) {
    // 获取起始位置
    const startX = piece.x;
    const startY = piece.y;
    
    // 获取目标位置 (基地位置)
    const basePosition = this.board.getPieceBasePosition(piece.color, piece.index);
    
    if (basePosition) {
      // 播放被击落动画
      piece.playHitAnimation(
        startX,
        startY,
        basePosition.x,
        basePosition.y,
        () => {
          // 动画完成后更新棋子状态
          piece.updateState(pieceData);
        }
      );
    }
  }
  
  /**
   * 查找棋子
   * @param {string} pieceId - 棋子ID
   * @returns {Object} 棋子对象
   */
  findPieceById(pieceId) {
    for (const colorPieces of Object.values(this.pieces)) {
      for (const piece of colorPieces) {
        if (piece.id === pieceId) {
          return piece;
        }
      }
    }
    return null;
  }
  
  /**
   * 触摸开始事件处理
   * @param {Object} e - 触摸事件对象
   */
  onTouchStart(e) {
    if (!this.active) return false;
    
    // 调用父类的触摸处理
    const handled = super.onTouchStart(e);
    if (handled) return true;
    
    // 处理棋子点击
    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;
    
    // 检查是否点击了骰子
    if (this.dice.checkClick(x, y)) {
      this.rollDice();
      return true;
    }
    
    // 检查是否点击了可移动的棋子
    const currentPlayer = GameGlobal.databus.getCurrentPlayer();
    if (!currentPlayer.isAI && GameGlobal.databus.diceValue > 0) {
      for (const pieceData of this.movablePieces) {
        const piece = this.findPieceById(pieceData.id);
        if (piece && piece.checkClick(x, y)) {
          this.selectPiece(piece.id);
          return true;
        }
      }
    }
    
    return false;
  }
  
  /**
   * 更新游戏场景
   */
  update() {
    super.update();
    
    // 更新游戏组件
    if (this.board) {
      this.board.update && this.board.update();
    }
    
    if (this.dice) {
      this.dice.update();
    }
    
    // 更新所有棋子
    if (this.pieces) {
      Object.values(this.pieces).forEach(colorPieces => {
        colorPieces.forEach(piece => {
          piece.update();
        });
      });
    }
    
    // 更新回合计时器显示
    this.updateTimerDisplay();
  }
  
  /**
   * 更新计时器显示
   */
  updateTimerDisplay() {
    if (this.turnTimer && this.turnTimeLeft > 0) {
      // 更新倒计时显示
      if (this.turnTimeLeft <= 5) {
        // 最后5秒警告
        if (window.audioManager && this.turnTimeLeft === 5) {
          window.audioManager.playSound('warning');
        }
      }
    }
  }

  /**
   * 渲染场景
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    // 清空画布
    ctx.clearRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 渲染棋盘
    if (this.board) {
      this.board.render(ctx);
    }
    
    // 渲染所有棋子
    if (this.pieces) {
      Object.values(this.pieces).forEach(colorPieces => {
        colorPieces.forEach(piece => {
          piece.render(ctx);
        });
      });
    }
    
    // 渲染骰子
    if (this.dice) {
      this.dice.render(ctx);
    }
    
    // 渲染UI元素
    this.renderUI(ctx);
    
    // 渲染玩家信息面板
    this.renderPlayerPanels(ctx);
    
    // 渲染状态信息
    this.renderStatusInfo(ctx);
    
    // 渲染按钮
    super.render(ctx);
  }
  
  /**
   * 渲染UI元素
   */
  renderUI(ctx) {
    // 渲染状态文本
    if (this.statusText) {
      ctx.fillStyle = '#333333';
      ctx.font = '24px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(
        this.statusText,
        SCREEN_WIDTH / 2,
        100
      );
    }
    
    // 渲染回合计时器
    if (this.turnTimer && this.turnTimeLeft > 0) {
      const timerColor = this.turnTimeLeft <= 5 ? '#FF4444' : '#333333';
      ctx.fillStyle = timerColor;
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        `剩余时间: ${this.turnTimeLeft}s`,
        SCREEN_WIDTH / 2,
        130
      );
    }
  }
  
  /**
   * 渲染玩家信息面板
   */
  renderPlayerPanels(ctx) {
    if (!this.playerPanels) return;
    
    this.playerPanels.forEach((panel, index) => {
      if (index >= (GameGlobal.databus.players?.length || 0)) return;
      
      const player = GameGlobal.databus.players[index];
      if (!player) return;
      
      // 绘制面板背景
      ctx.fillStyle = panel.isActive ? '#FFD700' : '#F0F0F0';
      ctx.fillRect(panel.x, panel.y, panel.width, panel.height);
      
      // 绘制边框
      ctx.strokeStyle = panel.isActive ? '#FF6B6B' : '#CCCCCC';
      ctx.lineWidth = panel.isActive ? 3 : 1;
      ctx.strokeRect(panel.x, panel.y, panel.width, panel.height);
      
      // 绘制玩家信息
      ctx.fillStyle = '#333333';
      ctx.font = '16px Arial';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';
      
      // 玩家昵称
      ctx.fillText(
        player.nickname || `玩家${index + 1}`,
        panel.x + 10,
        panel.y + 10
      );
      
      // 玩家颜色
      ctx.fillStyle = this.getPlayerColorHex(player.color);
      ctx.fillRect(panel.x + 10, panel.y + 30, 20, 20);
      
      // 完成的棋子数量
      const finishedCount = player.pieces ? 
        player.pieces.filter(p => p.status === PIECE_STATUS.FINISHED).length : 0;
      
      ctx.fillStyle = '#333333';
      ctx.fillText(
        `完成: ${finishedCount}/4`,
        panel.x + 40,
        panel.y + 35
      );
      
      // AI标识
      if (player.isAI) {
        ctx.fillStyle = '#666666';
        ctx.font = '12px Arial';
        ctx.fillText('AI', panel.x + panel.width - 25, panel.y + 10);
      }
    });
  }
  
  /**
   * 获取玩家颜色十六进制值
   */
  getPlayerColorHex(color) {
    const colorMap = {
      'red': '#FF4444',
      'yellow': '#FFDD44',
      'blue': '#4444FF',
      'green': '#44FF44'
    };
    
    return colorMap[color] || '#CCCCCC';
  }
  
  /**
   * 渲染状态信息
   */
  renderStatusInfo(ctx) {
    // 渲染当前骰子值
    if (this.diceValue > 0) {
      ctx.fillStyle = '#333333';
      ctx.font = '18px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        `骰子点数: ${this.diceValue}`,
        SCREEN_WIDTH / 2,
        SCREEN_HEIGHT - 200
      );
    }
    
    // 渲染连续投掷6的次数
    if (this.consecutiveSix > 0) {
      ctx.fillStyle = '#FF6B6B';
      ctx.font = '16px Arial';
      ctx.fillText(
        `连续6点: ${this.consecutiveSix}/3`,
        SCREEN_WIDTH / 2,
        SCREEN_HEIGHT - 180
      );
    }
    
    // 渲染等待移动提示
    if (this.isWaitingForMove) {
      ctx.fillStyle = '#4CAF50';
      ctx.font = '16px Arial';
      ctx.fillText(
        '点击高亮的棋子进行移动',
        SCREEN_WIDTH / 2,
        SCREEN_HEIGHT - 160
      );
    }
  }
  
  /**
   * 渲染游戏状态文本
   * @param {Object} ctx - Canvas上下文
   */
  renderStatusText(ctx) {
    ctx.fillStyle = '#000000';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      this.statusText,
      SCREEN_WIDTH / 2,
      40
    );
  }
}