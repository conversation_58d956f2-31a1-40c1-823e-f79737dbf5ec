<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElCard, ElTable, ElTableColumn, ElTag, ElProgress, ElButton, ElSelect, ElOption, ElIcon, ElAlert, ElEmpty } from 'element-plus'
import { Refresh, View, Platform, Warning, Check, Close } from '@element-plus/icons-vue'
import { monitorAPI, type MonitorStatus, type ServerStatus } from '@/api/monitor'
import { serverAPI } from '@/api/server'

const loading = ref(false)
const serverStatusLoading = ref(false)
const monitorStatus = ref<MonitorStatus | null>(null)
const serverStatusList = ref<ServerStatus[]>([])
const serverList = ref<string[]>([])
const selectedServer = ref('')

const fetchMonitorStatus = async () => {
  try {
    loading.value = true
    const response = await monitorAPI.getMonitorStatus()
    monitorStatus.value = response
  } catch (error) {
    console.error('获取监控状态失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchServerList = async () => {
  try {
    const response = await serverAPI.getIPList()
    serverList.value = response
  } catch (error) {
    console.error('获取服务器列表失败:', error)
  }
}

const fetchServerStatus = async (serverIP: string) => {
  try {
    serverStatusLoading.value = true
    const response = await monitorAPI.getServerStatus(serverIP)
    const index = serverStatusList.value.findIndex(s => s.server_ip === serverIP)
    if (index >= 0) {
      serverStatusList.value[index] = response
    } else {
      serverStatusList.value.push(response)
    }
  } catch (error) {
    console.error(`获取服务器 ${serverIP} 状态失败:`, error)
  } finally {
    serverStatusLoading.value = false
  }
}

const handleViewServerStatus = async (serverIP: string) => {
  if (!serverStatusList.value.find(s => s.server_ip === serverIP)) {
    await fetchServerStatus(serverIP)
  }
}

const handleRefresh = () => {
  fetchMonitorStatus()
}

const handleServerChange = (serverIP: string) => {
  if (serverIP) {
    fetchServerStatus(serverIP)
  }
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': 'success',
    'offline': 'danger',
    'warning': 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'online': '在线',
    'offline': '离线',
    'warning': '警告'
  }
  return statusMap[status] || status
}

const getItemStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'healthy': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

const getItemStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'healthy': '健康',
    'warning': '警告',
    'error': '错误'
  }
  return statusMap[status] || status
}

const getHealthColor = (score: number) => {
  if (score >= 80) return '#10b981'
  if (score >= 60) return '#f59e0b'
  return '#ef4444'
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchMonitorStatus()
  fetchServerList()
  // Auto refresh every 30 seconds
  setInterval(fetchMonitorStatus, 30000)
})
</script>

<template>
  <div class="monitor-status">
    <div class="page-header">
      <div class="header-content">
        <h2>监控状态</h2>
        <p>实时查看服务器和监控项的运行状态</p>
      </div>
      <ElButton type="primary" :icon="Refresh" @click="handleRefresh" :loading="loading">
        刷新状态
      </ElButton>
    </div>

    <!-- Global Status Overview -->
    <ElCard v-if="monitorStatus" class="overview-card">
      <template #header>
        <div class="card-header">
          <span>系统概览</span>
          <ElIcon><Platform /></ElIcon>
        </div>
      </template>
      
      <div class="overview-grid">
        <div class="overview-item">
          <div class="overview-label">服务器总数</div>
          <div class="overview-value">{{ monitorStatus.global_stats.total_servers }}</div>
        </div>
        <div class="overview-item success">
          <div class="overview-label">在线服务器</div>
          <div class="overview-value">{{ monitorStatus.global_stats.online_servers }}</div>
        </div>
        <div class="overview-item danger">
          <div class="overview-label">离线服务器</div>
          <div class="overview-value">{{ monitorStatus.global_stats.offline_servers }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">监控项总数</div>
          <div class="overview-value">{{ monitorStatus.global_stats.total_monitor_items }}</div>
        </div>
        <div class="overview-item success">
          <div class="overview-label">健康监控项</div>
          <div class="overview-value">{{ monitorStatus.global_stats.healthy_items }}</div>
        </div>
        <div class="overview-item warning">
          <div class="overview-label">警告监控项</div>
          <div class="overview-value">{{ monitorStatus.global_stats.warning_items }}</div>
        </div>
        <div class="overview-item danger">
          <div class="overview-label">错误监控项</div>
          <div class="overview-value">{{ monitorStatus.global_stats.error_items }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-label">总体成功率</div>
          <div class="overview-value">{{ monitorStatus.global_stats.overall_success_rate.toFixed(1) }}%</div>
        </div>
      </div>
    </ElCard>

    <!-- Problematic Servers -->
    <ElCard v-if="monitorStatus?.problematic_servers_count" class="problems-card">
      <template #header>
        <div class="card-header">
          <span>问题服务器 ({{ monitorStatus.problematic_servers_count }})</span>
          <ElIcon class="warning-icon"><Warning /></ElIcon>
        </div>
      </template>
      
      <ElTable :data="monitorStatus.problematic_servers" stripe>
        <ElTableColumn prop="server_ip" label="服务器IP" width="140">
          <template #default="{ row }">
            <span class="server-ip">{{ row.server_ip }}</span>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="状态" width="100">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row.server_status)" size="small">
              {{ getStatusText(row.server_status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="健康分数" width="150">
          <template #default="{ row }">
            <div class="health-score">
              <ElProgress 
                :percentage="row.health_score"
                :color="getHealthColor(row.health_score)"
                :show-text="false"
                :stroke-width="8"
              />
              <span class="score-text">{{ row.health_score.toFixed(1) }}</span>
            </div>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="错误项/总数" width="120">
          <template #default="{ row }">
            <span :class="{ 'error-count': row.error_items > 0 }">
              {{ row.error_items }}/{{ row.total_items }}
            </span>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="操作" width="120">
          <template #default="{ row }">
            <ElButton 
              size="small" 
              type="primary" 
              :icon="View" 
              @click="handleViewServerStatus(row.server_ip)"
            >
              查看详情
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>

    <!-- Server Status Detail -->
    <ElCard class="server-detail-card">
      <template #header>
        <div class="card-header">
          <span>服务器详细状态</span>
          <ElSelect 
            v-model="selectedServer" 
            placeholder="选择服务器查看详情"
            @change="handleServerChange"
            style="width: 200px"
          >
            <ElOption
              v-for="ip in serverList"
              :key="ip"
              :label="ip"
              :value="ip"
            />
          </ElSelect>
        </div>
      </template>
      
      <div v-if="!serverStatusList.length" class="empty-state">
        <ElEmpty description="请选择服务器查看详细状态" />
      </div>
      
      <div v-else class="server-status-list">
        <div 
          v-for="serverStatus in serverStatusList" 
          :key="serverStatus.server_ip"
          class="server-status-item"
        >
          <div class="server-status-header">
            <div class="server-info">
              <h3>{{ serverStatus.server_ip }}</h3>
              <div class="server-meta">
                <ElTag :type="getStatusType(serverStatus.server_status)" size="small">
                  {{ getStatusText(serverStatus.server_status) }}
                </ElTag>
                <span class="last-check">最后检查: {{ formatTime(serverStatus.last_check_time) }}</span>
              </div>
            </div>
            <div class="server-score">
              <div class="score-label">健康分数</div>
              <div class="score-circle">
                <ElProgress 
                  type="circle" 
                  :percentage="serverStatus.overall_health_score"
                  :color="getHealthColor(serverStatus.overall_health_score)"
                  :width="80"
                />
              </div>
            </div>
          </div>
          
          <div class="server-stats">
            <div class="stat-item">
              <div class="stat-label">监控项总数</div>
              <div class="stat-value">{{ serverStatus.total_monitor_items }}</div>
            </div>
            <div class="stat-item success">
              <div class="stat-label">健康项</div>
              <div class="stat-value">{{ serverStatus.healthy_items }}</div>
            </div>
            <div class="stat-item warning">
              <div class="stat-label">警告项</div>
              <div class="stat-value">{{ serverStatus.warning_items }}</div>
            </div>
            <div class="stat-item danger">
              <div class="stat-label">错误项</div>
              <div class="stat-value">{{ serverStatus.error_items }}</div>
            </div>
          </div>
          
          <div v-if="serverStatus.problematic_items.length" class="problematic-items">
            <h4>问题监控项</h4>
            <ElTable :data="serverStatus.problematic_items" size="small">
              <ElTableColumn prop="monitor_item_name" label="监控项名称" />
              
              <ElTableColumn label="状态" width="80">
                <template #default="{ row }">
                  <ElTag :type="getItemStatusType(row.status)" size="small">
                    {{ getItemStatusText(row.status) }}
                  </ElTag>
                </template>
              </ElTableColumn>
              
              <ElTableColumn prop="consecutive_failures" label="连续失败" width="90" />
              
              <ElTableColumn label="成功率" width="100">
                <template #default="{ row }">
                  <span>{{ row.success_rate.toFixed(1) }}%</span>
                </template>
              </ElTableColumn>
              
              <ElTableColumn prop="last_error" label="最后错误" show-overflow-tooltip>
                <template #default="{ row }">
                  <span v-if="row.last_error" class="error-text">{{ row.last_error }}</span>
                  <span v-else>-</span>
                </template>
              </ElTableColumn>
              
              <ElTableColumn label="最后检查" width="140">
                <template #default="{ row }">
                  <span class="time-text">{{ formatTime(row.last_check_time) }}</span>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.monitor-status {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.overview-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #1f2937;
}

.warning-icon {
  color: #f59e0b;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.overview-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-top: 3px solid #e5e7eb;
}

.overview-item.success {
  border-top-color: #10b981;
}

.overview-item.warning {
  border-top-color: #f59e0b;
}

.overview-item.danger {
  border-top-color: #ef4444;
}

.overview-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.problems-card {
  margin-bottom: 24px;
}

.server-ip {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 500;
  color: #3b82f6;
}

.health-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.error-count {
  color: #ef4444;
  font-weight: 500;
}

.server-detail-card {
  margin-bottom: 24px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.server-status-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.server-status-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: white;
}

.server-status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.server-info h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
}

.server-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-check {
  font-size: 12px;
  color: #6b7280;
}

.server-score {
  text-align: center;
}

.score-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.server-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border-top: 2px solid #e5e7eb;
}

.stat-item.success {
  border-top-color: #10b981;
}

.stat-item.warning {
  border-top-color: #f59e0b;
}

.stat-item.danger {
  border-top-color: #ef4444;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.problematic-items {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.problematic-items h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
}

.error-text {
  color: #ef4444;
  font-size: 12px;
}

.time-text {
  font-size: 12px;
  color: #6b7280;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-content h2 {
    font-size: 24px;
  }
  
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .server-status-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .server-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>