<template>
  <div class="home">
    <div class="header">
      <div class="title">监控系统</div>
      <div class="admin-btn-container">
        <router-link to="/admin" class="admin-btn">进入后台</router-link>
      </div>
    </div>
    <monitoring-charts />
    <div class="admin-link-container">
      <el-button type="primary" @click="goToAdmin">进入管理后台</el-button>
    </div>
  </div>
</template>

<script>
import MonitoringCharts from '../components/monitoring/MonitoringCharts.vue'

export default {
  name: 'HomePage',
  components: {
    MonitoringCharts
  },
  methods: {
    goToAdmin () {
      this.$router.push('/admin')
    }
  }
}
</script>

<style scoped lang="less">
.home {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 40px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.admin-btn-container {
  text-align: right;
}

.admin-btn {
  display: inline-block;
  padding: 8px 16px;
  background-color: #409EFF;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.3s;
}

.admin-btn:hover {
  background-color: #66b1ff;
}

.admin-link-container {
  margin-top: 20px;
  text-align: center;
}
</style>
