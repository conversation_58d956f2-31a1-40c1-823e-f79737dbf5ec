# Ubuntu 虚拟机自动化创建脚本

这个脚本可以在没有图形界面的服务器上自动创建Ubuntu虚拟机，无需人工干预。

## 工作原理

与标准ISO安装不同，此脚本采用**云镜像直接导入**的方式，具有以下优势：
- 完全无需交互，一次执行即可完成
- 适合无图形界面的服务器环境
- 启动速度更快，过程更可靠

脚本执行以下步骤：
1. 下载Ubuntu云镜像（如果本地不存在）
2. 创建并调整虚拟机磁盘大小
3. 生成cloud-init配置文件（user-data、meta-data、network-config）
4. 创建包含cloud-init配置的ISO镜像
5. 使用virt-install导入云镜像并附加配置ISO

## 使用方法

### 前提条件

请确保系统已安装以下软件包：
```bash
sudo apt install -y libvirt-daemon-system virtinst qemu-kvm cloud-image-utils genisoimage curl
```

### 运行脚本

1. 为脚本添加执行权限：
```bash
chmod +x auto_create_vm.sh
```

2. 以root权限运行脚本：
```bash
sudo ./auto_create_vm.sh
```

### 连接到虚拟机

脚本执行完成后，可通过以下方式连接到虚拟机：

1. 通过控制台：
```bash
sudo virsh console ubuntu-vm
```

2. 通过SSH（需要知道虚拟机IP地址）：
```bash
# 查看虚拟机IP地址
sudo virsh domifaddr ubuntu-vm

# SSH连接
ssh ubuntu@<虚拟机IP地址>
```

虚拟机默认账户信息：
- 用户名：ubuntu
- 密码：ubuntu123

## 自定义

您可以通过修改脚本开头的变量来自定义虚拟机配置：
- VM_NAME：虚拟机名称
- DISK_PATH：虚拟机磁盘路径
- CLOUD_IMAGE_PATH：云镜像保存路径
- CLOUD_IMAGE_URL：云镜像下载地址

要修改虚拟机的硬件配置，请编辑virt-install命令中的相应参数：
- --ram：内存大小（MB）
- --vcpus：CPU核心数
- 磁盘大小调整：修改qemu-img resize命令中的大小值

## 故障排除

如果遇到问题，请尝试：
1. 检查虚拟化服务是否正常运行：`sudo systemctl status libvirtd`
2. 验证KVM模块是否加载：`lsmod | grep kvm`
3. 查看虚拟机状态：`sudo virsh list --all`
4. 检查cloud-init日志：`sudo virsh console <虚拟机名称>` 登录后查看 `/var/log/cloud-init.log` 