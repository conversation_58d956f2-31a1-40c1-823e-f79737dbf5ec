/* 深色主题样式 */

// 主要颜色变量
@dark-bg: #1a1a1a;
@dark-bg-light: #2c2c2c;
@dark-text: #e0e0e0;
@dark-text-secondary: #aaaaaa;
@dark-border: #444444;
@dark-primary: #409EFF;
@dark-success: #67C23A;
@dark-warning: #E6A23C;
@dark-danger: #F56C6C;
@dark-info: #909399;

// 全局样式
body.dark-theme {
  background-color: @dark-bg;
  color: @dark-text;

  // App 容器
  #app {
    background-color: @dark-bg;
    color: @dark-text;
  }

  // 卡片样式
  .el-card {
    background-color: @dark-bg-light;
    border-color: @dark-border;
    color: @dark-text;

    .el-card__header {
      border-bottom-color: @dark-border;
    }
  }

  // 表格样式
  .el-table {
    background-color: @dark-bg-light;
    color: @dark-text;

    th, td {
      background-color: @dark-bg-light;
      border-bottom-color: @dark-border;
    }

    th {
      background-color: @dark-bg;
    }

    tr:hover > td {
      background-color: #3a3a3a;
    }

    .el-table__header-wrapper {
      th {
        background-color: @dark-bg;
        color: @dark-text;
        border-bottom-color: @dark-border;
      }
    }
  }

  // 表单样式
  .el-form-item__label {
    color: @dark-text;
  }

  .el-input__inner,
  .el-textarea__inner {
    background-color: @dark-bg;
    border-color: @dark-border;
    color: @dark-text;

    &:focus {
      border-color: @dark-primary;
    }
  }

  // 按钮样式
  .el-button {
    &.el-button--default {
      background-color: @dark-bg-light;
      border-color: @dark-border;
      color: @dark-text;

      &:hover, &:focus {
        background-color: #3a3a3a;
        border-color: @dark-primary;
        color: @dark-text;
      }
    }
  }

  // 下拉菜单
  .el-dropdown-menu {
    background-color: @dark-bg-light;
    border-color: @dark-border;

    .el-dropdown-menu__item {
      color: @dark-text;

      &:hover {
        background-color: #3a3a3a;
      }
    }
  }

  // 对话框
  .el-dialog {
    background-color: @dark-bg-light;
    
    .el-dialog__title {
      color: @dark-text;
    }
    
    .el-dialog__body {
      color: @dark-text;
    }
  }

  // 分页
  .el-pagination {
    color: @dark-text;
    
    button {
      background-color: @dark-bg-light;
      color: @dark-text;
    }
    
    .el-pagination__total,
    .el-pagination__jump {
      color: @dark-text;
    }
  }

  // 主页样式
  .home {
    background-color: @dark-bg;
  }

  // 监控图表区域
  .monitoring-charts {
    background-color: @dark-bg-light;
    border-color: @dark-border;
  }

  // 图表容器
  .chart-container {
    background-color: @dark-bg-light;
    border-color: @dark-border;
  }
} 