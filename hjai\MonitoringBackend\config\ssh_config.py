"""
SSH配置管理模块
用于管理SSH密钥文件路径和相关配置
"""
import os
from pathlib import Path
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)


class SSHConfig:
    """SSH配置管理类"""
    
    # 默认SSH密钥文件路径
    DEFAULT_PRIVATE_KEY_PATHS = [
        "~/.ssh/id_rsa",
        "~/.ssh/id_ed25519", 
        "~/.ssh/id_ecdsa",
        "~/.ssh/id_dsa"
    ]
    
    # SSH连接超时时间（秒）
    DEFAULT_TIMEOUT = 15
    
    # SSH连接重试次数
    DEFAULT_MAX_RETRIES = 2
    
    @classmethod
    def get_private_key_paths(cls) -> List[str]:
        """
        获取SSH私钥文件路径列表
        
        Returns:
            List[str]: 私钥文件路径列表
        """
        paths = []
        for path_str in cls.DEFAULT_PRIVATE_KEY_PATHS:
            expanded_path = os.path.expanduser(path_str)
            if os.path.exists(expanded_path) and os.path.isfile(expanded_path):
                paths.append(expanded_path)
                logger.debug(f"找到SSH私钥文件: {expanded_path}")
        
        if not paths:
            logger.warning("未找到任何SSH私钥文件")
        
        return paths
    
    @classmethod
    def get_default_private_key_path(cls) -> Optional[str]:
        """
        获取默认的SSH私钥文件路径

        Returns:
            Optional[str]: 默认私钥文件路径，如果不存在则返回None
        """
        paths = cls.get_private_key_paths()
        return paths[0] if paths else None
    
    @classmethod
    def validate_private_key_path(cls, key_path: str) -> bool:
        """
        验证SSH私钥文件路径是否有效
        
        Args:
            key_path: 私钥文件路径
            
        Returns:
            bool: 路径是否有效
        """
        if not key_path:
            return False
            
        expanded_path = os.path.expanduser(key_path)
        
        if not os.path.exists(expanded_path):
            logger.warning(f"SSH私钥文件不存在: {expanded_path}")
            return False
            
        if not os.path.isfile(expanded_path):
            logger.warning(f"SSH私钥路径不是文件: {expanded_path}")
            return False
            
        # 检查文件权限（私钥文件应该只有所有者可读）
        try:
            stat_info = os.stat(expanded_path)
            # 在Unix系统上检查权限
            if hasattr(stat_info, 'st_mode'):
                mode = stat_info.st_mode & 0o777
                if mode & 0o077:  # 检查是否其他用户有权限
                    logger.warning(f"SSH私钥文件权限过于宽松: {expanded_path} (权限: {oct(mode)})")
                    # 不阻止使用，只是警告
        except Exception as e:
            logger.debug(f"无法检查私钥文件权限: {e}")
        
        return True
    
    @classmethod
    def get_ssh_config(cls) -> dict:
        """
        获取SSH配置字典
        
        Returns:
            dict: SSH配置信息
        """
        return {
            "timeout": cls.DEFAULT_TIMEOUT,
            "max_retries": cls.DEFAULT_MAX_RETRIES,
            "private_key_paths": cls.get_private_key_paths(),
            "default_private_key_path": cls.get_default_private_key_path()
        }


# 创建全局配置实例
ssh_config = SSHConfig()
