#!/bin/bash
# Ubuntu 22.04 DHCP服务器自动化安装与配置脚本
# 请以root权限运行

set -e

# 1. 安装DHCP服务端
apt update
apt install -y isc-dhcp-server

# 2. 配置DHCP参数
cat > /etc/dhcp/dhcpd.conf <<EOF
default-lease-time 600;
max-lease-time 7200;
subnet 192.168.10.0 netmask 255.255.255.0 {
  range 192.168.10.100 192.168.10.200;
  option routers 192.168.10.1;
  option domain-name-servers 8.8.8.8, 8.8.4.4;
}
EOF

# 3. 设置监听网卡（假设为ens33，请根据实际情况修改）
INTERFACE=ens33
sed -i "s/^INTERFACESv4=.*/INTERFACESv4=\"$INTERFACE\"/" /etc/default/isc-dhcp-server

# 4. 启动服务并设置开机自启
systemctl restart isc-dhcp-server
systemctl enable isc-dhcp-server

echo "DHCP服务器已安装并配置完成。请确保网卡$INTERFACE已连接到目标网络。"