"""
SSH连接工具模块
支持密钥认证和密码认证的SSH连接工具
"""
import os
import paramiko
from typing import Dict, Any, Optional, Tuple
import logging
from config.ssh_config import ssh_config

logger = logging.getLogger(__name__)


class SSHConnectionManager:
    """SSH连接管理器，支持密钥认证和密码认证"""
    
    @staticmethod
    def create_ssh_client() -> paramiko.SSHClient:
        """
        创建SSH客户端实例
        
        Returns:
            paramiko.SSHClient: SSH客户端实例
        """
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        return ssh_client
    
    @staticmethod
    def connect_with_password(ssh_client: paramiko.SSHClient, server: Dict[str, Any], timeout: int = 15) -> bool:
        """
        使用密码认证连接SSH
        
        Args:
            ssh_client: SSH客户端实例
            server: 服务器信息字典
            timeout: 连接超时时间
            
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.debug(f"尝试使用密码认证连接到 {server['ip']}")
            ssh_client.connect(
                hostname=server["ip"],
                username=server["username"],
                password=server["password"],
                timeout=timeout,
                allow_agent=False,  # 禁用SSH代理
                look_for_keys=False  # 禁用自动查找密钥
            )
            logger.debug(f"密码认证连接成功: {server['ip']}")
            return True
        except paramiko.AuthenticationException as e:
            logger.warning(f"密码认证失败 {server['ip']}: {str(e)}")
            return False
        except Exception as e:
            logger.warning(f"密码连接失败 {server['ip']}: {str(e)}")
            return False
    
    @staticmethod
    def connect_with_key(ssh_client: paramiko.SSHClient, server: Dict[str, Any], 
                        key_path: Optional[str] = None, timeout: int = 15) -> bool:
        """
        使用SSH密钥认证连接
        
        Args:
            ssh_client: SSH客户端实例
            server: 服务器信息字典
            key_path: 私钥文件路径，为None时使用默认路径
            timeout: 连接超时时间
            
        Returns:
            bool: 连接是否成功
        """
        # 确定要使用的密钥路径
        key_paths_to_try = []
        
        if key_path:
            # 如果指定了密钥路径，优先使用
            expanded_path = os.path.expanduser(key_path)
            if ssh_config.validate_private_key_path(expanded_path):
                key_paths_to_try.append(expanded_path)
        
        # 添加默认密钥路径
        key_paths_to_try.extend(ssh_config.get_private_key_paths())
        
        if not key_paths_to_try:
            logger.warning(f"未找到可用的SSH私钥文件用于连接 {server['ip']}")
            return False
        
        # 尝试每个密钥文件
        for key_file_path in key_paths_to_try:
            try:
                logger.debug(f"尝试使用SSH密钥 {key_file_path} 连接到 {server['ip']}")
                
                # 尝试加载私钥
                private_key = None
                try:
                    # 尝试不同类型的私钥
                    if 'rsa' in key_file_path.lower():
                        private_key = paramiko.RSAKey.from_private_key_file(key_file_path)
                    elif 'ed25519' in key_file_path.lower():
                        private_key = paramiko.Ed25519Key.from_private_key_file(key_file_path)
                    elif 'ecdsa' in key_file_path.lower():
                        private_key = paramiko.ECDSAKey.from_private_key_file(key_file_path)
                    elif 'dsa' in key_file_path.lower():
                        private_key = paramiko.DSSKey.from_private_key_file(key_file_path)
                    else:
                        # 自动检测密钥类型
                        try:
                            private_key = paramiko.RSAKey.from_private_key_file(key_file_path)
                        except:
                            try:
                                private_key = paramiko.Ed25519Key.from_private_key_file(key_file_path)
                            except:
                                try:
                                    private_key = paramiko.ECDSAKey.from_private_key_file(key_file_path)
                                except:
                                    private_key = paramiko.DSSKey.from_private_key_file(key_file_path)
                
                except Exception as key_error:
                    logger.debug(f"无法加载私钥文件 {key_file_path}: {str(key_error)}")
                    continue
                
                # 使用私钥连接
                ssh_client.connect(
                    hostname=server["ip"],
                    username=server["username"],
                    pkey=private_key,
                    timeout=timeout,
                    allow_agent=False,
                    look_for_keys=False
                )
                
                logger.debug(f"SSH密钥认证连接成功: {server['ip']} (使用密钥: {key_file_path})")
                return True
                
            except paramiko.AuthenticationException as e:
                logger.debug(f"SSH密钥认证失败 {server['ip']} (密钥: {key_file_path}): {str(e)}")
                continue
            except Exception as e:
                logger.debug(f"SSH密钥连接失败 {server['ip']} (密钥: {key_file_path}): {str(e)}")
                continue
        
        logger.warning(f"所有SSH密钥认证尝试都失败: {server['ip']}")
        return False
    
    @classmethod
    def connect_to_server(cls, server: Dict[str, Any], timeout: int = 15) -> Tuple[Optional[paramiko.SSHClient], bool]:
        """
        连接到服务器，自动选择认证方式
        
        Args:
            server: 服务器信息字典，包含ip, username, password等字段
            timeout: 连接超时时间
            
        Returns:
            Tuple[Optional[SSHClient], bool]: (SSH客户端实例, 是否连接成功)
        """
        ssh_client = cls.create_ssh_client()
        
        # 判断认证方式
        should_use_key = server.get('use_ssh_key', False) or not server.get('password')
        
        if should_use_key:
            # 优先尝试SSH密钥认证
            logger.debug(f"尝试SSH密钥认证连接到 {server['ip']}")
            if cls.connect_with_key(ssh_client, server, server.get('ssh_key_path'), timeout):
                return ssh_client, True
            
            # 如果密钥认证失败且有密码，尝试密码认证作为备选
            if server.get('password'):
                logger.debug(f"SSH密钥认证失败，尝试密码认证作为备选: {server['ip']}")
                if cls.connect_with_password(ssh_client, server, timeout):
                    return ssh_client, True
        else:
            # 使用密码认证
            logger.debug(f"尝试密码认证连接到 {server['ip']}")
            if cls.connect_with_password(ssh_client, server, timeout):
                return ssh_client, True
        
        # 所有认证方式都失败
        ssh_client.close()
        logger.warning(f"所有认证方式都失败: {server['ip']}")
        return None, False
