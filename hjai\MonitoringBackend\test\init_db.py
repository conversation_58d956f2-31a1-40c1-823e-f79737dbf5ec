import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入配置
from config.db import TORTOISE_ORM
from tortoise import Tortoise

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

async def initialize_db():
    """初始化数据库表结构"""
    logger.info("开始初始化数据库表结构...")
    
    # 初始化ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 创建数据库表结构（如果不存在）
    logger.info("正在创建表结构...")
    await Tortoise.generate_schemas(safe=True)
    
    logger.info("数据库表结构初始化完成")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(initialize_db()) 