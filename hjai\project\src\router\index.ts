import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/Layout.vue'
import Dashboard from '@/views/Dashboard.vue'
import ServerManagement from '@/views/ServerManagement.vue'
import MonitorItems from '@/views/MonitorItems.vue'
import MonitorTesting from '@/views/MonitorTesting.vue'
import MonitorStatus from '@/views/MonitorStatus.vue'
import BatchOperations from '@/views/BatchOperations.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: Layout,
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: Dashboard
        },
        {
          path: '/servers',
          name: 'ServerManagement',
          component: ServerManagement
        },
        {
          path: '/monitor-items',
          name: 'MonitorItems',
          component: MonitorItems
        },
        {
          path: '/testing',
          name: 'MonitorTesting',
          component: MonitorTesting
        },
        {
          path: '/status',
          name: 'MonitorStatus',
          component: MonitorStatus
        },
        {
          path: '/batch',
          name: 'BatchOperations',
          component: BatchOperations
        }
      ]
    }
  ]
})

export default router