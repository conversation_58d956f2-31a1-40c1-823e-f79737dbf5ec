"""
测试时区警告修复

验证修复后不再出现Tortoise ORM的时区警告
"""

import os
import sys
import asyncio
import warnings
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 捕获警告
warnings.filterwarnings("error", category=RuntimeWarning, message=".*received a naive datetime.*")

async def test_model_save_without_warnings():
    """测试模型保存不产生时区警告"""
    from config.db import TORTOISE_ORM
    from tortoise import Tortoise
    from config.timezone_utils import TZ
    
    # 使用内存数据库进行测试
    test_config = {
        "connections": {
            "default": {
                "engine": "tortoise.backends.sqlite",
                "credentials": {"file_path": ":memory:"}
            }
        },
        "apps": {
            "models": {
                "models": ["models.cpu_stats", "models.memory_stats", "models.custom_monitor"],
                "default_connection": "default",
            }
        },
        "use_tz": True,
        "timezone": "UTC"
    }
    
    try:
        # 初始化ORM
        await Tortoise.init(config=test_config)
        await Tortoise.generate_schemas()
        
        print("✅ 数据库初始化成功")
        
        # 测试CPU统计模型
        from models.cpu_stats import CPUStats
        
        cpu_stats = await CPUStats.create(
            ip="*************",
            model="Intel Core i7",
            cores=8,
            frequency=3.2,
            usage_percent=75.5,
            user_percent=45.2,
            system_percent=30.3,
            iowait_percent=0.0,
            load_1min=1.5,
            load_5min=1.2,
            load_15min=1.0,
            process_count=150
        )
        
        print("✅ CPU统计模型保存成功，无时区警告")
        print(f"   时间戳: {cpu_stats.timestamp}")
        print(f"   时间戳类型: {type(cpu_stats.timestamp)}")
        print(f"   时区信息: {cpu_stats.timestamp.tzinfo}")
        
        # 测试内存统计模型
        from models.memory_stats import MemoryStats
        
        memory_stats = await MemoryStats.create(
            ip="*************",
            total=16777216,
            used=8388608,
            free=8388608,
            available=8388608,
            usage_percent=50.0,
            swap_total=4194304,
            swap_used=0
        )
        
        print("✅ 内存统计模型保存成功，无时区警告")
        print(f"   时间戳: {memory_stats.timestamp}")
        print(f"   时间戳类型: {type(memory_stats.timestamp)}")
        print(f"   时区信息: {memory_stats.timestamp.tzinfo}")
        
        # 测试自定义监控模型
        from models.custom_monitor import MonitorItem, MonitorData
        
        monitor_item = await MonitorItem.create(
            name="测试监控项",
            command="echo 'test'",
            description="测试用监控项",
            data_type="string"
        )
        
        monitor_data = await MonitorData.create(
            monitor_item_id=monitor_item.id,
            ip="*************",
            value="test_value",
            status=0
        )
        
        print("✅ 自定义监控模型保存成功，无时区警告")
        print(f"   时间戳: {monitor_data.timestamp}")
        print(f"   时间戳类型: {type(monitor_data.timestamp)}")
        print(f"   时区信息: {monitor_data.timestamp.tzinfo}")
        
        # 测试显示格式
        display_format = cpu_stats.get_timestamp_display()
        print(f"✅ 显示格式: {display_format}")
        
        # 验证时区转换
        from config.timezone_utils import TZ
        shanghai_time = TZ.to_shanghai(cpu_stats.timestamp)
        print(f"✅ 上海时区转换: {shanghai_time}")
        
        return True
        
    except RuntimeWarning as e:
        if "received a naive datetime" in str(e):
            print(f"❌ 仍然存在时区警告: {e}")
            return False
        else:
            raise
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理
        try:
            await Tortoise.close_connections()
        except:
            pass

def test_timezone_utils():
    """测试时区工具类"""
    try:
        from config.timezone_utils import TZ
        
        # 测试基本功能
        utc_now = TZ.now_utc()
        shanghai_now = TZ.now_shanghai()
        
        print(f"✅ UTC时间: {utc_now}")
        print(f"✅ 上海时间: {shanghai_now}")
        print(f"✅ UTC时区信息: {utc_now.tzinfo}")
        print(f"✅ 上海时区信息: {shanghai_now.tzinfo}")
        
        # 测试显示格式
        display_format = TZ.to_display_format(utc_now)
        print(f"✅ 显示格式: {display_format}")
        
        # 测试JSON编码
        from config.timezone_utils import ModernJSONEncoder
        import json
        
        test_data = {"time": utc_now, "value": 123}
        json_str = json.dumps(test_data, cls=ModernJSONEncoder)
        print(f"✅ JSON编码: {json_str}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时区工具测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔧 开始测试时区警告修复...")
    print("=" * 50)
    
    # 测试时区工具类
    print("\n📋 测试时区工具类...")
    utils_ok = test_timezone_utils()
    
    # 测试模型保存
    print("\n📋 测试模型保存（无警告）...")
    model_ok = await test_model_save_without_warnings()
    
    print("\n" + "=" * 50)
    
    if utils_ok and model_ok:
        print("🎉 所有测试通过！时区警告已修复。")
        print("\n📝 修复总结:")
        print("1. ✅ 所有模型现在使用带时区信息的UTC时间")
        print("2. ✅ 消除了Tortoise ORM的时区警告")
        print("3. ✅ 保持了时区转换功能的正确性")
        print("4. ✅ API响应格式仍然正确显示上海时区")
        return True
    else:
        print("❌ 部分测试失败，请检查修复。")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
