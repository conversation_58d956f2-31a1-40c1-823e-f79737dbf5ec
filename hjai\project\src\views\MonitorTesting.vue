<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElMessage, ElAlert, ElInputNumber, ElSwitch, ElDivider, ElTag, ElCollapse, ElCollapseItem } from 'element-plus'
import { monitorAPI, type TestCommandRequest, type TestCommandResponse, type ValidateMonitorRequest, type ValidateMonitorResponse } from '@/api/monitor'
import { serverAPI } from '@/api/server'

const loading = ref(false)
const validating = ref(false)
const serverList = ref<string[]>([])
const activeNames = ref(['test'])

// Test form
const testForm = ref<TestCommandRequest>({
  command: '',
  ip: '',
  username: '',
  password: '',
  use_ssh_key: false,
  ssh_key_path: '',
  timeout: 30,
  data_type: 'string'
})

// Validation form
const validateForm = ref<ValidateMonitorRequest>({
  name: '',
  command: '',
  data_type: 'string',
  timeout: 30,
  category: ''
})

// Results
const testResult = ref<TestCommandResponse | null>(null)
const validateResult = ref<ValidateMonitorResponse | null>(null)

const dataTypeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: 'JSON', value: 'json' }
]

const categoryOptions = [
  { label: '系统', value: 'system' },
  { label: '网络', value: 'network' },
  { label: '应用', value: 'application' },
  { label: '数据库', value: 'database' },
  { label: '自定义', value: 'custom' }
]

const commonCommands = [
  {
    name: 'CPU使用率',
    command: "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
    category: 'system',
    dataType: 'number'
  },
  {
    name: '内存使用率',
    command: "free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'",
    category: 'system',
    dataType: 'number'
  },
  {
    name: '磁盘使用率',
    command: "df -h / | awk 'NR==2{print $5}' | sed 's/%//'",
    category: 'system',
    dataType: 'number'
  },
  {
    name: '系统负载',
    command: "uptime | awk '{print $(NF-2)}' | sed 's/,//'",
    category: 'system',
    dataType: 'number'
  },
  {
    name: '进程数量',
    command: "ps aux | wc -l",
    category: 'system',
    dataType: 'number'
  }
]

const formRules = {
  command: [
    { required: true, message: '请输入监控命令', trigger: 'blur' }
  ],
  ip: [
    { required: true, message: '请选择服务器IP', trigger: 'change' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
}

const validateRules = {
  name: [
    { required: true, message: '请输入监控项名称', trigger: 'blur' }
  ],
  command: [
    { required: true, message: '请输入监控命令', trigger: 'blur' }
  ]
}

const fetchServerList = async () => {
  try {
    const response = await serverAPI.getIPList()
    serverList.value = response
  } catch (error) {
    console.error('获取服务器列表失败:', error)
  }
}

const handleTestCommand = async () => {
  try {
    loading.value = true
    testResult.value = null
    const response = await monitorAPI.testCommand(testForm.value)
    testResult.value = response
    if (response.success) {
      ElMessage.success('命令测试成功')
    } else {
      ElMessage.warning('命令执行失败')
    }
  } catch (error) {
    ElMessage.error('测试请求失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleValidateMonitor = async () => {
  try {
    validating.value = true
    validateResult.value = null
    const response = await monitorAPI.validateMonitor(validateForm.value)
    validateResult.value = response
    if (response.is_valid) {
      ElMessage.success('配置验证通过')
    } else {
      ElMessage.warning('配置验证失败')
    }
  } catch (error) {
    ElMessage.error('验证请求失败')
    console.error(error)
  } finally {
    validating.value = false
  }
}

const useCommonCommand = (cmd: any) => {
  testForm.value.command = cmd.command
  validateForm.value.command = cmd.command
  validateForm.value.name = cmd.name
  validateForm.value.category = cmd.category
  validateForm.value.data_type = cmd.dataType
  testForm.value.data_type = cmd.dataType
}

const clearResults = () => {
  testResult.value = null
  validateResult.value = null
}

const getSecurityLevelColor = (level: string) => {
  const colors: Record<string, string> = {
    low: '#10b981',
    normal: '#3b82f6',
    high: '#ef4444'
  }
  return colors[level] || '#6b7280'
}

const getSecurityLevelText = (level: string) => {
  const texts: Record<string, string> = {
    low: '低级',
    normal: '普通',
    high: '高级'
  }
  return texts[level] || level
}

onMounted(() => {
  fetchServerList()
})
</script>

<template>
  <div class="monitor-testing">
    <div class="page-header">
      <div class="header-content">
        <h2>监控测试</h2>
        <p>测试和验证监控命令的正确性和安全性</p>
      </div>
    </div>

    <!-- Common Commands -->
    <ElCard class="commands-card">
      <template #header>
        <div class="card-header">
          <span>常用监控命令</span>
          <el-icon><TestTube /></el-icon>
        </div>
      </template>
      
      <div class="commands-grid">
        <div 
          v-for="cmd in commonCommands" 
          :key="cmd.name"
          class="command-item"
          @click="useCommonCommand(cmd)"
        >
          <div class="command-header">
            <span class="command-name">{{ cmd.name }}</span>
            <ElTag size="small">{{ cmd.category }}</ElTag>
          </div>
          <code class="command-code">{{ cmd.command }}</code>
        </div>
      </div>
    </ElCard>

    <!-- Test and Validation Forms -->
    <ElCard class="testing-card">
      <ElCollapse v-model="activeNames" accordion>
        <ElCollapseItem title="命令测试" name="test">
          <template #title>
            <div class="collapse-title">
              <el-icon><TestTube /></el-icon>
              <span>命令测试</span>
            </div>
          </template>
          
          <ElForm :model="testForm" :rules="formRules" label-width="120px">
            <ElFormItem label="监控命令" prop="command">
              <el-input 
                v-model="testForm.command" 
                type="textarea"
                placeholder="请输入要测试的监控命令"
                :rows="3"
              />
            </ElFormItem>
            
            <ElFormItem label="目标服务器" prop="ip">
              <ElSelect v-model="testForm.ip" placeholder="请选择服务器">
                <ElOption
                  v-for="ip in serverList"
                  :key="ip"
                  :label="ip"
                  :value="ip"
                />
              </ElSelect>
            </ElFormItem>
            
            <ElFormItem label="用户名" prop="username">
              <ElInput v-model="testForm.username" placeholder="请输入用户名" />
            </ElFormItem>
            
            <ElFormItem label="认证方式">
              <ElSwitch
                v-model="testForm.use_ssh_key"
                active-text="SSH密钥"
                inactive-text="密码"
              />
            </ElFormItem>
            
            <ElFormItem v-if="!testForm.use_ssh_key" label="密码">
              <ElInput 
                v-model="testForm.password" 
                type="password" 
                placeholder="请输入密码" 
                show-password 
              />
            </ElFormItem>
            
            <ElFormItem v-if="testForm.use_ssh_key" label="SSH密钥路径">
              <ElInput 
                v-model="testForm.ssh_key_path" 
                placeholder="留空使用默认路径" 
              />
            </ElFormItem>
            
            <ElFormItem label="数据类型">
              <ElSelect v-model="testForm.data_type">
                <ElOption
                  v-for="option in dataTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
            
            <ElFormItem label="超时时间">
              <ElInputNumber 
                v-model="testForm.timeout" 
                :min="1" 
                :max="300" 
                controls-position="right"
              />
              <span style="margin-left: 8px; color: #6b7280;">秒</span>
            </ElFormItem>
            
            <ElFormItem>
              <ElButton 
                type="primary" 
                :loading="loading" 
                @click="handleTestCommand"
              >
                <el-icon><TestTube /></el-icon>
                测试命令
              </ElButton>
              <ElButton @click="clearResults">清空结果</ElButton>
            </ElFormItem>
          </ElForm>
        </ElCollapseItem>
        
        <ElCollapseItem title="配置验证" name="validate">
          <template #title>
            <div class="collapse-title">
              <el-icon><Checked /></el-icon>
              <span>配置验证</span>
            </div>
          </template>
          
          <ElForm :model="validateForm" :rules="validateRules" label-width="120px">
            <ElFormItem label="监控项名称" prop="name">
              <ElInput v-model="validateForm.name" placeholder="请输入监控项名称" />
            </ElFormItem>
            
            <ElFormItem label="监控命令" prop="command">
              <el-input 
                v-model="validateForm.command" 
                type="textarea"
                placeholder="请输入监控命令"
                :rows="3"
              />
            </ElFormItem>
            
            <ElFormItem label="分类">
              <ElSelect v-model="validateForm.category" placeholder="请选择分类" clearable>
                <ElOption
                  v-for="option in categoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
            
            <ElFormItem label="数据类型">
              <ElSelect v-model="validateForm.data_type">
                <ElOption
                  v-for="option in dataTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
            
            <ElFormItem label="超时时间">
              <ElInputNumber 
                v-model="validateForm.timeout" 
                :min="1" 
                :max="300" 
                controls-position="right"
              />
              <span style="margin-left: 8px; color: #6b7280;">秒</span>
            </ElFormItem>
            
            <ElFormItem>
              <ElButton 
                type="success" 
                :loading="validating" 
                @click="handleValidateMonitor"
              >
                <el-icon><Checked /></el-icon>
                验证配置
              </ElButton>
              <ElButton @click="clearResults">清空结果</ElButton>
            </ElFormItem>
          </ElForm>
        </ElCollapseItem>
      </ElCollapse>
    </ElCard>

    <!-- Test Results -->
    <ElCard v-if="testResult" class="result-card">
      <template #header>
        <div class="card-header">
          <span>测试结果</span>
          <el-icon :color="testResult.success ? '#10b981' : '#ef4444'">
            <Checked v-if="testResult.success" />
            <Close v-else />
          </el-icon>
        </div>
      </template>
      
      <div class="result-content">
        <ElAlert
          :title="testResult.success ? '命令执行成功' : '命令执行失败'"
          :type="testResult.success ? 'success' : 'error'"
          :closable="false"
          show-icon
        />
        
        <div class="result-details">
          <div class="detail-item">
            <span class="detail-label">执行时间:</span>
            <span class="detail-value">{{ testResult.execution_time.toFixed(2) }}s</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">安全级别:</span>
            <ElTag :color="getSecurityLevelColor(testResult.security_level)" size="small">
              {{ getSecurityLevelText(testResult.security_level) }}
            </ElTag>
          </div>
          
          <div v-if="testResult.output_sample" class="detail-item">
            <span class="detail-label">输出样例:</span>
            <code class="output-sample">{{ testResult.output_sample }}</code>
          </div>
          
          <div v-if="testResult.error" class="detail-item">
            <span class="detail-label">错误信息:</span>
            <span class="error-message">{{ testResult.error }}</span>
          </div>
        </div>
        
        <div v-if="testResult.recommendations.length" class="recommendations">
          <h4>建议:</h4>
          <ul>
            <li v-for="rec in testResult.recommendations" :key="rec">{{ rec }}</li>
          </ul>
        </div>
      </div>
    </ElCard>

    <!-- Validation Results -->
    <ElCard v-if="validateResult" class="result-card">
      <template #header>
        <div class="card-header">
          <span>验证结果</span>
          <el-icon :color="validateResult.is_valid ? '#10b981' : '#ef4444'">
            <Checked v-if="validateResult.is_valid" />
            <Warning v-else />
          </el-icon>
        </div>
      </template>
      
      <div class="result-content">
        <ElAlert
          :title="validateResult.is_valid ? '配置验证通过' : '配置验证失败'"
          :type="validateResult.is_valid ? 'success' : 'warning'"
          :closable="false"
          show-icon
        />
        
        <div class="validation-messages">
          <h4>验证信息:</h4>
          <ul>
            <li v-for="msg in validateResult.messages" :key="msg">{{ msg }}</li>
          </ul>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">安全级别:</span>
          <ElTag :color="getSecurityLevelColor(validateResult.security_level)" size="small">
            {{ getSecurityLevelText(validateResult.security_level) }}
          </ElTag>
        </div>
        
        <div v-if="validateResult.suggestions.length" class="suggestions">
          <h4>相关建议:</h4>
          <div class="suggestion-tags">
            <ElTag 
              v-for="suggestion in validateResult.suggestions" 
              :key="suggestion"
              type="info"
              size="small"
            >
              {{ suggestion }}
            </ElTag>
          </div>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.monitor-testing {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.page-header p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.commands-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #1f2937;
}

.commands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.command-item {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.command-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.command-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.command-name {
  font-weight: 500;
  color: #1f2937;
}

.command-code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 8px;
  border-radius: 4px;
  color: #374151;
  display: block;
  white-space: pre-wrap;
  word-break: break-all;
}

.testing-card {
  margin-bottom: 24px;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.result-card {
  margin-bottom: 24px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.detail-value {
  color: #1f2937;
}

.output-sample {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  color: #374151;
  flex: 1;
}

.error-message {
  color: #ef4444;
  flex: 1;
}

.recommendations,
.validation-messages,
.suggestions {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.recommendations h4,
.validation-messages h4,
.suggestions h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 14px;
}

.recommendations ul,
.validation-messages ul {
  margin: 0;
  padding-left: 16px;
  color: #6b7280;
}

.suggestions .suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .page-header h2 {
    font-size: 24px;
  }
  
  .commands-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-label {
    min-width: auto;
  }
}
</style>