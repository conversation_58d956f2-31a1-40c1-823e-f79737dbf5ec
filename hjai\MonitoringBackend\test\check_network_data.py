"""
检查网卡数据脚本

用于查询数据库中的网卡监控数据
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# 导入配置
from tortoise import Tortoise
from config.db import TORTOISE_ORM

# 导入模型
from models.network_stats import NetworkStatsSummary, NetworkStatsDetail

async def check_network_data():
    """检查数据库中的网卡数据"""
    logger.info("开始检查网卡数据")
    
    # 初始化ORM
    logger.info("初始化ORM...")
    await Tortoise.init(config=TORTOISE_ORM)
    
    try:
        # 查询摘要数据
        summary_count = await NetworkStatsSummary.all().count()
        logger.info(f"数据库中有 {summary_count} 条网卡摘要记录")
        
        if summary_count > 0:
            # 查询最新的5条记录
            latest_summaries = await NetworkStatsSummary.all().order_by("-created_at").limit(5)
            logger.info(f"最新的 {len(latest_summaries)} 条记录:")
            
            for summary in latest_summaries:
                logger.info(f"ID: {summary.id}, IP: {summary.ip}, 时间: {summary.timestamp}, 网卡数: {summary.total_interfaces}, 活动网卡: {summary.active_interfaces}")
                
                # 查询关联的详细记录
                details = await NetworkStatsDetail.filter(summary=summary).all()
                logger.info(f"  - 关联的详细记录数: {len(details)}")
                
                for detail in details:
                    logger.info(f"  - 网卡: {detail.interface_name}, 状态: {detail.state}, 接收速率: {detail.current_rx_speed} Mbps, 发送速率: {detail.current_tx_speed} Mbps")
        else:
            logger.warning("数据库中没有网卡监控数据")
            
    except Exception as e:
        logger.error(f"检查过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭ORM连接
        await Tortoise.close_connections()
        logger.info("检查完成")

async def main():
    """主函数"""
    await check_network_data()

if __name__ == "__main__":
    asyncio.run(main()) 