#!/bin/bash

# 设置严格的错误处理
set -euo pipefail

# 变量定义
cuda_version=~/nfs/cuda/cuda_12.4.0_550.54.14_linux.run
cuda_ver=$(basename "$cuda_version" | grep -oP 'cuda_\K\d+\.\d+')
cuda_path="/usr/local/cuda-$cuda_ver"

# 1. 系统环境检查
echo "=== 系统环境检查 ==="
lspci | grep -i nvidia || { echo "错误：未检测到NVIDIA显卡"; exit 1; }
uname -m && cat /etc/*release
gcc --version || { echo "错误：GCC未安装"; exit 1; }
uname -r

# 等待用户确认
read -p "环境检查已完成，请确认上述信息是否正确 [y/N] " confirm
if [[ "$confirm" != [yY] ]]; then
    echo "安装已取消"
    exit 1
fi


# 2. 安装CUDA
echo "=== 安装CUDA ==="
[ -f "$cuda_version" ] || { echo "错误：CUDA安装文件不存在: $cuda_version"; exit 1; }
sudo sh "$cuda_version"

# 3. 设置环境变量
echo "=== 设置环境变量 ==="
cat <<EOF | tee -a ~/.bashrc ~/.profile >/dev/null
# CUDA环境变量
export PATH="$cuda_path/bin:\$PATH"
export LD_LIBRARY_PATH="$cuda_path/lib64:\$LD_LIBRARY_PATH"
export CUDACXX="$cuda_path/bin/nvcc"
EOF

# 应用环境变量
source ~/.bashrc
source ~/.profile

# 启动NVIDIA持久模式
sudo /usr/bin/nvidia-persistenced --verbose

# GPU Direct支持
sudo modprobe nvidia-peermem
echo nvidia-peermem | sudo tee -a /etc/modules >/dev/null

# 4. 安装后检查
echo "=== 安装后检查 ==="
cat /proc/driver/nvidia/version
nvcc --version || { echo "错误：nvcc未正确安装"; exit 1; }
nvidia-smi || { echo "错误：nvidia-smi不可用"; exit 1; }

# 清理旧版本（可选）
echo "=== 清理旧版本 ==="
sudo apt-get remove --purge "cuda-repo-*" 2>/dev/null || true