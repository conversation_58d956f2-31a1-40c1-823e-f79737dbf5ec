# 最早导入时区设置，确保所有后续代码都使用正确的时区
import os
import sys
from pathlib import Path
from datetime import datetime  # 添加datetime模块导入

# 设置上海时区环境变量(尽管在Windows上不能通过tzset生效)
os.environ["TZ"] = "Asia/Shanghai"

# 导入新的时区处理模块
from config.timezone_utils import TZ, ModernJSONEncoder
# 保持向后兼容
from config.timezone import now, utcnow, DateTimeWithTZEncoder

# 显示当前时间，确认时区设置正确
utc_time = TZ.now_utc()
shanghai_time = TZ.now_shanghai()
print(f"应用启动时间 (UTC): {utc_time}")
print(f"应用启动时间 (上海时区): {shanghai_time}")
print(f"时差: {(shanghai_time.hour - utc_time.hour) % 24}小时")

import asyncio
import logging
import uvicorn
from fastapi import FastAPI, Request, Response, status, HTTPException
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder

# 导入配置模块
from config.app_config import APP_TITLE, APP_DESCRIPTION, APP_VERSION, APP_DOCS_URL, APP_REDOC_URL, setup_app_config
from config.logging_config import setup_logging
from config.uvicorn_config import get_uvicorn_config
from config.db import init_db, close_db, init_tortoise

# 导入核心功能模块
from core.monitoring import monitoring_scheduler, start_monitoring_with_timeout, done_callback
from core.exception_handlers import global_exception_handler, http_exception_handler, validation_exception_handler

# 导入路由
from routers.root import router as root_router
from routers.ip_user import router as ip_router
from routers.gpu_stats import router as gpu_stats_router
from routers.custom_monitor import router as custom_monitor_router
from routers.memory_stats import router as memory_stats_router
from routers.network_stats import router as network_stats_router

# 设置日志
logger = setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用程序生命周期管理
    
    在这个上下文管理器中，我们:
    1. 初始化核心服务 (数据库等)
    2. 创建后台任务启动监控系统（但不等待它完成）
    3. 启动HTTP服务 (yield)
    """
    global monitoring_scheduler
    
    # 阶段1: 初始化核心服务 (高优先级)
    try:
        # 初始化ORM和数据库
        logger.info("【高优先级】正在初始化数据库...")
        # 初始化ORM
        init_tortoise(app)
        # 初始化数据库(创建表等)
        await init_db()
        logger.info("【高优先级】数据库初始化完成")
        
        # 在这里可以添加其他高优先级服务的初始化
        # 例如: Redis, 缓存, 微服务连接等
    except Exception as e:
        logger.error(f"【高优先级】核心服务初始化失败: {str(e)}")
    
    # 阶段2: 创建后台任务启动监控系统，但不等待它完成
    # 注意：这不会阻塞HTTP服务启动，因为我们只是创建任务，不等待它完成
    logger.info("【低优先级】创建监控系统启动任务...")
    
    # 创建监控启动任务，但不等待它完成
    monitoring_task = asyncio.create_task(start_monitoring_with_timeout())
    
    # 添加回调函数
    monitoring_task.add_done_callback(done_callback)
    
    # HTTP服务准备就绪，开始接受请求
    logger.info("【高优先级】HTTP服务已准备就绪，即将开始接受请求...")
    
    # yield之后，HTTP服务开始处理请求
    yield
    
    # 应用关闭时执行的代码
    logger.info("正在关闭应用程序...")
    
    # 停止监控调度器
    if monitoring_scheduler:
        logger.info("正在停止监控调度器...")
        await monitoring_scheduler.stop()
        logger.info("监控调度器已停止")
    
    # 关闭数据库连接
    await close_db()
    logger.info("应用程序已关闭")

# 自定义FastAPI JSON响应处理，使用新的时区处理方式
class CustomJSONResponse(JSONResponse):
    """
    自定义JSON响应类，统一处理时区转换

    新的处理方式：
    1. 使用ModernJSONEncoder进行时区转换
    2. 移除复杂的字符串替换逻辑
    3. 确保所有datetime都转换为上海时区显示格式
    """
    def render(self, content) -> bytes:
        # 使用新的JSON编码器，自动处理时区转换
        json_str = super().render(jsonable_encoder(content, custom_encoder={
            datetime: lambda dt: TZ.to_display_format(dt)
        }))

        return json_str

def create_app():
    """
    创建FastAPI应用实例
    
    Returns:
        FastAPI: FastAPI应用实例
    """
    # 创建FastAPI应用实例
    app = FastAPI(
        title=APP_TITLE,
        description=APP_DESCRIPTION,
        version=APP_VERSION,
        docs_url=APP_DOCS_URL,
        redoc_url=APP_REDOC_URL,
        lifespan=lifespan,
        default_response_class=CustomJSONResponse  # 设置默认响应类为自定义响应类
    )
    
    # 设置应用配置
    setup_app_config(app)
    
    # 注册异常处理器
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(Exception, global_exception_handler)
    
    # 注册路由
    app.include_router(root_router)
    app.include_router(ip_router)
    app.include_router(gpu_stats_router)
    app.include_router(custom_monitor_router)
    app.include_router(memory_stats_router)
    app.include_router(network_stats_router)
    
    return app

# 创建应用实例
app = create_app()

if __name__ == "__main__":
    # 记录时区设置
    logger.info(f"正在启动应用程序，时区设置为：Asia/Shanghai，当前时间：{now()}")
    
    # 启动Uvicorn服务器
    uvicorn_config = get_uvicorn_config()
    uvicorn.run(app, **uvicorn_config)