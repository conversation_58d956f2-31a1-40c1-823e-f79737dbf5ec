/**
 * 时区工具函数
 * 处理中国标准时间（UTC+8）的转换
 */

// 中国时区偏移量（毫秒）
const CHINA_TIMEZONE_OFFSET = 8 * 60 * 60 * 1000

/**
 * 获取当前的中国标准时间
 * @returns {Date} 当前的中国标准时间
 */
export function getChineseTime () {
  const now = new Date()
  // 获取当前的UTC时间戳
  const utcTime = now.getTime() + now.getTimezoneOffset() * 60 * 1000
  // 转换为中国标准时间
  return new Date(utcTime + CHINA_TIMEZONE_OFFSET)
}

/**
 * 将UTC时间转换为中国标准时间
 * @param {Date|string} utcTime UTC时间或ISO字符串
 * @returns {Date} 中国标准时间
 */
export function utcToChineseTime (utcTime) {
  const date = utcTime instanceof Date ? utcTime : new Date(utcTime)
  // 获取UTC时间戳
  const utcTimestamp = date.getTime() + date.getTimezoneOffset() * 60 * 1000
  // 转换为中国标准时间
  return new Date(utcTimestamp + CHINA_TIMEZONE_OFFSET)
}

/**
 * 将中国标准时间转换为ISO字符串
 * @param {Date} chineseTime 中国标准时间
 * @returns {string} ISO字符串
 */
export function chineseTimeToISO (chineseTime) {
  // 获取中国标准时间的时间戳
  const chineseTimestamp = chineseTime.getTime()
  // 转换为UTC时间戳
  const utcTimestamp = chineseTimestamp - CHINA_TIMEZONE_OFFSET + (new Date().getTimezoneOffset() * 60 * 1000)
  // 转换为ISO字符串
  return new Date(utcTimestamp).toISOString()
}

/**
 * 格式化日期时间为可读字符串
 * @param {Date|string} date 日期对象或ISO字符串
 * @param {string} format 格式化模式，默认为 'yyyy-MM-dd HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime (date, format = 'yyyy-MM-dd HH:mm:ss') {
  const d = date instanceof Date ? date : new Date(date)

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('yyyy', year)
    .replace('MM', month)
    .replace('dd', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 生成上海时区(UTC+8)的ISO时间字符串
 * 确保时间值不会因为toISOString方法而被转换为UTC
 * @param {Date} date 日期对象
 * @returns {string} 上海时区的ISO时间字符串
 */
export function toShanghaiISOString (date) {
  // 复制日期对象，避免修改原始对象
  const shanghaiDate = new Date(date.getTime())

  // 格式化年月日时分秒毫秒
  const year = shanghaiDate.getFullYear()
  const month = String(shanghaiDate.getMonth() + 1).padStart(2, '0')
  const day = String(shanghaiDate.getDate()).padStart(2, '0')
  const hours = String(shanghaiDate.getHours()).padStart(2, '0')
  const minutes = String(shanghaiDate.getMinutes()).padStart(2, '0')
  const seconds = String(shanghaiDate.getSeconds()).padStart(2, '0')
  const milliseconds = String(shanghaiDate.getMilliseconds()).padStart(3, '0')

  // 手动构建ISO字符串，添加上海时区标识
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+08:00`
}
