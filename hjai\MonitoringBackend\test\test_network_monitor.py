"""
网卡监控测试脚本

用于测试网卡监控功能是否正常工作
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 配置日志 - 使用DEBUG级别
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别以查看更多信息
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# 设置paramiko日志级别为DEBUG
paramiko_logger = logging.getLogger("paramiko")
paramiko_logger.setLevel(logging.DEBUG)

# 导入配置
from config.db import init_db, close_db
from tortoise import Tortoise
from config.db import TORTOISE_ORM

# 导入监控模块
from scripts.network_monitor import NetworkMonitor
from models.network_stats import NetworkStatsSummary, NetworkStatsDetail
from models.ip_user import IPUser

async def test_network_monitor():
    """测试网卡监控功能"""
    logger.info("开始测试网卡监控功能")
    
    # 初始化ORM
    logger.info("初始化ORM...")
    await Tortoise.init(config=TORTOISE_ORM)
    
    try:
        # 检查是否有可用的服务器
        servers = await IPUser.filter(is_deleted=False).all()
        if not servers:
            logger.warning("数据库中没有可用的服务器，请先添加服务器")
            return
            
        logger.info(f"找到 {len(servers)} 台服务器:")
        for server in servers:
            logger.info(f"  - ID: {server.id}, IP: {server.ip}, 用户名: {server.username}, 可连接: {getattr(server, 'is_connectable', '未知')}")
        
        # 创建网卡监控实例
        monitor = NetworkMonitor()
        
        # 手动监控每台服务器
        for server in servers:
            logger.info(f"正在监控服务器 {server.ip}...")
            
            # 准备服务器信息
            server_info = {
                "id": server.id,
                "ip": server.ip,
                "username": server.username,
                "password": server.password,
                "is_connectable": getattr(server, "is_connectable", True)
            }
            
            # 监控单台服务器
            success = await monitor.monitor_server(
                server=server_info,
                data_collector=monitor.get_network_info,
                data_saver=monitor.save_network_stats
            )
            
            if success:
                logger.info(f"服务器 {server.ip} 监控成功")
            else:
                logger.error(f"服务器 {server.ip} 监控失败")
        
        # 检查是否保存了数据
        summary_count = await NetworkStatsSummary.all().count()
        detail_count = await NetworkStatsDetail.all().count()
        
        logger.info(f"监控完成，保存了 {summary_count} 条摘要记录和 {detail_count} 条详细记录")
        
        if summary_count > 0:
            # 查询最新的记录
            latest_summary = await NetworkStatsSummary.all().order_by("-created_at").first()
            logger.info(f"最新记录: {latest_summary}")
            
            # 查询关联的详细记录
            details = await NetworkStatsDetail.filter(summary=latest_summary).all()
            logger.info(f"关联的详细记录数: {len(details)}")
            
            for detail in details:
                logger.info(f"网卡 {detail.interface_name}: 状态={detail.state}, 速率={detail.current_rx_speed}/{detail.current_tx_speed} Mbps")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭ORM连接
        await Tortoise.close_connections()
        logger.info("测试完成")

async def main():
    """主函数"""
    await test_network_monitor()

if __name__ == "__main__":
    asyncio.run(main()) 