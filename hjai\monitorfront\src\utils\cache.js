/**
 * 简单的前端缓存工具
 * 用于缓存API请求结果，减少重复请求
 */

// 缓存存储
const cacheStore = {}

// 默认缓存时间（毫秒）
const DEFAULT_CACHE_TIME = 60 * 1000 // 1分钟

/**
 * 设置缓存
 * @param {string} key - 缓存键名
 * @param {any} value - 缓存值
 * @param {number} expireTime - 过期时间（毫秒）
 */
export function setCache (key, value, expireTime = DEFAULT_CACHE_TIME) {
  cacheStore[key] = {
    value,
    expire: Date.now() + expireTime
  }
}

/**
 * 获取缓存
 * @param {string} key - 缓存键名
 * @returns {any|null} - 缓存值或null（如果不存在或已过期）
 */
export function getCache (key) {
  const item = cacheStore[key]
  if (!item) return null

  // 检查是否过期
  if (Date.now() > item.expire) {
    removeCache(key)
    return null
  }

  return item.value
}

/**
 * 删除缓存
 * @param {string} key - 缓存键名
 */
export function removeCache (key) {
  if (key in cacheStore) {
    delete cacheStore[key]
  }
}

/**
 * 清除所有缓存
 */
export function clearCache () {
  Object.keys(cacheStore).forEach(key => {
    delete cacheStore[key]
  })
}

/**
 * 生成缓存键
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @returns {string} - 缓存键
 */
export function generateCacheKey (url, params) {
  return `${url}_${JSON.stringify(params || {})}`
}
