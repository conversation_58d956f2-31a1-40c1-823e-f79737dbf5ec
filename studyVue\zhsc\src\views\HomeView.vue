<template>
  <div class="home-container">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <van-search
        v-model="searchKey"
        placeholder="请输入搜索关键词"
        @search="onSearch"
        shape="round"
        show-action
        @cancel="onCancel"
      />
    </div>

    <!-- 轮播图 -->
    <div class="swiper-container">
      <van-swipe :autoplay="3000" indicator-color="#409eff">
        <van-swipe-item v-for="(item, index) in banners" :key="index">
          <img :src="item.image" class="swipe-img" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 分类导航 -->
    <div class="category-nav">
      <div
        v-for="(item, index) in categories"
        :key="index"
        class="category-item"
        @click="onCategoryClick(item)"
      >
        <img :src="item.icon" class="category-icon" />
        <span class="category-name">{{ item.name }}</span>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="section-title">
      <span class="title-text">精选商品</span>
      <span class="title-more" @click="loadMoreProducts">查看更多</span>
    </div>

    <div class="product-list">
      <div
        v-for="(item, index) in products"
        :key="index"
        class="product-card"
        @click="viewProduct(item)"
      >
        <img :src="item.image" class="product-image" />
        <div class="product-info">
          <div class="product-name">{{ item.name }}</div>
          <div class="product-price">¥ {{ item.price }}</div>
          <div class="product-sales">已售 {{ item.sales }}+</div>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div class="load-more" v-if="hasMore">
      <van-button type="default" plain size="small" @click="loadMoreProducts">加载更多</van-button>
    </div>
    <div class="no-more" v-else>-- 没有更多商品了 --</div>
  </div>
</template>

<script>
import service from '@/api/request'

export default {
  data () {
    return {
      searchKey: '',
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: false,
      products: [],
      banners: [
        { image: 'https://img01.yzcdn.cn/vant/apple-1.jpg' },
        { image: 'https://img01.yzcdn.cn/vant/apple-2.jpg' },
        { image: 'https://img01.yzcdn.cn/vant/apple-3.jpg' }
      ],
      categories: [
        { name: '新品上市', icon: 'https://img.yzcdn.cn/vant/cat.jpeg' },
        { name: '热卖商品', icon: 'https://img.yzcdn.cn/vant/dog.jpeg' },
        { name: '限时特价', icon: 'https://img.yzcdn.cn/vant/apple-1.jpg' },
        { name: '分类导航', icon: 'https://img.yzcdn.cn/vant/apple-2.jpg' }
      ]
    }
  },
  created () {
    this.loadProducts()
  },
  methods: {
    onSearch () {
      this.$toast(`搜索: ${this.searchKey}`)
      // 重置数据并加载搜索结果
      this.page = 1
      this.products = []
      this.hasMore = true
      this.loadProducts()
    },
    onCancel () {
      this.searchKey = ''
      this.$toast('已取消搜索')
    },
    onCategoryClick (category) {
      this.$toast(`选择分类: ${category.name}`)
    },
    viewProduct (product) {
      // 跳转到商品详情页，并传递商品ID
      this.$router.push({
        name: 'product-detail',
        params: { id: product.id }
      })
    },
    loadProducts () {
      if (this.loading || !this.hasMore) return

      this.loading = true
      // 这里假设API能接受page和pageSize参数
      // 实际使用时需要根据后端API调整
      service.get('/index.php?s=/api/goods/lists', {
        params: {
          page: this.page,
          pageSize: this.pageSize,
          categoryId: '',
          sortType: '',
          search: this.searchKey
        }
      })
        .then(response => {
          if (response.status === 200 && response.data) {
            // 处理返回的商品数据
            if (response.data.list && response.data.list.length > 0) {
              this.products = [...this.products, ...response.data.list]
              this.page++
            } else {
              this.hasMore = false
            }
          } else {
            // 加载失败处理
            this.$toast(response.message || '获取商品列表失败')
            // 如果是第一页加载失败，显示模拟数据
            if (this.page === 1) {
              this.loadMockData()
            }
          }
        })
        .catch(error => {
          console.error('获取商品列表失败:', error)
          this.$toast('获取商品列表失败，请重试')
          // 加载失败时显示模拟数据
          if (this.page === 1) {
            this.loadMockData()
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadMockData () {
      // 加载模拟数据
      this.products = [
        { id: 1, name: '商品1', price: '99.00', sales: 100, image: 'https://img01.yzcdn.cn/vant/apple-1.jpg' },
        { id: 2, name: '商品2', price: '199.00', sales: 200, image: 'https://img01.yzcdn.cn/vant/apple-2.jpg' },
        { id: 3, name: '商品3', price: '299.00', sales: 300, image: 'https://img01.yzcdn.cn/vant/apple-3.jpg' },
        { id: 4, name: '商品4', price: '399.00', sales: 400, image: 'https://img01.yzcdn.cn/vant/apple-4.jpg' }
      ]
    },
    loadMoreProducts () {
      this.loadProducts()
    }
  }
}
</script>

<style>
.home-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 50px;
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.swiper-container {
  padding: 10px 0;
  background-color: #fff;
}

.swipe-img {
  width: 100%;
  height: 160px;
  object-fit: cover;
  display: block;
}

.category-nav {
  display: flex;
  justify-content: space-around;
  padding: 15px 0;
  background-color: #fff;
  margin-top: 10px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  margin-bottom: 5px;
  object-fit: cover;
}

.category-name {
  font-size: 12px;
  color: #333;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin-top: 10px;
  background-color: #fff;
  border-bottom: 1px solid #f2f2f2;
}

.title-text {
  font-size: 15px;
  font-weight: bold;
  color: #333;
}

.title-more {
  font-size: 12px;
  color: #999;
}

.product-list {
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  background-color: #fff;
}

.product-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.product-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.product-info {
  padding: 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 2px;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

.load-more, .no-more {
  text-align: center;
  padding: 15px;
  color: #999;
  font-size: 12px;
  background-color: #fff;
}
</style>
