let isActive = false;
const toggleBtn = document.getElementById('toggle');
const statusDiv = document.getElementById('status');
const intervalInput = document.getElementById('interval');

// 初始化时检查当前状态
chrome.storage.sync.get(['interval', 'isActive'], (result) => {
  if(result.interval && result.isActive) {
    isActive = true;
    toggleBtn.innerHTML = '<i class="fas fa-stop icon"></i><span>停止刷新</span>';
    toggleBtn.classList.replace('primary', 'danger');
    statusDiv.textContent = `运行中 - 每${result.interval/1000}秒刷新一次`;
    statusDiv.style.animation = 'pulse 2s infinite';
    intervalInput.value = `${result.interval/1000}s`;
  } else if(result.interval) {
    statusDiv.textContent = `就绪（当前间隔：${result.interval/1000}秒）`;
    intervalInput.value = `${result.interval/1000}s`;
  } else {
    // 添加默认值和欢迎信息
    statusDiv.textContent = '欢迎使用自动刷新器 - 请设置刷新间隔';
    intervalInput.value = '30s';
  }
  
  // 添加页面加载动画
  document.querySelector('.container').style.opacity = '0';
  setTimeout(() => {
    document.querySelector('.container').style.opacity = '1';
  }, 100);
});

toggleBtn.addEventListener('click', () => {
  isActive = !isActive;
  
  if(isActive) {
    const input = intervalInput.value;
    const unit = input.slice(-1);
    const value = parseInt(input);
    
    if(validateInput(unit, value)) {
      const ms = unit === 's' ? value * 1000 : value * 60000;
      chrome.storage.sync.set({ interval: ms, isActive: true }, () => {
        toggleBtn.innerHTML = '<i class="fas fa-stop icon"></i><span>停止刷新</span>';
        toggleBtn.classList.replace('primary', 'danger');
        statusDiv.textContent = `运行中 - 每${ms/1000}秒刷新一次`;
        // 添加动画效果
        statusDiv.style.animation = 'pulse 2s infinite';
      });
    }
  } else {
    chrome.storage.sync.set({ isActive: false }, () => {
      toggleBtn.innerHTML = '<i class="fas fa-play icon"></i><span>开始刷新</span>';
      toggleBtn.classList.replace('danger', 'primary');
      statusDiv.textContent = '已停止 - 点击开始按钮重新启动';
      // 移除动画效果
      statusDiv.style.animation = 'none';
    });
  }
});

function validateInput(unit, value) {
  if (['s','m'].includes(unit) && !isNaN(value) && value > 0) {
    return true;
  } else {
    alert('请输入有效格式 (例如: 30s 或 5m)');
    return false;
  }
}