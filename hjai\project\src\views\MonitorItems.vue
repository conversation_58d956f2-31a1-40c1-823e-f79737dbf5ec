<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElButton, ElCard, ElTable, ElTableColumn, ElTag, ElDialog, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElSwitch, ElMessage, ElMessageBox, ElInputNumber } from 'element-plus'
import { Plus, Edit, Delete, View, Link } from '@element-plus/icons-vue'
import { monitorAPI, type MonitorItem, type CreateMonitorItemRequest, type UpdateMonitorItemRequest } from '@/api/monitor'
import { serverAPI } from '@/api/server'

const loading = ref(false)
const tableData = ref<MonitorItem[]>([])
const serverList = ref<string[]>([])

// Dialog states
const dialogVisible = ref(false)
const linkDialogVisible = ref(false)
const isEditing = ref(false)
const editingId = ref<number | null>(null)

// Form data
const form = ref<CreateMonitorItemRequest>({
  name: '',
  command: '',
  description: '',
  data_type: 'string',
  timeout: 30,
  retry_count: 2,
  category: '',
  security_level: 'normal'
})

const linkForm = ref({
  monitor_item_id: 0,
  ip_address: ''
})

const dataTypeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: 'JSON', value: 'json' }
]

const securityLevelOptions = [
  { label: '普通', value: 'normal' },
  { label: '低级', value: 'low' },
  { label: '高级', value: 'high' }
]

const categoryOptions = [
  { label: '系统', value: 'system' },
  { label: '网络', value: 'network' },
  { label: '应用', value: 'application' },
  { label: '数据库', value: 'database' },
  { label: '自定义', value: 'custom' }
]

const formRules = {
  name: [
    { required: true, message: '请输入监控项名称', trigger: 'blur' }
  ],
  command: [
    { required: true, message: '请输入监控命令', trigger: 'blur' }
  ]
}

const fetchMonitorItems = async () => {
  try {
    loading.value = true
    const response = await monitorAPI.getMonitorItems()
    tableData.value = response
  } catch (error) {
    ElMessage.error('获取监控项列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const fetchServerList = async () => {
  try {
    const response = await serverAPI.getIPList()
    serverList.value = response
  } catch (error) {
    console.error('获取服务器列表失败:', error)
  }
}

const handleAdd = () => {
  form.value = {
    name: '',
    command: '',
    description: '',
    data_type: 'string',
    timeout: 30,
    retry_count: 2,
    category: '',
    security_level: 'normal'
  }
  isEditing.value = false
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = async (row: MonitorItem) => {
  try {
    const response = await monitorAPI.getMonitorItem(row.id)
    form.value = {
      name: response.name,
      command: response.command,
      description: response.description || '',
      data_type: response.data_type,
      timeout: response.timeout,
      retry_count: response.retry_count,
      category: response.category || '',
      security_level: response.security_level
    }
    isEditing.value = true
    editingId.value = row.id
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取监控项详情失败')
    console.error(error)
  }
}

const handleDelete = async (row: MonitorItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除监控项 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await monitorAPI.deleteMonitorItem(row.id)
    ElMessage.success('删除成功')
    fetchMonitorItems()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const handleLink = (row: MonitorItem) => {
  linkForm.value = {
    monitor_item_id: row.id,
    ip_address: ''
  }
  linkDialogVisible.value = true
}

const handleSubmit = async () => {
  try {
    loading.value = true
    if (isEditing.value && editingId.value) {
      const updateData: UpdateMonitorItemRequest = {
        name: form.value.name,
        description: form.value.description,
        timeout: form.value.timeout,
        enabled: true
      }
      await monitorAPI.updateMonitorItem(editingId.value, updateData)
      ElMessage.success('更新成功')
    } else {
      await monitorAPI.createMonitorItem(form.value)
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    fetchMonitorItems()
  } catch (error) {
    ElMessage.error(isEditing.value ? '更新失败' : '添加失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleLinkSubmit = async () => {
  try {
    await monitorAPI.addMonitorItemIP(linkForm.value)
    ElMessage.success('关联成功')
    linkDialogVisible.value = false
  } catch (error) {
    ElMessage.error('关联失败')
    console.error(error)
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  linkDialogVisible.value = false
}

const toggleEnabled = async (row: MonitorItem) => {
  try {
    const updateData: UpdateMonitorItemRequest = {
      enabled: !row.enabled
    }
    await monitorAPI.updateMonitorItem(row.id, updateData)
    row.enabled = !row.enabled
    ElMessage.success(row.enabled ? '已启用' : '已禁用')
  } catch (error) {
    ElMessage.error('操作失败')
    console.error(error)
  }
}

const getDataTypeTag = (dataType: string) => {
  const typeMap: Record<string, { type: string; text: string }> = {
    string: { type: 'primary', text: '字符串' },
    number: { type: 'success', text: '数字' },
    json: { type: 'warning', text: 'JSON' }
  }
  return typeMap[dataType] || { type: 'info', text: dataType }
}

const getSecurityLevelTag = (level: string) => {
  const levelMap: Record<string, { type: string; text: string }> = {
    low: { type: 'success', text: '低级' },
    normal: { type: 'primary', text: '普通' },
    high: { type: 'danger', text: '高级' }
  }
  return levelMap[level] || { type: 'info', text: level }
}

onMounted(() => {
  fetchMonitorItems()
  fetchServerList()
})
</script>

<template>
  <div class="monitor-items">
    <div class="page-header">
      <div class="header-content">
        <h2>监控项管理</h2>
        <p>配置和管理自定义监控项，支持多种数据类型和安全级别</p>
      </div>
      <ElButton type="primary" :icon="Plus" @click="handleAdd">
        添加监控项
      </ElButton>
    </div>

    <ElCard class="table-card">
      <ElTable 
        :data="tableData" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <ElTableColumn prop="name" label="监控项名称" width="180">
          <template #default="{ row }">
            <div class="item-name">
              <span class="name-text">{{ row.name }}</span>
              <ElTag v-if="!row.enabled" type="info" size="small">已禁用</ElTag>
            </div>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="command" label="监控命令" show-overflow-tooltip>
          <template #default="{ row }">
            <code class="command-text">{{ row.command }}</code>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="category" label="分类" width="100">
          <template #default="{ row }">
            <ElTag v-if="row.category" size="small">{{ row.category }}</ElTag>
            <span v-else class="no-category">-</span>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="数据类型" width="100">
          <template #default="{ row }">
            <ElTag :type="getDataTypeTag(row.data_type).type" size="small">
              {{ getDataTypeTag(row.data_type).text }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="安全级别" width="100">
          <template #default="{ row }">
            <ElTag :type="getSecurityLevelTag(row.security_level).type" size="small">
              {{ getSecurityLevelTag(row.security_level).text }}
            </ElTag>
          </template>
        </ElTableColumn>
        
        <ElTableColumn prop="timeout" label="超时(秒)" width="90" />
        
        <ElTableColumn prop="retry_count" label="重试次数" width="90" />
        
        <ElTableColumn label="状态" width="80">
          <template #default="{ row }">
            <ElSwitch
              v-model="row.enabled"
              @change="toggleEnabled(row)"
              size="small"
            />
          </template>
        </ElTableColumn>
        
        <ElTableColumn label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <ElButton size="small" type="primary" :icon="Edit" @click="handleEdit(row)">
                编辑
              </ElButton>
              <ElButton size="small" type="success" :icon="Link" @click="handleLink(row)">
                关联
              </ElButton>
              <ElButton size="small" type="danger" :icon="Delete" @click="handleDelete(row)">
                删除
              </ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>

    <!-- Add/Edit Dialog -->
    <ElDialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑监控项' : '添加监控项'"
      width="600px"
      :before-close="handleCancel"
    >
      <ElForm :model="form" :rules="formRules" label-width="100px">
        <ElFormItem label="监控项名称" prop="name">
          <ElInput v-model="form.name" placeholder="请输入监控项名称" />
        </ElFormItem>
        
        <ElFormItem label="监控命令" prop="command">
          <el-input 
            v-model="form.command" 
            type="textarea"
            placeholder="请输入监控命令，如：df -h / | awk 'NR==2{print $5}' | sed 's/%//'"
            :rows="3"
          />
        </ElFormItem>
        
        <ElFormItem label="描述信息">
          <el-input 
            v-model="form.description" 
            type="textarea"
            placeholder="请输入描述信息（可选）"
            :rows="2"
          />
        </ElFormItem>
        
        <ElFormItem label="分类">
          <ElSelect v-model="form.category" placeholder="请选择分类" clearable>
            <ElOption
              v-for="option in categoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>
        
        <ElFormItem label="数据类型">
          <ElSelect v-model="form.data_type" placeholder="请选择数据类型">
            <ElOption
              v-for="option in dataTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>
        
        <ElFormItem label="安全级别">
          <ElSelect v-model="form.security_level" placeholder="请选择安全级别">
            <ElOption
              v-for="option in securityLevelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </ElSelect>
        </ElFormItem>
        
        <ElFormItem label="超时时间">
          <ElInputNumber 
            v-model="form.timeout" 
            :min="1" 
            :max="300" 
            controls-position="right"
            style="width: 120px"
          />
          <span style="margin-left: 8px; color: #6b7280;">秒</span>
        </ElFormItem>
        
        <ElFormItem label="重试次数">
          <ElInputNumber 
            v-model="form.retry_count" 
            :min="0" 
            :max="10" 
            controls-position="right"
            style="width: 120px"
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" :loading="loading" @click="handleSubmit">
            {{ isEditing ? '更新' : '添加' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- Link Dialog -->
    <ElDialog
      v-model="linkDialogVisible"
      title="关联服务器"
      width="400px"
      :before-close="handleCancel"
    >
      <ElForm :model="linkForm" label-width="100px">
        <ElFormItem label="选择服务器">
          <ElSelect v-model="linkForm.ip_address" placeholder="请选择要关联的服务器">
            <ElOption
              v-for="ip in serverList"
              :key="ip"
              :label="ip"
              :value="ip"
            />
          </ElSelect>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancel">取消</ElButton>
          <ElButton type="primary" @click="handleLinkSubmit">
            关联
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.monitor-items {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.item-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  color: #1f2937;
}

.command-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  color: #374151;
}

.no-category {
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-content h2 {
    font-size: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    font-size: 12px;
    padding: 4px 6px;
  }
}
</style>