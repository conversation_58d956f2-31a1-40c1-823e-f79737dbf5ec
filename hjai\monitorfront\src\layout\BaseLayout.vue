<template>
  <div class="base-layout">
    <div class="header">
      <!-- 导航栏 -->
      <slot name="header"></slot>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseLayout'
}
</script>

<style scoped>
.base-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.header {
  z-index: 100;
}
.content {
  flex: 1;
}
</style>
