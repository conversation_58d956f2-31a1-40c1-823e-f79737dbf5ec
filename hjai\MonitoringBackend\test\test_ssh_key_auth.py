"""
SSH密钥认证功能测试脚本
测试SSH密钥认证和密码认证的自动切换功能
"""
import asyncio
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from config.db import TORTOISE_ORM
from models.ip_user import IPUser
from utils.ssh_utils import SSHConnectionManager
from config.ssh_config import ssh_config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_ssh_config():
    """测试SSH配置"""
    logger.info("=== 测试SSH配置 ===")
    
    # 测试获取SSH密钥路径
    key_paths = ssh_config.get_private_key_paths()
    logger.info(f"找到的SSH密钥文件: {key_paths}")
    
    default_key = ssh_config.get_default_private_key_path()
    logger.info(f"默认SSH密钥文件: {default_key}")
    
    # 测试SSH配置
    config = ssh_config.get_ssh_config()
    logger.info(f"SSH配置: {config}")
    
    return len(key_paths) > 0


async def test_database_model():
    """测试数据库模型"""
    logger.info("=== 测试数据库模型 ===")
    
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 测试创建SSH密钥认证的服务器记录
        test_ip = "*************"
        
        # 删除可能存在的测试记录
        await IPUser.filter(ip=test_ip).delete()
        
        # 创建SSH密钥认证的服务器
        ssh_server = await IPUser.create(
            ip=test_ip,
            username="testuser",
            password=None,  # 密码为空
            use_ssh_key=True,
            ssh_key_path=None  # 使用默认路径
        )
        
        logger.info(f"创建SSH密钥认证服务器: {ssh_server}")
        logger.info(f"认证方式: {ssh_server.get_auth_method()}")
        logger.info(f"应该使用SSH密钥: {ssh_server.should_use_ssh_key()}")
        
        # 测试创建密码认证的服务器记录
        test_ip2 = "*************"
        await IPUser.filter(ip=test_ip2).delete()
        
        password_server = await IPUser.create(
            ip=test_ip2,
            username="testuser2",
            password="testpass",
            use_ssh_key=False
        )
        
        logger.info(f"创建密码认证服务器: {password_server}")
        logger.info(f"认证方式: {password_server.get_auth_method()}")
        logger.info(f"应该使用SSH密钥: {password_server.should_use_ssh_key()}")
        
        # 测试自动判断认证方式（密码为空时自动使用SSH密钥）
        test_ip3 = "*************"
        await IPUser.filter(ip=test_ip3).delete()
        
        auto_ssh_server = await IPUser.create(
            ip=test_ip3,
            username="testuser3",
            password="",  # 空密码
            use_ssh_key=False  # 明确设置为False，但密码为空
        )
        
        logger.info(f"创建自动SSH密钥认证服务器: {auto_ssh_server}")
        logger.info(f"认证方式: {auto_ssh_server.get_auth_method()}")
        logger.info(f"应该使用SSH密钥: {auto_ssh_server.should_use_ssh_key()}")
        
        # 清理测试数据
        await IPUser.filter(ip__in=[test_ip, test_ip2, test_ip3]).delete()
        
        logger.info("数据库模型测试完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库模型测试失败: {str(e)}")
        return False
    finally:
        await Tortoise.close_connections()


async def test_ssh_connection_manager():
    """测试SSH连接管理器"""
    logger.info("=== 测试SSH连接管理器 ===")
    
    # 测试SSH密钥认证（使用不存在的服务器，只测试连接逻辑）
    ssh_server = {
        "ip": "192.168.1.999",  # 不存在的IP
        "username": "testuser",
        "password": None,
        "use_ssh_key": True,
        "ssh_key_path": None
    }
    
    logger.info("测试SSH密钥认证连接逻辑...")
    ssh_client, connected = SSHConnectionManager.connect_to_server(ssh_server, timeout=5)
    logger.info(f"SSH密钥认证结果: connected={connected}")
    if ssh_client:
        ssh_client.close()
    
    # 测试密码认证
    password_server = {
        "ip": "192.168.1.999",  # 不存在的IP
        "username": "testuser",
        "password": "testpass",
        "use_ssh_key": False
    }
    
    logger.info("测试密码认证连接逻辑...")
    ssh_client, connected = SSHConnectionManager.connect_to_server(password_server, timeout=5)
    logger.info(f"密码认证结果: connected={connected}")
    if ssh_client:
        ssh_client.close()
    
    # 测试自动选择认证方式（密码为空时使用SSH密钥）
    auto_server = {
        "ip": "192.168.1.999",  # 不存在的IP
        "username": "testuser",
        "password": "",  # 空密码
        "use_ssh_key": False
    }
    
    logger.info("测试自动选择认证方式...")
    ssh_client, connected = SSHConnectionManager.connect_to_server(auto_server, timeout=5)
    logger.info(f"自动选择认证结果: connected={connected}")
    if ssh_client:
        ssh_client.close()
    
    logger.info("SSH连接管理器测试完成")
    return True


async def test_real_connection():
    """测试真实连接（需要用户提供真实的服务器信息）"""
    logger.info("=== 测试真实连接 ===")
    
    # 这里可以添加真实的服务器测试
    # 注意：需要用户提供真实的服务器IP和认证信息
    
    print("\n如果您想测试真实的SSH连接，请手动修改以下代码:")
    print("1. 将test_server字典中的IP、用户名等信息替换为真实值")
    print("2. 确保SSH密钥已正确配置或提供正确的密码")
    print("3. 重新运行测试")
    
    # 示例配置（请根据实际情况修改）
    test_server = {
        "ip": "YOUR_SERVER_IP",  # 替换为真实IP
        "username": "YOUR_USERNAME",  # 替换为真实用户名
        "password": None,  # 使用SSH密钥时设为None
        "use_ssh_key": True,
        "ssh_key_path": None  # 使用默认密钥路径
    }
    
    logger.info(f"示例服务器配置: {test_server}")
    logger.info("请修改配置后重新测试真实连接")
    
    return True


async def main():
    """主测试函数"""
    logger.info("开始SSH密钥认证功能测试")
    
    tests = [
        ("SSH配置测试", test_ssh_config),
        ("数据库模型测试", test_database_model),
        ("SSH连接管理器测试", test_ssh_connection_manager),
        ("真实连接测试", test_real_connection)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            logger.info(f"\n开始执行: {test_name}")
            result = await test_func()
            results.append((test_name, result))
            logger.info(f"{test_name} {'成功' if result else '失败'}")
        except Exception as e:
            logger.error(f"{test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    logger.info("\n=== 测试结果汇总 ===")
    for test_name, result in results:
        status = "✓ 成功" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    logger.info(f"\n总计: {success_count}/{total_count} 个测试通过")
    
    return success_count == total_count


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n所有测试通过！SSH密钥认证功能实现正确。")
        sys.exit(0)
    else:
        print("\n部分测试失败，请检查实现。")
        sys.exit(1)
