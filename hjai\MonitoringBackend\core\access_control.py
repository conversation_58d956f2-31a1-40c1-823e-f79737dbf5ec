"""
访问权限控制系统

提供基于角色的访问控制(RBAC)，管理用户权限和资源访问
"""

import logging
from typing import Dict, Any, List, Optional, Set
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib
import secrets

from config.timezone_utils import TZ

logger = logging.getLogger(__name__)


class Permission(Enum):
    """权限枚举"""
    # 监控项权限
    MONITOR_CREATE = "monitor:create"
    MONITOR_READ = "monitor:read"
    MONITOR_UPDATE = "monitor:update"
    MONITOR_DELETE = "monitor:delete"
    MONITOR_EXECUTE = "monitor:execute"
    MONITOR_TEST = "monitor:test"
    
    # 服务器权限
    SERVER_READ = "server:read"
    SERVER_MANAGE = "server:manage"
    
    # 数据权限
    DATA_READ = "data:read"
    DATA_EXPORT = "data:export"
    DATA_DELETE = "data:delete"
    
    # 系统权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_STATS = "system:stats"
    
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"


class Role(Enum):
    """角色枚举"""
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    GUEST = "guest"


@dataclass
class User:
    """用户信息"""
    user_id: str
    username: str
    email: Optional[str] = None
    roles: Set[Role] = field(default_factory=set)
    permissions: Set[Permission] = field(default_factory=set)
    created_at: datetime = field(default_factory=TZ.now_utc)
    last_login: Optional[datetime] = None
    is_active: bool = True
    password_hash: Optional[str] = None
    api_key: Optional[str] = None
    api_key_expires: Optional[datetime] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    def has_permission(self, permission: Permission) -> bool:
        """检查用户是否有指定权限"""
        return permission in self.permissions
    
    def has_role(self, role: Role) -> bool:
        """检查用户是否有指定角色"""
        return role in self.roles
    
    def add_role(self, role: Role):
        """添加角色"""
        self.roles.add(role)
        # 自动添加角色对应的权限
        role_permissions = AccessControlManager.get_role_permissions(role)
        self.permissions.update(role_permissions)
    
    def remove_role(self, role: Role):
        """移除角色"""
        if role in self.roles:
            self.roles.remove(role)
            # 重新计算权限
            self.permissions.clear()
            for remaining_role in self.roles:
                role_permissions = AccessControlManager.get_role_permissions(remaining_role)
                self.permissions.update(role_permissions)
    
    def generate_api_key(self, expires_in_days: int = 30) -> str:
        """生成API密钥"""
        self.api_key = secrets.token_urlsafe(32)
        self.api_key_expires = TZ.now_utc() + timedelta(days=expires_in_days)
        return self.api_key
    
    def is_api_key_valid(self) -> bool:
        """检查API密钥是否有效"""
        if not self.api_key or not self.api_key_expires:
            return False
        return TZ.now_utc() < self.api_key_expires


@dataclass
class AccessLog:
    """访问日志"""
    user_id: str
    action: str
    resource: str
    timestamp: datetime
    success: bool
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    details: Optional[str] = None


class AccessControlManager:
    """访问控制管理器"""
    
    # 角色权限映射
    ROLE_PERMISSIONS = {
        Role.ADMIN: {
            Permission.MONITOR_CREATE, Permission.MONITOR_READ, Permission.MONITOR_UPDATE,
            Permission.MONITOR_DELETE, Permission.MONITOR_EXECUTE, Permission.MONITOR_TEST,
            Permission.SERVER_READ, Permission.SERVER_MANAGE,
            Permission.DATA_READ, Permission.DATA_EXPORT, Permission.DATA_DELETE,
            Permission.SYSTEM_CONFIG, Permission.SYSTEM_ADMIN, Permission.SYSTEM_STATS,
            Permission.USER_CREATE, Permission.USER_READ, Permission.USER_UPDATE, Permission.USER_DELETE
        },
        Role.OPERATOR: {
            Permission.MONITOR_CREATE, Permission.MONITOR_READ, Permission.MONITOR_UPDATE,
            Permission.MONITOR_EXECUTE, Permission.MONITOR_TEST,
            Permission.SERVER_READ,
            Permission.DATA_READ, Permission.DATA_EXPORT,
            Permission.SYSTEM_STATS
        },
        Role.VIEWER: {
            Permission.MONITOR_READ,
            Permission.SERVER_READ,
            Permission.DATA_READ,
            Permission.SYSTEM_STATS
        },
        Role.GUEST: {
            Permission.MONITOR_READ,
            Permission.DATA_READ
        }
    }
    
    def __init__(self):
        self.users: Dict[str, User] = {}
        self.access_logs: List[AccessLog] = []
        self.session_tokens: Dict[str, str] = {}  # token -> user_id
        self.failed_login_attempts: Dict[str, List[datetime]] = {}
        
        # 创建默认管理员用户
        self._create_default_admin()
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        admin_user = User(
            user_id="admin",
            username="admin",
            email="<EMAIL>"
        )
        admin_user.add_role(Role.ADMIN)
        admin_user.password_hash = self._hash_password("admin123")  # 默认密码
        self.users["admin"] = admin_user
        
        logger.info("创建默认管理员用户: admin/admin123")
    
    @staticmethod
    def get_role_permissions(role: Role) -> Set[Permission]:
        """获取角色对应的权限"""
        return AccessControlManager.ROLE_PERMISSIONS.get(role, set())
    
    def create_user(
        self,
        username: str,
        email: Optional[str] = None,
        password: Optional[str] = None,
        roles: Optional[List[Role]] = None
    ) -> User:
        """创建用户"""
        user_id = self._generate_user_id(username)
        
        user = User(
            user_id=user_id,
            username=username,
            email=email
        )
        
        if password:
            user.password_hash = self._hash_password(password)
        
        if roles:
            for role in roles:
                user.add_role(role)
        
        self.users[user_id] = user
        logger.info(f"创建用户: {username} (ID: {user_id})")
        
        return user
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user = self.get_user_by_username(username)
        if not user or not user.is_active:
            self._log_failed_login(username)
            return None
        
        if not user.password_hash:
            self._log_failed_login(username)
            return None
        
        if self._verify_password(password, user.password_hash):
            user.last_login = TZ.now_utc()
            self._log_access(user.user_id, "login", "system", True)
            return user
        else:
            self._log_failed_login(username)
            return None
    
    def authenticate_api_key(self, api_key: str) -> Optional[User]:
        """API密钥认证"""
        for user in self.users.values():
            if user.api_key == api_key and user.is_api_key_valid() and user.is_active:
                self._log_access(user.user_id, "api_access", "system", True)
                return user
        
        self._log_access("unknown", "api_access", "system", False, details="Invalid API key")
        return None
    
    def check_permission(self, user: User, permission: Permission, resource: Optional[str] = None) -> bool:
        """检查用户权限"""
        if not user.is_active:
            return False
        
        has_permission = user.has_permission(permission)
        
        # 记录访问日志
        self._log_access(
            user.user_id,
            f"check_permission:{permission.value}",
            resource or "unknown",
            has_permission
        )
        
        return has_permission
    
    def require_permission(self, user: User, permission: Permission, resource: Optional[str] = None):
        """要求用户具有指定权限，否则抛出异常"""
        if not self.check_permission(user, permission, resource):
            raise PermissionError(f"用户 {user.username} 没有权限 {permission.value}")
    
    def get_user(self, user_id: str) -> Optional[User]:
        """获取用户"""
        return self.users.get(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        for user in self.users.values():
            if user.username == username:
                return user
        return None
    
    def update_user(self, user_id: str, **kwargs) -> bool:
        """更新用户信息"""
        user = self.users.get(user_id)
        if not user:
            return False
        
        for key, value in kwargs.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        logger.info(f"更新用户信息: {user.username}")
        return True
    
    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        if user_id in self.users:
            user = self.users[user_id]
            del self.users[user_id]
            logger.info(f"删除用户: {user.username}")
            return True
        return False
    
    def list_users(self) -> List[User]:
        """列出所有用户"""
        return list(self.users.values())
    
    def _generate_user_id(self, username: str) -> str:
        """生成用户ID"""
        base_id = username.lower()
        counter = 1
        user_id = base_id
        
        while user_id in self.users:
            user_id = f"{base_id}_{counter}"
            counter += 1
        
        return user_id
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_hex = password_hash.split(':')
            expected_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return expected_hash.hex() == hash_hex
        except Exception:
            return False
    
    def _log_access(
        self,
        user_id: str,
        action: str,
        resource: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[str] = None
    ):
        """记录访问日志"""
        log_entry = AccessLog(
            user_id=user_id,
            action=action,
            resource=resource,
            timestamp=TZ.now_utc(),
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details
        )
        
        self.access_logs.append(log_entry)
        
        # 保留最近1000条日志
        if len(self.access_logs) > 1000:
            self.access_logs = self.access_logs[-1000:]
    
    def _log_failed_login(self, username: str):
        """记录失败的登录尝试"""
        now = TZ.now_utc()
        
        if username not in self.failed_login_attempts:
            self.failed_login_attempts[username] = []
        
        self.failed_login_attempts[username].append(now)
        
        # 清理1小时前的记录
        cutoff_time = now - timedelta(hours=1)
        self.failed_login_attempts[username] = [
            attempt for attempt in self.failed_login_attempts[username]
            if attempt > cutoff_time
        ]
        
        # 检查是否需要锁定账户
        if len(self.failed_login_attempts[username]) >= 5:
            user = self.get_user_by_username(username)
            if user:
                user.is_active = False
                logger.warning(f"用户 {username} 因多次登录失败被锁定")
    
    def get_access_logs(
        self,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        limit: int = 100
    ) -> List[AccessLog]:
        """获取访问日志"""
        logs = self.access_logs
        
        if user_id:
            logs = [log for log in logs if log.user_id == user_id]
        
        if action:
            logs = [log for log in logs if action in log.action]
        
        return logs[-limit:]
    
    def get_user_stats(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        total_users = len(self.users)
        active_users = sum(1 for user in self.users.values() if user.is_active)
        
        role_counts = {}
        for role in Role:
            role_counts[role.value] = sum(
                1 for user in self.users.values() if user.has_role(role)
            )
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': total_users - active_users,
            'role_distribution': role_counts,
            'total_access_logs': len(self.access_logs)
        }


# 全局访问控制管理器实例
_access_control_manager: Optional[AccessControlManager] = None


def get_access_control_manager() -> AccessControlManager:
    """获取全局访问控制管理器实例"""
    global _access_control_manager
    if _access_control_manager is None:
        _access_control_manager = AccessControlManager()
    return _access_control_manager
