"""
监控系统启动模块

本模块负责监控系统的启动和管理。
"""

import asyncio
import logging
import traceback
from typing import Optional

# 获取日志记录器
logger = logging.getLogger(__name__)

# 创建异步任务存储变量
monitoring_scheduler = None

async def start_monitoring_async():
    """异步启动监控系统，不阻塞HTTP服务"""
    try:
        # 等待短暂时间确保HTTP服务已启动
        await asyncio.sleep(5)
        
        logger.info("【低优先级】正在启动服务器监控系统...")
        
        # 导入监控模块
        from scripts.monitor_scheduler import MonitorScheduler
        
        # 创建新的调度器实例
        monitor_scheduler = MonitorScheduler()
        
        # 设置监控参数
        connectivity_interval = 3600  # 每1小时检查一次连通性
        resource_interval = 300       # 每5分钟监控一次资源
        
        # 启动监控调度器（不等待它完成）
        logger.info(f"开始启动监控调度器（连通性检查间隔：{connectivity_interval}秒，资源监控间隔：{resource_interval}秒）")
        
        # 启动监控系统，不阻塞当前函数执行
        # 由于修改了start方法，它现在不会阻塞，而是立即返回
        await monitor_scheduler.start(connectivity_interval, resource_interval)
        
        # 设置运行状态
        monitor_scheduler.running = True
        
        logger.info("监控系统已成功启动，后台任务正在运行")
        
        # 不等待这些任务完成，让它们在后台运行
        return monitor_scheduler
        
    except asyncio.CancelledError:
        logger.info("【低优先级】监控启动任务被取消")
        raise
    except Exception as e:
        logger.error(f"【低优先级】启动监控系统时出错: {str(e)}")
        # 记录详细错误，以便排查
        logger.error(traceback.format_exc())
        
        # 即使发生错误也返回None，避免阻塞HTTP服务
        return None

async def start_monitoring_with_timeout() -> Optional[object]:
    """
    为监控启动任务创建一个有超时保护的包装函数
    
    Returns:
        Optional[object]: 监控调度器实例或None（如果启动失败）
    """
    try:
        # 设置监控启动总超时为2分钟
        return await asyncio.wait_for(start_monitoring_async(), timeout=120)
    except asyncio.TimeoutError:
        logger.error("【低优先级】监控系统启动超时，但HTTP服务不受影响")
        return None
    except Exception as e:
        logger.error(f"【低优先级】监控系统启动出错: {str(e)}")
        return None

def done_callback(task):
    """
    在监控启动任务完成时获取监控调度器实例
    
    Args:
        task: 异步任务对象
    """
    global monitoring_scheduler
    try:
        if not task.cancelled():
            monitoring_scheduler = task.result()
            if monitoring_scheduler:
                logger.info("【低优先级】监控调度器实例已保存")
            else:
                logger.warning("【低优先级】监控调度器启动失败，但HTTP服务正常运行")
    except Exception as e:
        logger.error(f"【低优先级】获取监控调度器实例失败: {str(e)}") 