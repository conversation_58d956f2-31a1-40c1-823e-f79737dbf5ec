from fastapi import APIRouter, HTTPException
from typing import List, Optional, Dict, Any
from models.ip_user import IPUser
from pydantic import BaseModel, Field, field_validator, model_validator
import re


router = APIRouter(
    prefix="/ip",
    tags=["IP用户管理"]
)

# IPv4地址正则表达式
IPV4_PATTERN = r"^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$"


class IPUserListRequest(BaseModel):
    """
    IP用户列表请求模型
    """
    page: int = 1  # 改为page更符合命名规范
    page_size: Optional[int] = 10  # 添加可自定义每页数量
    is_deleted: Optional[bool] = None
    is_connectable: Optional[bool] = None


class IPUserResponse(BaseModel):
    """
    IP用户响应模型
    """
    id: int
    ip: str
    username: str
    is_deleted: bool
    is_connectable: bool


class PaginatedResponse(BaseModel):
    """
    分页响应模型
    """
    data: List[IPUserResponse]
    total: int
    page: int
    page_size: int
    pages: int


class IPUserAddRequest(BaseModel):
    """
    添加IP用户请求模型
    """
    ip: str = Field(..., description="IP地址(必须是有效的IPv4格式)")
    user: str = Field(..., description="用户名")
    password: Optional[str] = Field(None, description="密码（可选，为空时使用SSH密钥认证）")
    use_ssh_key: bool = Field(False, description="是否使用SSH密钥认证")
    ssh_key_path: Optional[str] = Field(None, description="SSH私钥文件路径（可选，为空时使用默认路径）")

    @field_validator('ip')
    def validate_ip_format(cls, v):
        """验证IP地址是否符合IPv4格式"""
        if not re.match(IPV4_PATTERN, v):
            raise ValueError('必须是有效的IPv4地址格式，例如：***********')
        return v

    @model_validator(mode='after')
    def validate_auth_method(self):
        """验证认证方式配置"""
        if not self.use_ssh_key and not self.password:
            raise ValueError('必须提供密码或启用SSH密钥认证')
        return self


class CommonResponse(BaseModel):
    """
    通用响应模型
    """
    code: int
    message: str


@router.get("/list", response_model=List[str])
async def get_all_ips():
    """
    获取所有未软删除的IP地址列表
    """
    # 查询所有未删除的IP用户记录
    ip_users = await IPUser.filter(is_deleted=False).all()
    
    # 返回IP地址列表
    ip_list = [user.ip for user in ip_users]
    return ip_list


@router.post("/user-list", response_model=PaginatedResponse)
async def get_ip_users(request: IPUserListRequest):
    """
    获取IP用户列表，包含详细信息和分页元数据
    
    参数:
        request: 筛选条件
            - page: 页码，从1开始
            - page_size: 每页记录数，默认10条
            - is_deleted: 是否删除(True:已删除 False:未删除)
            - is_connectable: 是否可连通(True:可连通 False:不可连通)
        
    返回:
        分页数据，包含：
        - data: IP用户列表
        - total: 总记录数
        - page: 当前页码
        - page_size: 每页记录数
        - pages: 总页数
    """
    # 构建查询条件
    query = IPUser.all()
    
    # 处理is_deleted参数
    if request.is_deleted is not None:
        query = query.filter(is_deleted=request.is_deleted)
    
    # 处理is_connectable参数
    if request.is_connectable is not None:
        query = query.filter(is_connectable=request.is_connectable)
    
    # 获取总记录数
    total = await query.count()
    
    # 设置分页参数
    offset = (request.page - 1) * request.page_size
    limit = request.page_size
    
    # 计算总页数
    pages = (total + request.page_size - 1) // request.page_size
    
    # 执行查询并进行分页
    ip_users = await query.offset(offset).limit(limit).all()
    
    # 构建用户列表
    user_list = [
        IPUserResponse(
            id=user.id,
            ip=user.ip,
            username=user.username,
            is_deleted=user.is_deleted,
            is_connectable=user.is_connectable
        ) 
        for user in ip_users
    ]
    
    # 返回带分页信息的结果
    return PaginatedResponse(
        data=user_list,
        total=total,
        page=request.page,
        page_size=request.page_size,
        pages=pages
    )


@router.post("/add", response_model=CommonResponse)
async def add_ip_user(request: IPUserAddRequest):
    """
    添加新的IP用户，支持密码认证和SSH密钥认证

    参数:
        request: 添加IP用户的请求
            - ip: IP地址(必须是有效的IPv4格式)
            - user: 用户名(不能为空)
            - password: 密码(可选，为空时使用SSH密钥认证)
            - use_ssh_key: 是否使用SSH密钥认证
            - ssh_key_path: SSH私钥文件路径(可选)

    返回:
        - 如果IP格式无效，返回400错误
        - 如果认证方式配置无效，返回400错误
        - 如果IP已存在，返回提示消息
        - 添加成功返回成功消息
    """
    # 验证必要字段不为空
    if not request.ip or not request.user:
        return CommonResponse(code=400, message="IP和用户名不能为空")

    # 检查IP是否已存在
    ip_exists = await IPUser.filter(ip=request.ip).exists()
    if ip_exists:
        return CommonResponse(code=200, message=f"IP {request.ip} 已经存在")

    # 创建新的IP用户记录
    new_ip_user = IPUser(
        ip=request.ip,
        username=request.user,
        password=request.password,
        use_ssh_key=request.use_ssh_key,
        ssh_key_path=request.ssh_key_path,
        is_deleted=False,  # 默认未删除
        is_connectable=True  # 默认可连通
    )

    # 保存到数据库
    await new_ip_user.save()

    # 返回成功消息，包含认证方式信息
    auth_method = new_ip_user.get_auth_method()
    return CommonResponse(code=200, message=f"添加成功，认证方式: {auth_method}")