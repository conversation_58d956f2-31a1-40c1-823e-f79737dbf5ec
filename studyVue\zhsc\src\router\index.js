import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout/LayoutVue.vue'
import Home from '@/views/HomeView.vue'
import Login from '@/views/LoginView.vue'
import User from '@/views/UserView.vue'
import ProductDetail from '@/views/ProductDetailView.vue'
import { clear as clearToast } from '@/utils/toast'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        redirect: '/home'
      },
      {
        path: 'home',
        name: 'home',
        component: Home,
        meta: { requiresAuth: false }
      },
      {
        path: 'user',
        name: 'user',
        component: User,
        meta: { requiresAuth: true }
      },
      {
        path: 'login',
        name: 'login',
        component: Login,
        meta: { requiresAuth: false, guestOnly: true }
      }
    ]
  },
  {
    path: '/product/:id',
    name: 'product-detail',
    component: ProductDetail,
    meta: { requiresAuth: false }
  }
]

const router = new VueRouter({
  mode: 'history',
  routes
})

// 防止路由重复点击报错
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}

// 路由前置守卫 - 优化版
router.beforeEach((to, from, next) => {
  clearToast()

  const hasToken = !!localStorage.getItem('token')

  // 直接使用 to.meta，不使用 matched.some
  const requiresAuth = to.meta.requiresAuth
  const guestOnly = to.meta.guestOnly

  // 调试信息
  console.log('Navigation:', {
    from: from.path,
    to: to.path,
    hasToken,
    requiresAuth,
    guestOnly
  })

  // 需要认证但没有 token
  if (requiresAuth && !hasToken) {
    // 防止从 login 跳转到 login
    if (from.name !== 'login') {
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
    } else {
      next(false)
    }
    return
  }

  // 仅限游客访问但已登录
  if (guestOnly && hasToken) {
    // 防止从目标页跳转到目标页
    const redirectPath = from.query.redirect || '/home'
    if (to.path !== redirectPath && from.path !== redirectPath) {
      next(redirectPath)
    } else if (to.path === '/login') {
      next('/home')
    } else {
      next(false)
    }
    return
  }

  next()
})

// 全局后置钩子 - 用于调试
router.afterEach((to, from) => {
  console.log('Navigation completed:', from.path, '->', to.path)
})

export default router
