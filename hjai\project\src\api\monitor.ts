import api from './index'

export interface MonitorItem {
  id: number
  name: string
  command: string
  description?: string
  data_type: string
  timeout: number
  retry_count: number
  enabled: boolean
  category?: string
  security_level: string
  created_at: string
}

export interface CreateMonitorItemRequest {
  name: string
  command: string
  description?: string
  data_type?: string
  timeout?: number
  retry_count?: number
  category?: string
  security_level?: string
}

export interface UpdateMonitorItemRequest {
  name?: string
  description?: string
  timeout?: number
  enabled?: boolean
}

export interface TestCommandRequest {
  command: string
  ip: string
  username: string
  password?: string
  use_ssh_key?: boolean
  ssh_key_path?: string
  timeout?: number
  data_type?: string
}

export interface TestCommandResponse {
  success: boolean
  error?: string
  execution_time: number
  output_sample: string
  security_level: string
  recommendations: string[]
}

export interface ValidateMonitorRequest {
  name: string
  command: string
  data_type?: string
  timeout?: number
  category?: string
}

export interface ValidateMonitorResponse {
  is_valid: boolean
  messages: string[]
  security_level: string
  suggestions: string[]
}

export interface MonitorItemIP {
  id: number
  monitor_item_id: number
  monitor_item_name: string
  ip_address: string
  created_at: string
}

export interface AddMonitorItemIPRequest {
  monitor_item_id: number
  ip_address: string
}

export interface MonitorStatus {
  global_stats: {
    total_servers: number
    online_servers: number
    offline_servers: number
    total_monitor_items: number
    healthy_items: number
    warning_items: number
    error_items: number
    overall_success_rate: number
  }
  problematic_servers_count: number
  problematic_servers: Array<{
    server_ip: string
    server_status: string
    health_score: number
    error_items: number
    total_items: number
  }>
}

export interface ServerStatus {
  server_ip: string
  server_status: string
  last_check_time: string
  total_monitor_items: number
  healthy_items: number
  warning_items: number
  error_items: number
  overall_health_score: number
  problematic_items: Array<{
    monitor_item_id: number
    monitor_item_name: string
    status: string
    consecutive_failures: number
    success_rate: number
    last_error?: string
    last_check_time: string
  }>
}

export interface MonitorStats {
  scheduler_stats: {
    running: boolean
    total_cycles: number
    successful_cycles: number
    failed_cycles: number
    success_rate: number
    average_cycle_time: number
    last_cycle_time: string
  }
  error_stats: {
    total_errors: number
    connection_errors: number
    timeout_errors: number
    command_errors: number
    other_errors: number
  }
  system_health: {
    scheduler_running: boolean
    success_rate: number
    average_cycle_time: number
    total_errors: number
  }
}

export interface BatchOperationRequest {
  item_ids: number[]
  operation: 'enable' | 'disable' | 'delete'
}

export interface BatchOperationResponse {
  success_count: number
  failed_count: number
  success_items: number[]
  failed_items: Array<{
    id: number
    error: string
  }>
}

export interface ExecuteMonitorRequest {
  monitor_item_id: number
  server_ips?: string[]
}

export interface ExecuteMonitorResponse {
  success: boolean
  message: string
  results: Record<string, {
    success: boolean
    error?: string
    data?: any
  }>
}

export const monitorAPI = {
  // Monitor items management
  getMonitorItems: () => api.get<MonitorItem[]>('/monitor/items'),
  createMonitorItem: (data: CreateMonitorItemRequest) => api.post<MonitorItem>('/monitor/items', data),
  getMonitorItem: (id: number) => api.get<MonitorItem>(`/monitor/items/${id}`),
  updateMonitorItem: (id: number, data: UpdateMonitorItemRequest) => api.put<MonitorItem>(`/monitor/items/${id}`, data),
  deleteMonitorItem: (id: number) => api.delete(`/monitor/items/${id}`),

  // Monitor item IP associations
  addMonitorItemIP: (data: AddMonitorItemIPRequest) => api.post<MonitorItemIP>('/monitor/items/ip', data),
  getMonitorItemsByIP: (ip: string) => api.get<MonitorItem[]>(`/monitor/items/ip/${ip}`),

  // Testing and validation
  testCommand: (data: TestCommandRequest) => api.post<TestCommandResponse>('/monitor/test', data),
  validateMonitor: (data: ValidateMonitorRequest) => api.post<ValidateMonitorResponse>('/monitor/validate', data),
  getSuggestions: (category?: string) => api.get('/monitor/suggestions', { params: { category } }),

  // Status monitoring
  getMonitorStatus: () => api.get<MonitorStatus>('/monitor/status'),
  getServerStatus: (serverIP: string) => api.get<ServerStatus>(`/monitor/status/server/${serverIP}`),
  getMonitorStats: () => api.get<MonitorStats>('/monitor/stats'),
  getItemStatistics: (itemId: number, hours?: number) => api.get(`/monitor/items/${itemId}/statistics`, { params: { hours } }),

  // Batch operations
  batchOperation: (data: BatchOperationRequest) => api.post<BatchOperationResponse>('/monitor/batch', data),
  executeMonitor: (data: ExecuteMonitorRequest) => api.post<ExecuteMonitorResponse>('/monitor/execute', data)
}