import Pool from './base/pool';
import { GAME_STATUS, PLAYER_COLORS, GAME_MODES, AI_DIFFICULTY } from './config/gameConfig';
import Storage from './utils/storage';

let instance;

/**
 * 全局状态管理器
 * 负责管理游戏的状态和数据
 */
export default class DataBus {
  // 游戏数据
  frame = 0; // 当前帧数
  isGameOver = false; // 游戏是否结束
  pool = new Pool(); // 对象池
  
  // 飞行棋游戏特有数据
  gameStatus = GAME_STATUS.WAITING; // 游戏状态
  gameMode = GAME_MODES.LOCAL_MULTIPLAYER; // 游戏模式
  aiDifficulty = AI_DIFFICULTY.MEDIUM; // AI难度
  players = []; // 玩家列表
  currentPlayerIndex = 0; // 当前玩家索引
  diceValue = 0; // 骰子点数
  consecutiveSix = 0; // 连续投掷6点的次数
  lastAction = null; // 最后一次动作
  soundEnabled = true; // 是否启用音效
  vibrationEnabled = true; // 是否启用震动
  
  // 游戏统计数据
  statistics = {
    totalGames: 0,
    wins: 0,
    fastestWin: null,
    longestGame: null
  };
  
  // 存储管理器
  storage = new Storage();

  constructor() {
    // 确保单例模式
    if (instance) return instance;

    instance = this;
    
    // 加载游戏设置
    this.loadSettings();
  }

  /**
   * 重置游戏状态
   */
  reset() {
    this.frame = 0;
    this.isGameOver = false;
    this.gameStatus = GAME_STATUS.WAITING;
    this.players = [];
    this.currentPlayerIndex = 0;
    this.diceValue = 0;
    this.consecutiveSix = 0;
    this.lastAction = null;
  }
  
  /**
   * 初始化游戏
   * @param {number} playerCount - 玩家数量
   * @param {string} gameMode - 游戏模式
   */
  initGame(playerCount, gameMode) {
    this.reset();
    this.gameMode = gameMode;
    this.gameStatus = GAME_STATUS.PLAYING;
    
    // 创建玩家
    this.createPlayers(playerCount);
    
    // 记录游戏开始时间
    this.gameStartTime = Date.now();
  }
  
  /**
   * 创建玩家
   * @param {number} playerCount - 玩家数量
   */
  createPlayers(playerCount) {
    this.players = [];
    
    // 可用的颜色
    const availableColors = [
      PLAYER_COLORS.RED,
      PLAYER_COLORS.YELLOW,
      PLAYER_COLORS.BLUE,
      PLAYER_COLORS.GREEN
    ];
    
    // 创建人类玩家
    this.players.push({
      id: 'player1',
      color: availableColors[0],
      nickname: '玩家1',
      avatar: '',
      isAI: false,
      pieces: this.createPieces(availableColors[0]),
      finishedCount: 0
    });
    
    // 根据游戏模式创建其他玩家
    for (let i = 1; i < playerCount; i++) {
      const isAI = this.gameMode === GAME_MODES.SINGLE_PLAYER;
      this.players.push({
        id: `player${i + 1}`,
        color: availableColors[i],
        nickname: isAI ? `AI ${i}` : `玩家${i + 1}`,
        avatar: '',
        isAI: isAI,
        aiDifficulty: isAI ? this.aiDifficulty : null,
        pieces: this.createPieces(availableColors[i]),
        finishedCount: 0
      });
    }
  }
  
  /**
   * 创建棋子
   * @param {string} color - 棋子颜色
   * @returns {Array} 棋子数组
   */
  createPieces(color) {
    const pieces = [];
    for (let i = 0; i < 4; i++) {
      pieces.push({
        id: `${color}_${i + 1}`,
        position: -1, // -1表示在基地
        status: 'base',
        isStacked: false,
        stackWith: null
      });
    }
    return pieces;
  }
  
  /**
   * 切换到下一个玩家
   */
  nextPlayer() {
    this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.players.length;
    this.consecutiveSix = 0;
  }
  
  /**
   * 获取当前玩家
   * @returns {Object} 当前玩家
   */
  getCurrentPlayer() {
    return this.players[this.currentPlayerIndex];
  }
  
  /**
   * 投掷骰子
   * @returns {number} 骰子点数
   */
  rollDice() {
    this.diceValue = Math.floor(Math.random() * 6) + 1;
    
    // 如果投掷到6，增加连续6的计数
    if (this.diceValue === 6) {
      this.consecutiveSix++;
    } else {
      this.consecutiveSix = 0;
    }
    
    return this.diceValue;
  }
  
  /**
   * 游戏结束
   * @param {string} winnerId - 获胜玩家ID
   */
  gameOver(winnerId) {
    this.isGameOver = true;
    this.gameStatus = GAME_STATUS.FINISHED;
    
    // 计算游戏时长
    const gameTime = Date.now() - this.gameStartTime;
    
    // 更新统计数据
    this.statistics.totalGames++;
    
    // 如果人类玩家获胜
    if (winnerId === 'player1') {
      this.statistics.wins++;
      
      // 更新最快获胜记录
      if (!this.statistics.fastestWin || gameTime < this.statistics.fastestWin) {
        this.statistics.fastestWin = gameTime;
      }
    }
    
    // 更新最长游戏记录
    if (!this.statistics.longestGame || gameTime > this.statistics.longestGame) {
      this.statistics.longestGame = gameTime;
    }
    
    // 保存统计数据
    this.saveStatistics();
  }
  
  /**
   * 保存游戏统计数据
   */
  saveStatistics() {
    this.storage.setItem('gameStatistics', this.statistics);
  }
  
  /**
   * 加载游戏统计数据
   */
  loadStatistics() {
    const stats = this.storage.getItem('gameStatistics');
    if (stats) {
      this.statistics = stats;
    }
  }
  
  /**
   * 保存游戏设置
   */
  saveSettings() {
    const settings = {
      soundEnabled: this.soundEnabled,
      vibrationEnabled: this.vibrationEnabled,
      aiDifficulty: this.aiDifficulty
    };
    this.storage.setItem('gameSettings', settings);
  }
  
  /**
   * 加载游戏设置
   */
  loadSettings() {
    const settings = this.storage.getItem('gameSettings');
    if (settings) {
      this.soundEnabled = settings.soundEnabled;
      this.vibrationEnabled = settings.vibrationEnabled;
      this.aiDifficulty = settings.aiDifficulty;
    }
    
    // 加载统计数据
    this.loadStatistics();
  }
}
