import os
import sys
import asyncio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from tortoise import Tortoise
from config.db import TORTOISE_ORM

async def update_schema():
    """更新数据库表结构"""
    print("正在初始化Tortoise ORM...")
    await Tortoise.init(config=TORTOISE_ORM)
    
    print("正在更新数据库表结构...")
    # 将safe参数设为True，这样只会添加新列，不会重建已存在的表
    await Tortoise.generate_schemas(safe=True)
    
    print("关闭数据库连接...")
    await Tortoise.close_connections()
    
    print("数据库表结构更新完成！")

if __name__ == "__main__":
    asyncio.run(update_schema()) 