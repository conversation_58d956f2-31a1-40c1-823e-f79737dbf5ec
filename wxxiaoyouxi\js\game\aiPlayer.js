/**
 * AI玩家类
 * 实现不同难度的AI策略
 */
import { AI_DIFFICULTY, PIECE_STATUS, BOARD_CONFIG } from '../config/gameConfig';
import GameRules from './rules';

export default class AIPlayer {
  constructor(difficulty = AI_DIFFICULTY.MEDIUM) {
    this.difficulty = difficulty;
    this.thinkingTime = this.getThinkingTime();
  }
  
  /**
   * 获取思考时间（模拟真实玩家）
   */
  getThinkingTime() {
    switch (this.difficulty) {
      case AI_DIFFICULTY.EASY:
        return 500 + Math.random() * 1000; // 0.5-1.5秒
      case AI_DIFFICULTY.MEDIUM:
        return 1000 + Math.random() * 1500; // 1-2.5秒
      case AI_DIFFICULTY.HARD:
        return 1500 + Math.random() * 2000; // 1.5-3.5秒
      default:
        return 1000;
    }
  }
  
  /**
   * AI决策主函数
   * @param {Object} gameState - 当前游戏状态
   * @param {number} diceValue - 骰子点数
   * @returns {Promise<Object>} AI决策结果
   */
  async makeDecision(gameState, diceValue) {
    // 模拟思考时间
    await this.delay(this.thinkingTime);
    
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    const movablePieces = GameRules.getMovablePieces(currentPlayer.pieces, diceValue);
    
    if (movablePieces.length === 0) {
      return { action: 'pass', piece: null };
    }
    
    let selectedPiece;
    
    switch (this.difficulty) {
      case AI_DIFFICULTY.EASY:
        selectedPiece = this.easyStrategy(movablePieces, gameState, diceValue);
        break;
      case AI_DIFFICULTY.MEDIUM:
        selectedPiece = this.mediumStrategy(movablePieces, gameState, diceValue);
        break;
      case AI_DIFFICULTY.HARD:
        selectedPiece = this.hardStrategy(movablePieces, gameState, diceValue);
        break;
      default:
        selectedPiece = this.mediumStrategy(movablePieces, gameState, diceValue);
    }
    
    return {
      action: 'move',
      piece: selectedPiece
    };
  }
  
  /**
   * 简单AI策略 - 随机选择
   */
  easyStrategy(movablePieces, gameState, diceValue) {
    // 简单随机选择，但优先起飞
    const basePieces = movablePieces.filter(piece => piece.status === PIECE_STATUS.BASE);
    
    if (basePieces.length > 0 && diceValue === 6) {
      return basePieces[Math.floor(Math.random() * basePieces.length)];
    }
    
    return movablePieces[Math.floor(Math.random() * movablePieces.length)];
  }
  
  /**
   * 中等AI策略 - 基本策略
   */
  mediumStrategy(movablePieces, gameState, diceValue) {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    const strategies = [];
    
    movablePieces.forEach(piece => {
      const score = this.evaluatePieceMove(piece, diceValue, gameState);
      strategies.push({ piece, score });
    });
    
    // 按分数排序，选择最高分的棋子
    strategies.sort((a, b) => b.score - a.score);
    
    return strategies[0].piece;
  }
  
  /**
   * 困难AI策略 - 高级策略
   */
  hardStrategy(movablePieces, gameState, diceValue) {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    const strategies = [];
    
    movablePieces.forEach(piece => {
      const score = this.evaluatePieceMoveAdvanced(piece, diceValue, gameState);
      strategies.push({ piece, score });
    });
    
    // 按分数排序
    strategies.sort((a, b) => b.score - a.score);
    
    // 有一定概率不选择最优解，增加游戏趣味性
    const randomFactor = Math.random();
    if (randomFactor < 0.1 && strategies.length > 1) {
      return strategies[1].piece; // 10%概率选择次优解
    }
    
    return strategies[0].piece;
  }
  
  /**
   * 评估棋子移动（基础版本）
   */
  evaluatePieceMove(piece, diceValue, gameState) {
    let score = 0;
    
    // 1. 起飞优先级
    if (piece.status === PIECE_STATUS.BASE && diceValue === 6) {
      score += 100;
    }
    
    // 2. 接近终点的棋子优先级更高
    if (piece.status === PIECE_STATUS.RUNWAY || piece.status === PIECE_STATUS.SAFE) {
      const distanceToFinish = this.calculateDistanceToFinish(piece);
      score += (60 - distanceToFinish) * 2;
    }
    
    // 3. 能够击落对手的移动
    const targetPosition = this.calculateTargetPosition(piece, diceValue);
    if (this.canHitOpponent(targetPosition, gameState, piece.color)) {
      score += 80;
    }
    
    // 4. 能够跳跃的移动
    if (this.canJump(targetPosition, piece.color)) {
      score += 30;
    }
    
    // 5. 能够飞行的移动
    if (this.canFly(targetPosition, piece.color)) {
      score += 50;
    }
    
    // 6. 避免被击落
    if (this.isPositionSafe(targetPosition, gameState, piece.color)) {
      score += 20;
    } else {
      score -= 30;
    }
    
    return score;
  }
  
  /**
   * 评估棋子移动（高级版本）
   */
  evaluatePieceMoveAdvanced(piece, diceValue, gameState) {
    let score = this.evaluatePieceMove(piece, diceValue, gameState);
    
    // 高级策略额外考虑因素
    
    // 1. 叠子策略
    const targetPosition = this.calculateTargetPosition(piece, diceValue);
    if (this.canStack(targetPosition, gameState, piece.color)) {
      score += 40;
    }
    
    // 2. 阻挡对手策略
    if (this.canBlockOpponent(targetPosition, gameState, piece.color)) {
      score += 60;
    }
    
    // 3. 形成保护链
    if (this.canFormProtectionChain(targetPosition, gameState, piece.color)) {
      score += 25;
    }
    
    // 4. 预判对手下一步
    const opponentThreat = this.evaluateOpponentThreat(targetPosition, gameState, piece.color);
    score -= opponentThreat;
    
    // 5. 终局策略
    if (this.isEndGame(gameState, piece.color)) {
      score += this.evaluateEndGameMove(piece, diceValue, gameState);
    }
    
    return score;
  }
  
  /**
   * 计算棋子到终点的距离
   */
  calculateDistanceToFinish(piece) {
    if (piece.status === PIECE_STATUS.FINISHED) {
      return 0;
    }
    
    if (piece.status === PIECE_STATUS.BASE) {
      return 60; // 基地到终点的大概距离
    }
    
    if (piece.status === PIECE_STATUS.SAFE) {
      return 6 - (piece.position - 52); // 安全区域内的距离
    }
    
    // 计算在跑道上到安全区域入口的距离
    const startPoint = BOARD_CONFIG.startPoints[piece.color];
    let distance;
    
    if (piece.position >= startPoint) {
      distance = piece.position - startPoint;
    } else {
      distance = BOARD_CONFIG.totalCells - startPoint + piece.position;
    }
    
    return BOARD_CONFIG.totalCells - distance + BOARD_CONFIG.safeRunwayLength;
  }
  
  /**
   * 计算目标位置
   */
  calculateTargetPosition(piece, diceValue) {
    if (piece.status === PIECE_STATUS.BASE) {
      return BOARD_CONFIG.startPoints[piece.color];
    }
    
    return (piece.position + diceValue) % BOARD_CONFIG.totalCells;
  }
  
  /**
   * 检查是否能击落对手
   */
  canHitOpponent(position, gameState, myColor) {
    return gameState.players.some(player => {
      if (player.color === myColor) return false;
      
      return player.pieces.some(piece => {
        return piece.position === position && 
               piece.status === PIECE_STATUS.RUNWAY &&
               !piece.isStacked;
      });
    });
  }
  
  /**
   * 检查是否能跳跃
   */
  canJump(position, color) {
    const coloredCells = BOARD_CONFIG.coloredCells[color];
    return coloredCells && coloredCells.includes(position);
  }
  
  /**
   * 检查是否能飞行
   */
  canFly(position, color) {
    const flyingCells = BOARD_CONFIG.flyingCells[color];
    return flyingCells && flyingCells.includes(position);
  }
  
  /**
   * 检查位置是否安全
   */
  isPositionSafe(position, gameState, myColor) {
    // 检查是否在安全格子上
    const coloredCells = BOARD_CONFIG.coloredCells[myColor];
    if (coloredCells && coloredCells.includes(position)) {
      return true;
    }
    
    // 检查是否有己方棋子可以叠子
    const myPieces = gameState.players.find(p => p.color === myColor).pieces;
    const hasFriendlyPiece = myPieces.some(piece => 
      piece.position === position && piece.status === PIECE_STATUS.RUNWAY
    );
    
    return hasFriendlyPiece;
  }
  
  /**
   * 检查是否能叠子
   */
  canStack(position, gameState, myColor) {
    const myPieces = gameState.players.find(p => p.color === myColor).pieces;
    return myPieces.some(piece => 
      piece.position === position && 
      piece.status === PIECE_STATUS.RUNWAY &&
      !piece.isStacked
    );
  }
  
  /**
   * 检查是否能阻挡对手
   */
  canBlockOpponent(position, gameState, myColor) {
    // 检查对手是否有棋子即将经过这个位置
    return gameState.players.some(player => {
      if (player.color === myColor) return false;
      
      return player.pieces.some(piece => {
        if (piece.status !== PIECE_STATUS.RUNWAY) return false;
        
        // 简单检查：对手棋子在1-6步内会经过这个位置
        for (let steps = 1; steps <= 6; steps++) {
          const futurePos = (piece.position + steps) % BOARD_CONFIG.totalCells;
          if (futurePos === position) return true;
        }
        
        return false;
      });
    });
  }
  
  /**
   * 检查是否能形成保护链
   */
  canFormProtectionChain(position, gameState, myColor) {
    // 简化实现：检查附近是否有己方棋子
    const myPieces = gameState.players.find(p => p.color === myColor).pieces;
    
    return myPieces.some(piece => {
      if (piece.status !== PIECE_STATUS.RUNWAY) return false;
      
      const distance = Math.abs(piece.position - position);
      return distance <= 3 && distance > 0;
    });
  }
  
  /**
   * 评估对手威胁
   */
  evaluateOpponentThreat(position, gameState, myColor) {
    let threat = 0;
    
    gameState.players.forEach(player => {
      if (player.color === myColor) return;
      
      player.pieces.forEach(piece => {
        if (piece.status !== PIECE_STATUS.RUNWAY) return;
        
        // 检查对手是否能在下一轮击落我们
        for (let steps = 1; steps <= 6; steps++) {
          const futurePos = (piece.position + steps) % BOARD_CONFIG.totalCells;
          if (futurePos === position) {
            threat += 20;
            break;
          }
        }
      });
    });
    
    return threat;
  }
  
  /**
   * 检查是否进入终局
   */
  isEndGame(gameState, myColor) {
    const myPlayer = gameState.players.find(p => p.color === myColor);
    const finishedCount = myPlayer.pieces.filter(p => p.status === PIECE_STATUS.FINISHED).length;
    
    return finishedCount >= 2; // 有2个或以上棋子到达终点
  }
  
  /**
   * 评估终局移动
   */
  evaluateEndGameMove(piece, diceValue, gameState) {
    // 终局阶段，优先让剩余棋子快速到达终点
    if (piece.status === PIECE_STATUS.SAFE) {
      return 50; // 安全区域内的棋子优先级很高
    }
    
    if (piece.status === PIECE_STATUS.RUNWAY) {
      const distanceToSafe = this.calculateDistanceToSafeZone(piece);
      return Math.max(0, 30 - distanceToSafe);
    }
    
    return 0;
  }
  
  /**
   * 计算到安全区域的距离
   */
  calculateDistanceToSafeZone(piece) {
    const startPoint = BOARD_CONFIG.startPoints[piece.color];
    let distance;
    
    if (piece.position >= startPoint) {
      distance = piece.position - startPoint;
    } else {
      distance = BOARD_CONFIG.totalCells - startPoint + piece.position;
    }
    
    return BOARD_CONFIG.totalCells - distance;
  }
  
  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 设置AI难度
   */
  setDifficulty(difficulty) {
    this.difficulty = difficulty;
    this.thinkingTime = this.getThinkingTime();
  }
}