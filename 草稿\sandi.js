// 三体世界可视化
let stars = [];
let planet;
let particles = [];
let time = 0;
let timeSpeed = 1;
let camera = {
  angleX: 0,
  angleY: 0,
  distance: 800,
  targetDistance: 800
};
let starField = [];

// 三个恒星的初始参数
let suns = [
  { pos: createVector(0, 0, 0), vel: createVector(0.5, 0, 0.3), mass: 100, color: [255, 100, 100] },
  { pos: createVector(200, 0, 0), vel: createVector(0, 0.8, -0.2), mass: 120, color: [100, 255, 100] },
  { pos: createVector(-100, 150, 0), vel: createVector(-0.3, -0.4, 0.5), mass: 80, color: [100, 150, 255] }
];

// 三体行星
let trisolaris = {
  pos: createVector(50, 50, 0),
  vel: createVector(1, 0, 0),
  trail: [],
  mass: 1
};

function setup() {
  createCanvas(windowWidth, windowHeight, WEBGL);
  
  // 创建星空背景
  for (let i = 0; i < 500; i++) {
    starField.push({
      x: random(-2000, 2000),
      y: random(-2000, 2000),
      z: random(-2000, 2000),
      brightness: random(50, 255)
    });
  }
  
  // 创建粒子效果
  for (let i = 0; i < 100; i++) {
    particles.push({
      pos: createVector(random(-500, 500), random(-500, 500), random(-500, 500)),
      vel: createVector(random(-0.5, 0.5), random(-0.5, 0.5), random(-0.5, 0.5)),
      life: random(100, 255),
      maxLife: random(100, 255)
    });
  }
  
  // 创建UI控制
  createUI();
}

function draw() {
  background(5, 5, 15);
  
  // 更新相机
  updateCamera();
  
  // 绘制星空背景
  drawStarField();
  
  // 更新和绘制三体系统
  updateThreeBodySystem();
  drawSuns();
  
  // 更新和绘制行星
  updatePlanet();
  drawPlanet();
  
  // 绘制粒子效果
  updateParticles();
  drawParticles();
  
  // 绘制UI
  drawUI();
  
  time += timeSpeed;
}

function updateCamera() {
  // 平滑缩放
  camera.distance = lerp(camera.distance, camera.targetDistance, 0.05);
  
  // 设置相机位置
  let camX = camera.distance * cos(camera.angleY) * cos(camera.angleX);
  let camY = camera.distance * sin(camera.angleX);
  let camZ = camera.distance * sin(camera.angleY) * cos(camera.angleX);
  
  camera(camX, camY, camZ, 0, 0, 0, 0, 1, 0);
}

function updateThreeBodySystem() {
  const G = 0.1; // 引力常数
  const dt = 0.01 * timeSpeed;
  
  // 计算三个恒星之间的引力
  for (let i = 0; i < suns.length; i++) {
    let force = createVector(0, 0, 0);
    
    for (let j = 0; j < suns.length; j++) {
      if (i !== j) {
        let r = p5.Vector.sub(suns[j].pos, suns[i].pos);
        let distance = r.mag();
        distance = max(distance, 20); // 避免奇点
        
        let forceMag = G * suns[i].mass * suns[j].mass / (distance * distance);
        r.normalize();
        r.mult(forceMag / suns[i].mass);
        force.add(r);
      }
    }
    
    // 更新速度和位置
    suns[i].vel.add(p5.Vector.mult(force, dt));
    suns[i].pos.add(p5.Vector.mult(suns[i].vel, dt));
  }
}

function updatePlanet() {
  const G = 0.1;
  const dt = 0.01 * timeSpeed;
  
  let force = createVector(0, 0, 0);
  
  // 计算来自三个恒星的引力
  for (let sun of suns) {
    let r = p5.Vector.sub(sun.pos, trisolaris.pos);
    let distance = r.mag();
    distance = max(distance, 10);
    
    let forceMag = G * sun.mass * trisolaris.mass / (distance * distance);
    r.normalize();
    r.mult(forceMag / trisolaris.mass);
    force.add(r);
  }
  
  // 更新行星运动
  trisolaris.vel.add(p5.Vector.mult(force, dt));
  trisolaris.pos.add(p5.Vector.mult(trisolaris.vel, dt));
  
  // 记录轨迹
  trisolaris.trail.push(trisolaris.pos.copy());
  if (trisolaris.trail.length > 1000) {
    trisolaris.trail.shift();
  }
}

function drawStarField() {
  push();
  noFill();
  for (let star of starField) {
    push();
    translate(star.x, star.y, star.z);
    fill(255, star.brightness);
    noStroke();
    sphere(1);
    pop();
  }
  pop();
}

function drawSuns() {
  for (let i = 0; i < suns.length; i++) {
    let sun = suns[i];
    
    push();
    translate(sun.pos.x, sun.pos.y, sun.pos.z);
    
    // 恒星光晕效果
    for (let j = 0; j < 3; j++) {
      push();
      fill(sun.color[0], sun.color[1], sun.color[2], 50 - j * 15);
      noStroke();
      sphere(15 + j * 8);
      pop();
    }
    
    // 恒星核心
    push();
    fill(sun.color[0], sun.color[1], sun.color[2]);
    noStroke();
    sphere(12);
    pop();
    
    // 恒星耀斑效果
    push();
    stroke(sun.color[0], sun.color[1], sun.color[2], 100);
    strokeWeight(2);
    for (let k = 0; k < 8; k++) {
      let angle = (time * 0.02 + k * PI / 4);
      let x1 = cos(angle) * 20;
      let y1 = sin(angle) * 20;
      let x2 = cos(angle) * 35;
      let y2 = sin(angle) * 35;
      line(x1, y1, 0, x2, y2, 0);
    }
    pop();
    
    pop();
  }
}

function drawPlanet() {
  // 绘制轨迹
  if (trisolaris.trail.length > 1) {
    push();
    stroke(100, 200, 255, 150);
    strokeWeight(1);
    noFill();
    beginShape();
    for (let i = 0; i < trisolaris.trail.length; i++) {
      let alpha = map(i, 0, trisolaris.trail.length - 1, 0, 255);
      stroke(100, 200, 255, alpha);
      let pos = trisolaris.trail[i];
      vertex(pos.x, pos.y, pos.z);
    }
    endShape();
    pop();
  }
  
  // 绘制行星
  push();
  translate(trisolaris.pos.x, trisolaris.pos.y, trisolaris.pos.z);
  
  // 行星大气层
  push();
  fill(50, 150, 255, 80);
  noStroke();
  sphere(8);
  pop();
  
  // 行星表面
  push();
  fill(80, 120, 200);
  noStroke();
  sphere(5);
  pop();
  
  pop();
}

function updateParticles() {
  for (let particle of particles) {
    particle.pos.add(particle.vel);
    particle.life -= 1;
    
    if (particle.life <= 0) {
      particle.pos = createVector(random(-500, 500), random(-500, 500), random(-500, 500));
      particle.vel = createVector(random(-0.5, 0.5), random(-0.5, 0.5), random(-0.5, 0.5));
      particle.life = particle.maxLife;
    }
  }
}

function drawParticles() {
  push();
  for (let particle of particles) {
    push();
    translate(particle.pos.x, particle.pos.y, particle.pos.z);
    let alpha = map(particle.life, 0, particle.maxLife, 0, 100);
    fill(150, 200, 255, alpha);
    noStroke();
    sphere(0.5);
    pop();
  }
  pop();
}

function drawUI() {
  // 切换到2D模式绘制UI
  camera();
  hint(DISABLE_DEPTH_TEST);
  
  // 时间控制面板
  push();
  fill(0, 0, 0, 150);
  rect(20, 20, 200, 80);
  
  fill(255);
  textSize(14);
  text("时间流速: " + timeSpeed.toFixed(1), 30, 40);
  text("按键控制:", 30, 60);
  text("↑↓: 调整时间流速", 30, 75);
  text("鼠标: 旋转视角", 30, 90);
  pop();
  
  hint(ENABLE_DEPTH_TEST);
}

function createUI() {
  // 这里可以添加更多UI元素
}

// 鼠标控制
function mouseDragged() {
  if (mouseX > 0 && mouseX < width && mouseY > 0 && mouseY < height) {
    camera.angleY += (mouseX - pmouseX) * 0.01;
    camera.angleX -= (mouseY - pmouseY) * 0.01;
    camera.angleX = constrain(camera.angleX, -PI/2, PI/2);
  }
}

// 鼠标滚轮缩放
function mouseWheel(event) {
  camera.targetDistance += event.delta * 10;
  camera.targetDistance = constrain(camera.targetDistance, 100, 2000);
  return false;
}

// 键盘控制
function keyPressed() {
  if (keyCode === UP_ARROW) {
    timeSpeed = min(timeSpeed + 0.1, 5);
  } else if (keyCode === DOWN_ARROW) {
    timeSpeed = max(timeSpeed - 0.1, 0);
  } else if (key === 'r' || key === 'R') {
    // 重置场景
    resetScene();
  }
}

function resetScene() {
  // 重置恒星位置
  suns[0].pos = createVector(0, 0, 0);
  suns[0].vel = createVector(0.5, 0, 0.3);
  suns[1].pos = createVector(200, 0, 0);
  suns[1].vel = createVector(0, 0.8, -0.2);
  suns[2].pos = createVector(-100, 150, 0);
  suns[2].vel = createVector(-0.3, -0.4, 0.5);
  
  // 重置行星
  trisolaris.pos = createVector(50, 50, 0);
  trisolaris.vel = createVector(1, 0, 0);
  trisolaris.trail = [];
  
  time = 0;
}

function windowResized() {
  resizeCanvas(windowWidth, windowHeight);
}