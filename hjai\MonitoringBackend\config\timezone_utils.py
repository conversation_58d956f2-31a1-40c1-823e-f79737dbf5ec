"""
现代化时区处理工具模块

这是新的推荐时区处理模块，提供更简洁、可靠的时区处理功能。
逐步替换旧的 config/timezone.py 模块。

设计原则：
1. 内部统一使用UTC时间
2. 边界转换：仅在输入/输出时转换时区
3. 使用现代的zoneinfo而不是pytz
4. 提供清晰的API和错误处理
"""

from datetime import datetime, timezone
from zoneinfo import ZoneInfo
from typing import Optional, Union
import json
import logging

logger = logging.getLogger(__name__)

# 时区常量
UTC = timezone.utc
SHANGHAI_TZ = ZoneInfo("Asia/Shanghai")

class TimezoneMixin:
    """时区处理混入类，可以被模型类继承"""
    
    def get_utc_timestamp(self) -> datetime:
        """获取UTC时间戳"""
        return datetime.now(UTC)
    
    def format_for_display(self, dt: datetime) -> str:
        """格式化时间用于显示"""
        if dt is None:
            return None
        return TZ.to_display_format(dt)

class TZ:
    """时区处理工具类 - 简洁的API"""

    # 类常量
    UTC = UTC
    SHANGHAI_TZ = SHANGHAI_TZ

    @staticmethod
    def now_utc() -> datetime:
        """获取当前UTC时间（推荐用于内部计算）"""
        return datetime.now(UTC)
    
    @staticmethod
    def now_shanghai() -> datetime:
        """获取当前上海时间（用于显示）"""
        return datetime.now(SHANGHAI_TZ)
    
    @staticmethod
    def to_utc(dt: datetime, source_tz: Optional[ZoneInfo] = None) -> datetime:
        """
        转换为UTC时间
        
        Args:
            dt: 要转换的时间
            source_tz: 源时区，如果dt没有时区信息则使用此时区
        """
        if dt is None:
            return None
            
        if dt.tzinfo is None:
            if source_tz is None:
                source_tz = SHANGHAI_TZ  # 默认假设为上海时区
            dt = dt.replace(tzinfo=source_tz)
        
        return dt.astimezone(UTC)
    
    @staticmethod
    def to_shanghai(dt: datetime, source_tz: Optional[ZoneInfo] = None) -> datetime:
        """
        转换为上海时区时间
        
        Args:
            dt: 要转换的时间
            source_tz: 源时区，如果dt没有时区信息则使用此时区
        """
        if dt is None:
            return None
            
        if dt.tzinfo is None:
            if source_tz is None:
                source_tz = UTC  # 默认假设为UTC时间
            dt = dt.replace(tzinfo=source_tz)
        
        return dt.astimezone(SHANGHAI_TZ)
    
    @staticmethod
    def to_display_format(dt: datetime, source_tz: Optional[ZoneInfo] = None) -> str:
        """
        转换为显示格式（上海时区ISO字符串）

        Args:
            dt: 要转换的时间
            source_tz: 源时区，如果dt没有时区信息则使用此时区

        Returns:
            str: 格式如 "2023-12-25T14:30:00+08:00"
        """
        if dt is None:
            return None

        # 如果dt已经有时区信息，直接转换到上海时区
        if dt.tzinfo is not None:
            shanghai_dt = dt.astimezone(SHANGHAI_TZ)
        else:
            # 如果没有时区信息，使用source_tz参数
            shanghai_dt = TZ.to_shanghai(dt, source_tz)

        return shanghai_dt.isoformat()
    
    @staticmethod
    def from_input(dt_input: Union[str, datetime], input_tz: Optional[ZoneInfo] = None) -> datetime:
        """
        解析输入时间为UTC时间
        
        Args:
            dt_input: 时间输入（字符串或datetime）
            input_tz: 输入时区，如果输入没有时区信息则使用此时区
            
        Returns:
            datetime: UTC时间
        """
        if dt_input is None:
            return None
            
        if isinstance(dt_input, str):
            try:
                # 尝试解析ISO格式
                if '+' in dt_input or dt_input.endswith('Z'):
                    dt = datetime.fromisoformat(dt_input.replace('Z', '+00:00'))
                else:
                    # 没有时区信息
                    dt = datetime.fromisoformat(dt_input)
                    if input_tz is None:
                        input_tz = SHANGHAI_TZ  # 默认假设为上海时区输入
                    dt = dt.replace(tzinfo=input_tz)
            except ValueError as e:
                logger.error(f"解析时间字符串失败: {dt_input}, 错误: {e}")
                raise ValueError(f"无效的时间格式: {dt_input}")
        else:
            dt = dt_input
        
        return TZ.to_utc(dt, input_tz)
    
    @staticmethod
    def safe_localize(dt: datetime, tz: ZoneInfo = SHANGHAI_TZ) -> datetime:
        """
        安全地为naive datetime添加时区信息
        
        Args:
            dt: datetime对象
            tz: 要添加的时区
            
        Returns:
            datetime: 带时区信息的datetime
        """
        if dt is None:
            return None
            
        if dt.tzinfo is not None:
            return dt
            
        return dt.replace(tzinfo=tz)

class ModernJSONEncoder(json.JSONEncoder):
    """现代化的JSON编码器，处理时区转换"""
    
    def default(self, obj):
        if isinstance(obj, datetime):
            return TZ.to_display_format(obj)
        return super().default(obj)

# 便捷函数
def utc_now() -> datetime:
    """获取当前UTC时间"""
    return TZ.now_utc()

def shanghai_now() -> datetime:
    """获取当前上海时间"""
    return TZ.now_shanghai()

def to_display(dt: datetime) -> str:
    """转换为显示格式"""
    return TZ.to_display_format(dt)

def from_user_input(dt_input: Union[str, datetime]) -> datetime:
    """从用户输入解析时间"""
    return TZ.from_input(dt_input)

# 数据库相关工具
class DatabaseTimeUtils:
    """数据库时间处理工具"""
    
    @staticmethod
    def prepare_for_save(dt: Optional[datetime]) -> Optional[datetime]:
        """准备时间用于数据库保存（转换为UTC）"""
        if dt is None:
            return None
        return TZ.to_utc(dt)
    
    @staticmethod
    def prepare_for_display(dt: Optional[datetime]) -> Optional[str]:
        """准备从数据库读取的时间用于显示"""
        if dt is None:
            return None
        return TZ.to_display_format(dt, UTC)  # 假设数据库存储的是UTC时间

# 验证函数
def verify_timezone_setup():
    """验证时区设置"""
    utc_time = TZ.now_utc()
    shanghai_time = TZ.now_shanghai()
    
    print(f"UTC时间: {utc_time}")
    print(f"上海时间: {shanghai_time}")
    print(f"显示格式: {TZ.to_display_format(utc_time)}")
    
    # 测试转换
    test_utc = TZ.to_utc(shanghai_time.replace(tzinfo=None), SHANGHAI_TZ)
    print(f"转换测试: {test_utc}")
    
    print("✅ 现代化时区工具已就绪")

if __name__ == "__main__":
    verify_timezone_setup()
