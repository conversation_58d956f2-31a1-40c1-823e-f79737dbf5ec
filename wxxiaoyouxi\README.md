# 飞行棋游戏 🎮

一个基于HTML5 Canvas的经典飞行棋游戏，支持单机AI对战和本地多人游戏。

## 🎯 游戏特色

- ✅ **完整规则实现** - 严格按照传统飞行棋规则开发
- 🤖 **智能AI对手** - 三种难度的AI，提供不同挑战
- 👥 **多人对战** - 支持2-4人本地对战
- 🎨 **精美界面** - 现代化UI设计，视觉效果出色
- 🎬 **流畅动画** - 棋子移动、骰子投掷等动画效果
- 🔊 **音效支持** - 丰富的游戏音效
- 📱 **移动优化** - 完美适配手机和平板设备
- ⚡ **高性能** - 优化的渲染引擎，流畅运行

## 🎮 游戏规则

### 基本规则
- **玩家数量**: 2-4人
- **棋子数量**: 每人4枚棋子
- **获胜条件**: 率先将所有棋子送到终点

### 详细规则
1. **起飞**: 投出6点可以将基地中的棋子起飞到起点
2. **移动**: 根据骰子点数移动棋子
3. **再投**: 投出6点可以再投一次骰子
4. **击落**: 棋子可以击落对手的棋子，被击落的棋子返回基地
5. **叠子**: 同色棋子可以叠在一起，形成保护
6. **跳跃**: 踩到同色格子可以跳跃到下一个同色格子
7. **飞行**: 特定格子可以飞行到对应位置
8. **安全区**: 进入安全跑道后不会被击落
9. **惩罚**: 连续3次投出6点将跳过本回合

## 🚀 快速开始

### 方法一：直接运行（推荐）

1. **下载项目**
   ```bash
   git clone <项目地址>
   cd wxxiaoyouxi
   ```

2. **启动服务器**
   ```bash
   node server.js
   ```

3. **打开游戏**
   在浏览器中访问：`http://localhost:3000`

### 方法二：使用npm

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动游戏**
   ```bash
   npm start
   ```

## 🎯 操作说明

### 鼠标/触屏操作
- **点击骰子** - 投掷骰子
- **点击棋子** - 选择要移动的棋子
- **点击空白** - 取消选择

### 键盘快捷键
- **空格键** - 投掷骰子
- **ESC键** - 显示退出确认

### 游戏界面
- **状态栏** - 显示当前玩家和游戏状态
- **计时器** - 显示回合剩余时间
- **玩家面板** - 显示各玩家信息和进度
- **控制按钮** - 全屏、音效、帮助等功能

## 📁 项目结构

```
wxxiaoyouxi/
├── index.html              # 游戏主页面
├── server.js               # 开发服务器
├── package.json            # 项目配置
├── README.md              # 项目说明
├── js/                    # JavaScript源码
│   ├── main.js            # 游戏主入口
│   ├── config/            # 配置文件
│   │   ├── constants.js   # 游戏常量
│   │   └── gameConfig.js  # 游戏配置
│   ├── engine/            # 游戏引擎
│   │   ├── gameEngine.js  # 游戏引擎核心
│   │   ├── sceneManager.js # 场景管理器
│   │   ├── audioManager.js # 音频管理器
│   │   └── inputManager.js # 输入管理器
│   ├── scenes/            # 游戏场景
│   │   ├── base.js        # 基础场景类
│   │   ├── menu.js        # 主菜单场景
│   │   ├── game.js        # 游戏场景
│   │   ├── settings.js    # 设置场景
│   │   └── result.js      # 结果场景
│   ├── game/              # 游戏逻辑
│   │   ├── board.js       # 棋盘类
│   │   ├── piece.js       # 棋子类
│   │   ├── dice.js        # 骰子类
│   │   ├── rules.js       # 游戏规则
│   │   └── aiPlayer.js    # AI玩家
│   ├── utils/             # 工具类
│   │   ├── animationManager.js # 动画管理器
│   │   ├── mathUtils.js   # 数学工具
│   │   └── colorUtils.js  # 颜色工具
│   ├── ui/                # UI组件
│   │   ├── button.js      # 按钮组件
│   │   └── dialog.js      # 对话框组件
│   ├── databus.js         # 数据总线
│   └── render.js          # 渲染初始化
└── assets/                # 游戏资源
    ├── images/            # 图片资源
    └── sounds/            # 音频资源
```

## 🔧 技术架构

### 前端技术
- **HTML5 Canvas** - 游戏渲染
- **ES6+ JavaScript** - 现代JavaScript特性
- **模块化设计** - ES6模块系统
- **面向对象** - 类和继承
- **事件驱动** - 游戏事件系统

### 核心模块
- **游戏引擎** - 统一的游戏循环和状态管理
- **场景管理** - 灵活的场景切换系统
- **动画系统** - 高性能的动画引擎
- **音频系统** - 完整的音效管理
- **输入系统** - 统一的输入处理

### 设计模式
- **单例模式** - 全局管理器
- **观察者模式** - 事件系统
- **状态模式** - 游戏状态管理
- **工厂模式** - 对象创建
- **策略模式** - AI决策

## 🎨 自定义配置

### 游戏设置
可以在 `js/config/gameConfig.js` 中修改：
- 棋盘尺寸和布局
- 动画速度和效果
- AI难度参数
- 音效设置
- 界面主题

### 添加新功能
1. **新场景** - 继承 `BaseScene` 类
2. **新组件** - 实现标准接口
3. **新动画** - 使用 `AnimationManager`
4. **新音效** - 通过 `AudioManager` 管理

## 🐛 常见问题

### Q: 游戏无法启动？
A: 请确保：
- Node.js版本 >= 12.0.0
- 端口3000未被占用
- 浏览器支持ES6模块

### Q: 音效无法播放？
A: 现代浏览器需要用户交互后才能播放音频，请先点击页面任意位置。

### Q: 移动设备上操作不灵敏？
A: 游戏已针对触屏优化，如有问题请检查浏览器兼容性。

### Q: 如何修改游戏规则？
A: 在 `js/game/rules.js` 中修改相应的规则逻辑。

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的飞行棋规则实现
- ✅ 单机AI对战功能
- ✅ 本地多人游戏
- ✅ 精美的游戏界面
- ✅ 流畅的动画效果
- ✅ 音效系统
- ✅ 移动端适配

### 计划功能
- 🔄 在线多人对战
- 🔄 游戏回放功能
- 🔄 成就系统
- 🔄 排行榜
- 🔄 自定义皮肤
- 🔄 更多游戏模式

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢所有贡献者的努力
- 感谢开源社区的支持
- 特别感谢经典飞行棋游戏的启发

## 📞 联系方式

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：<EMAIL>

---

**享受游戏，祝你好运！** 🎲✨
