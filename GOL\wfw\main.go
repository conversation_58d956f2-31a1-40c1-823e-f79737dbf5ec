package main

import (
	"context"
	"fmt"
	"log"
	"net"

	"calculator/pb" // 根据你的项目路径调整
	"google.golang.org/grpc"
)

// 服务器结构体
type calculatorServer struct {
	pb.UnimplementedCalculatorServer
}

// Add 实现 Add 方法
func (s *calculatorServer) Add(ctx context.Context, req *pb.AddRequest) (*pb.AddResponse, error) {
	result := req.Num1 + req.Num2
	log.Printf("收到计算请求: %.2f + %.2f = %.2f", req.Num1, req.Num2, result)

	return &pb.AddResponse{
		Result: result,
	}, nil
}

func main() {
	// 监听端口
	listener, err := net.Listen("tcp", ":50051")
	if err != nil {
		log.Fatalf("监听失败: %v", err)
	}

	// 创建 gRPC 服务器
	s := grpc.NewServer()

	// 注册计算器服务
	pb.RegisterCalculatorServer(s, &calculatorServer{})

	fmt.Println("计算器服务运行在 :50051")

	// 启动服务器
	if err := s.Serve(listener); err != nil {
		log.Fatalf("服务启动失败: %v", err)
	}
}
