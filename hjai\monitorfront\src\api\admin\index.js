import request from '@/utils/request'

/**
 * 获取IP列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求Promise
 */
export function getAdminIpList (params) {
  return request({
    url: '/admin/ip/list',
    method: 'get',
    params
  })
}

/**
 * 添加IP
 * @param {Object} data - IP数据
 * @returns {Promise} - 请求Promise
 */
export function addIp (data) {
  return request({
    url: '/admin/ip/add',
    method: 'post',
    data
  })
}

/**
 * 删除IP
 * @param {String} ip - 要删除的IP
 * @returns {Promise} - 请求Promise
 */
export function deleteIp (ip) {
  return request({
    url: '/admin/ip/delete',
    method: 'post',
    data: { ip }
  })
}

/**
 * 获取监控项列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求Promise
 */
export function getMonitorItems (params) {
  return request({
    url: '/admin/monitor/items',
    method: 'get',
    params
  })
}

/**
 * 添加监控项
 * @param {Object} data - 监控项数据
 * @returns {Promise} - 请求Promise
 */
export function addMonitorItem (data) {
  return request({
    url: '/admin/monitor/add',
    method: 'post',
    data
  })
}

/**
 * 删除监控项
 * @param {String} id - 监控项ID
 * @returns {Promise} - 请求Promise
 */
export function deleteMonitorItem (id) {
  return request({
    url: '/admin/monitor/delete',
    method: 'post',
    data: { id }
  })
}

/**
 * 获取IP关联的监控项
 * @param {String} ip - IP地址
 * @returns {Promise} - 请求Promise
 */
export function getIpMonitorRelations (ip) {
  return request({
    url: '/admin/relation/list',
    method: 'get',
    params: { ip }
  })
}

/**
 * 绑定监控项到IP
 * @param {Object} data - 绑定数据
 * @returns {Promise} - 请求Promise
 */
export function bindMonitorToIp (data) {
  return request({
    url: '/admin/relation/bind',
    method: 'post',
    data
  })
}

/**
 * 解绑IP的监控项
 * @param {Object} data - 解绑数据
 * @returns {Promise} - 请求Promise
 */
export function unbindMonitor (data) {
  return request({
    url: '/admin/relation/unbind',
    method: 'post',
    data
  })
}
