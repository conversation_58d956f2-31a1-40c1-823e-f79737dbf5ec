#创建vlan
vlan 32

#interface vlan
DUT2 (config)# interface vlan 32
DUT2 (config-if)# ip address ************/24

DUT2 (config)# interface vlan 32
DUT2 (config-if)# ip address ************/24


#vrrp

Switch# configure terminal
Switch(config)# router vrrp 32
Switch(config-router)#virtual-ip  ************
Switch(config-router)#interface  eth-0-1
Switch(config-router)# preempt-mode true
Switch(config-router)# advertisement-interval 5
Switch (config-router)# bfd ************
Switch(config-router)# enable


Switch# configure terminal
Switch(config)# router vrrp 32
Switch(config-router)#virtual-ip  ************
Switch(config-router)#interface  eth-0-1
Switch(config-router)# preempt-mode true
Switch(config-router)# advertisement-interval 5
Switch (config-router)# bfd ************
Switch(config-router)# enable