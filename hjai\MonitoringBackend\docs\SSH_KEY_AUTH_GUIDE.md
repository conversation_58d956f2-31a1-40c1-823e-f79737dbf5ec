# SSH密钥认证功能使用指南

## 功能概述

监控程序现已支持SSH密钥认证，可以替代传统的用户名密码认证方式。系统会根据配置自动选择合适的认证方式：

- **SSH密钥认证**：当密码字段为空或明确启用SSH密钥认证时使用
- **密码认证**：当提供密码且未启用SSH密钥认证时使用
- **自动降级**：SSH密钥认证失败时，如果有密码配置，会自动尝试密码认证

## 实现的功能

### 1. 数据库模型更新
- `password` 字段现在允许为空（NULL）
- 新增 `use_ssh_key` 字段：是否使用SSH密钥认证
- 新增 `ssh_key_path` 字段：SSH私钥文件路径（可选）

### 2. SSH配置管理
- 自动检测常见的SSH密钥文件路径
- 支持自定义SSH密钥文件路径
- 验证密钥文件的有效性和权限

### 3. 智能认证方式选择
- 密码为空时自动使用SSH密钥认证
- 支持多种SSH密钥类型（RSA、Ed25519、ECDSA、DSA）
- 认证失败时的降级处理

## 部署步骤

### 1. 执行数据库迁移

```bash
# 执行迁移脚本
python scripts/migrate_ssh_support.py

# 如果需要回滚
python scripts/migrate_ssh_support.py rollback
```

### 2. 配置SSH密钥

#### 生成SSH密钥对（如果还没有）
```bash
# 生成RSA密钥
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa

# 或生成Ed25519密钥（推荐）
ssh-keygen -t ed25519 -f ~/.ssh/id_ed25519
```

#### 将公钥复制到目标服务器
```bash
# 复制公钥到远程服务器
ssh-copy-id -i ~/.ssh/id_rsa.pub username@server_ip

# 或手动添加到远程服务器的 ~/.ssh/authorized_keys
```

### 3. 验证SSH密钥连接
```bash
# 测试SSH密钥连接
ssh -i ~/.ssh/id_rsa username@server_ip

# 确保可以无密码登录
```

## 使用方法

### 1. 通过API添加服务器

#### 使用SSH密钥认证
```json
{
    "ip": "*************",
    "user": "username",
    "password": null,
    "use_ssh_key": true,
    "ssh_key_path": null
}
```

#### 使用自定义SSH密钥路径
```json
{
    "ip": "*************",
    "user": "username",
    "password": null,
    "use_ssh_key": true,
    "ssh_key_path": "/path/to/custom/key"
}
```

#### 使用密码认证（保持兼容）
```json
{
    "ip": "*************",
    "user": "username",
    "password": "your_password",
    "use_ssh_key": false
}
```

#### 自动选择认证方式（密码为空时使用SSH密钥）
```json
{
    "ip": "*************",
    "user": "username",
    "password": "",
    "use_ssh_key": false
}
```

### 2. 认证方式优先级

1. **明确启用SSH密钥** (`use_ssh_key: true`)：优先使用SSH密钥认证
2. **密码为空**：自动使用SSH密钥认证
3. **有密码且未启用SSH密钥**：使用密码认证
4. **降级处理**：SSH密钥认证失败且有密码时，尝试密码认证

## 测试验证

### 运行测试脚本
```bash
# 运行完整测试
python test/test_ssh_key_auth.py

# 测试包括：
# - SSH配置检测
# - 数据库模型验证
# - SSH连接管理器测试
# - 真实连接测试（需要手动配置）
```

### 检查SSH密钥配置
```bash
# 检查系统中的SSH密钥
ls -la ~/.ssh/

# 验证密钥权限（应该是600）
ls -l ~/.ssh/id_rsa

# 测试SSH连接
ssh -v username@server_ip
```

## 故障排除

### 1. SSH密钥认证失败
- 检查私钥文件是否存在且权限正确（600）
- 确认公钥已正确添加到远程服务器的 `~/.ssh/authorized_keys`
- 检查远程服务器的SSH配置是否允许密钥认证

### 2. 权限问题
```bash
# 修复SSH密钥权限
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
chmod 700 ~/.ssh
```

### 3. 远程服务器配置
确保远程服务器的 `/etc/ssh/sshd_config` 包含：
```
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
```

### 4. 查看日志
监控程序会记录详细的连接日志，包括：
- 使用的认证方式
- 尝试的密钥文件路径
- 连接失败的具体原因

## 安全建议

1. **密钥权限**：确保私钥文件权限为600，只有所有者可读
2. **密钥类型**：推荐使用Ed25519密钥，安全性更高
3. **密钥管理**：定期轮换SSH密钥
4. **访问控制**：在远程服务器上限制SSH访问来源IP
5. **监控日志**：定期检查SSH连接日志，发现异常访问

## 兼容性说明

- **向后兼容**：现有的密码认证方式完全保持兼容
- **平滑迁移**：可以逐步将服务器从密码认证迁移到SSH密钥认证
- **混合使用**：同一系统中可以同时使用两种认证方式

## 配置文件

### SSH配置 (`config/ssh_config.py`)
- 默认SSH密钥路径配置
- 连接超时和重试设置
- 密钥文件验证逻辑

### 数据库配置
- 新增字段的默认值设置
- 认证方式的自动判断逻辑

通过以上配置，监控程序现在具备了完整的SSH密钥认证能力，提供了更安全、更便捷的服务器连接方式。
