#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IP探测脚本 - 异步高性能版本
探测***********-*************之间的IP地址
使用asyncio实现异步并发，提高探测效率
"""

import asyncio
import time
import platform
import socket
from concurrent.futures import ThreadPoolExecutor

# 配置参数
IP_PREFIXES = [ "192.168.10"]  # 同时探测两个网段
START_IP = 1
END_IP = 254
TIMEOUT = 1  # 超时时间(秒)
MAX_CONCURRENT = 100  # 最大并发数
CHECK_PORTS = [80, 443, 22, 8080]  # 常用端口检测列表

# 根据操作系统选择ping命令
if platform.system().lower() == "windows":
    ping_cmd = "ping -n 1 -w 1000 {}"
else:
    ping_cmd = "ping -c 1 -W 1 {}"


async def ping_ip(ip):
    """
    异步ping IP地址
    """
    cmd = ping_cmd.format(ip)
    try:
        # 创建子进程执行ping命令
        proc = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 设置超时
        try:
            stdout, stderr = await asyncio.wait_for(proc.communicate(), timeout=TIMEOUT)
            if proc.returncode == 0:
                return ip, True  # IP可达
            else:
                return ip, False  # IP不可达
        except asyncio.TimeoutError:
            # 超时处理
            try:
                proc.kill()  # 尝试终止进程
            except Exception:
                pass
            return ip, False  # 超时视为不可达
    except Exception as e:
        return ip, False  # 出错视为不可达


async def tcp_check(ip, port=80):
    """
    异步TCP连接检测
    作为备用方法，有些环境可能禁止ICMP但允许TCP
    """
    try:
        conn = asyncio.open_connection(ip, port)
        reader, writer = await asyncio.wait_for(conn, timeout=TIMEOUT/2)  # 减少TCP检测超时时间
        writer.close()
        await writer.wait_closed()
        return True
    except Exception:
        return False


async def check_ip_available(ip, prefix):
    """
    检查IP是否可用，先尝试ping，如果失败再尝试TCP连接
    """
    ip_addr = f"{prefix}{ip}"
    ping_result = await ping_ip(ip_addr)
    
    if ping_result[1]:
        return ip_addr, True
    
    for port in CHECK_PORTS:
        tcp_result = await tcp_check(ip_addr, port)
        if tcp_result:
            return ip_addr, True
    
    return ip_addr, False


async def scan_ip_range():
    sem = asyncio.Semaphore(MAX_CONCURRENT)
    
    async def _wrapped_check(ip, prefix):
        async with sem:
            return await check_ip_available(ip, prefix)
    
    # 为每个网段创建任务
    all_tasks = []
    for prefix in IP_PREFIXES:
        prefix_tasks = [_wrapped_check(i, prefix) for i in range(START_IP, END_IP + 1)]
        all_tasks.extend(prefix_tasks)
    
    # 执行所有任务并等待结果
    results = await asyncio.gather(*all_tasks)
    
    # 分离结果
    available_ips = [ip for ip, status in results if status]
    unavailable_ips = [ip for ip, status in results if not status]
    
    # 添加本机地址
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        if local_ip not in available_ips:
            available_ips.append(local_ip)
    except Exception as e:
        print(f"获取本机IP时出错: {e}")
    
    return available_ips, unavailable_ips


async def main():
    print("开始探测IP范围:")
    for prefix in IP_PREFIXES:
        print(f"  {prefix}{START_IP} - {prefix}{END_IP}")
    print(f"最大并发数: {MAX_CONCURRENT}")
    print(f"检测端口: {CHECK_PORTS}")
    print("正在扫描，请稍候...\n")
    
    start_time = time.time()
    
    available_ips, unavailable_ips = await scan_ip_range()
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 输出结果
    print("\n扫描完成!")
    print(f"耗时: {duration:.2f}秒")
    print(f"扫描IP数量: {END_IP - START_IP + 1}")
    print(f"可达IP数量: {len(available_ips)}")
    
    if available_ips:
        print("\n可达的IP地址:")
        for ip in sorted(available_ips, key=lambda x: [int(i) for i in x.split('.')]):
            print(f"  {ip}")
    
    # 计算性能指标
    ips_per_second = (END_IP - START_IP + 1) / duration if duration > 0 else 0
    print(f"\n性能: {ips_per_second:.2f} IP/秒")


if __name__ == "__main__":
    # 在Windows上需要使用不同的事件循环策略
    if platform.system().lower() == "windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 运行主函数
    asyncio.run(main())