<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElCard, ElRow, ElCol, ElStatistic, ElProgress, ElIcon, ElAlert } from 'element-plus'
import { Monitor, Platform, Warning, Check, Close } from '@element-plus/icons-vue'
import { monitorAPI, type MonitorStatus, type MonitorStats } from '@/api/monitor'
import type { VChart } from 'vue-echarts'

const loading = ref(true)
const monitorStatus = ref<MonitorStatus | null>(null)
const monitorStats = ref<MonitorStats | null>(null)

const fetchDashboardData = async () => {
  try {
    loading.value = true
    const [statusRes, statsRes] = await Promise.all([
      monitorAPI.getMonitorStatus(),
      monitorAPI.getMonitorStats()
    ])
    monitorStatus.value = statusRes
    monitorStats.value = statsRes
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchDashboardData()
  // Auto refresh every 30 seconds
  setInterval(fetchDashboardData, 30000)
})
</script>

<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>监控概览</h2>
      <p>实时系统状态和性能指标</p>
    </div>

    <!-- System Health Alert -->
    <ElAlert
      v-if="monitorStats && !monitorStats.system_health.scheduler_running"
      title="系统警告"
      description="监控调度器未运行，请检查系统状态"
      type="warning"
      :closable="false"
      show-icon
      class="alert-card"
    />

    <!-- Key Metrics -->
    <ElRow :gutter="24" class="metrics-row">
      <ElCol :xs="12" :sm="6">
        <ElCard class="metric-card">
          <div class="metric-content">
            <div class="metric-icon server">
              <ElIcon size="24"><Platform /></ElIcon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ monitorStatus?.global_stats.total_servers || 0 }}</div>
              <div class="metric-label">总服务器数</div>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <ElCol :xs="12" :sm="6">
        <ElCard class="metric-card">
          <div class="metric-content">
            <div class="metric-icon online">
              <ElIcon size="24"><Check /></ElIcon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ monitorStatus?.global_stats.online_servers || 0 }}</div>
              <div class="metric-label">在线服务器</div>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <ElCol :xs="12" :sm="6">
        <ElCard class="metric-card">
          <div class="metric-content">
            <div class="metric-icon monitor">
              <ElIcon size="24"><Monitor /></ElIcon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ monitorStatus?.global_stats.total_monitor_items || 0 }}</div>
              <div class="metric-label">监控项总数</div>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <ElCol :xs="12" :sm="6">
        <ElCard class="metric-card">
          <div class="metric-content">
            <div class="metric-icon success">
              <ElIcon size="24"><Check /></ElIcon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ (monitorStatus?.global_stats.overall_success_rate || 0).toFixed(1) }}%</div>
              <div class="metric-label">成功率</div>
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- Status Cards -->
    <ElRow :gutter="24" class="status-row">
      <ElCol :xs="24" :md="12">
        <ElCard class="status-card">
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <ElIcon class="header-icon"><Monitor /></ElIcon>
            </div>
          </template>
          
          <div class="status-grid">
            <div class="status-item">
              <div class="status-label">调度器状态</div>
              <div class="status-value">
                <span :class="['status-badge', monitorStats?.scheduler_stats.running ? 'running' : 'stopped']">
                  {{ monitorStats?.scheduler_stats.running ? '运行中' : '已停止' }}
                </span>
              </div>
            </div>
            
            <div class="status-item">
              <div class="status-label">成功周期</div>
              <div class="status-value">{{ monitorStats?.scheduler_stats.successful_cycles || 0 }}</div>
            </div>
            
            <div class="status-item">
              <div class="status-label">失败周期</div>
              <div class="status-value error">{{ monitorStats?.scheduler_stats.failed_cycles || 0 }}</div>
            </div>
            
            <div class="status-item">
              <div class="status-label">平均周期时间</div>
              <div class="status-value">{{ (monitorStats?.scheduler_stats.average_cycle_time || 0).toFixed(1) }}s</div>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <ElCol :xs="24" :md="12">
        <ElCard class="status-card">
          <template #header>
            <div class="card-header">
              <span>监控项状态</span>
              <ElIcon class="header-icon"><Platform /></ElIcon>
            </div>
          </template>
          
          <div class="monitor-status">
            <div class="status-progress">
              <div class="progress-item">
                <div class="progress-label">
                  <span>健康监控项</span>
                  <span class="progress-count">{{ monitorStatus?.global_stats.healthy_items || 0 }}</span>
                </div>
                <ElProgress 
                  :percentage="monitorStatus?.global_stats.total_monitor_items ? 
                    (monitorStatus.global_stats.healthy_items / monitorStatus.global_stats.total_monitor_items * 100) : 0"
                  color="#10b981"
                  :show-text="false"
                />
              </div>
              
              <div class="progress-item">
                <div class="progress-label">
                  <span>警告监控项</span>
                  <span class="progress-count">{{ monitorStatus?.global_stats.warning_items || 0 }}</span>
                </div>
                <ElProgress 
                  :percentage="monitorStatus?.global_stats.total_monitor_items ? 
                    (monitorStatus.global_stats.warning_items / monitorStatus.global_stats.total_monitor_items * 100) : 0"
                  color="#f59e0b"
                  :show-text="false"
                />
              </div>
              
              <div class="progress-item">
                <div class="progress-label">
                  <span>错误监控项</span>
                  <span class="progress-count">{{ monitorStatus?.global_stats.error_items || 0 }}</span>
                </div>
                <ElProgress 
                  :percentage="monitorStatus?.global_stats.total_monitor_items ? 
                    (monitorStatus.global_stats.error_items / monitorStatus.global_stats.total_monitor_items * 100) : 0"
                  color="#ef4444"
                  :show-text="false"
                />
              </div>
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- Problematic Servers -->
    <ElCard v-if="monitorStatus?.problematic_servers_count" class="problems-card">
      <template #header>
        <div class="card-header">
          <span>问题服务器</span>
          <ElIcon class="header-icon warning"><Warning /></ElIcon>
        </div>
      </template>
      
      <div class="problems-list">
        <div 
          v-for="server in monitorStatus.problematic_servers" 
          :key="server.server_ip"
          class="problem-item"
        >
          <div class="problem-info">
            <div class="problem-ip">{{ server.server_ip }}</div>
            <div class="problem-details">
              <span class="problem-status" :class="server.server_status">
                {{ server.server_status === 'offline' ? '离线' : '在线' }}
              </span>
              <span class="problem-score">健康分: {{ server.health_score.toFixed(1) }}</span>
              <span class="problem-errors">错误项: {{ server.error_items }}/{{ server.total_items }}</span>
            </div>
          </div>
          <div class="problem-progress">
            <ElProgress 
              :percentage="server.health_score"
              :color="server.health_score > 80 ? '#10b981' : server.health_score > 50 ? '#f59e0b' : '#ef4444'"
              :show-text="false"
              :stroke-width="8"
            />
          </div>
        </div>
      </div>
    </ElCard>

    <!-- Error Statistics -->
    <ElCard class="error-stats-card">
      <template #header>
        <div class="card-header">
          <span>错误统计</span>
          <ElIcon class="header-icon"><Close /></ElIcon>
        </div>
      </template>
      
      <div class="error-stats">
        <div class="error-item">
          <div class="error-type">连接错误</div>
          <div class="error-count">{{ monitorStats?.error_stats.connection_errors || 0 }}</div>
        </div>
        <div class="error-item">
          <div class="error-type">超时错误</div>
          <div class="error-count">{{ monitorStats?.error_stats.timeout_errors || 0 }}</div>
        </div>
        <div class="error-item">
          <div class="error-type">命令错误</div>
          <div class="error-count">{{ monitorStats?.error_stats.command_errors || 0 }}</div>
        </div>
        <div class="error-item">
          <div class="error-type">其他错误</div>
          <div class="error-count">{{ monitorStats?.error_stats.other_errors || 0 }}</div>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.dashboard {
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
}

.dashboard-header p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.alert-card {
  margin-bottom: 24px;
}

.metrics-row {
  margin-bottom: 24px;
}

.metric-card {
  height: 100%;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.metric-icon.server {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.metric-icon.online {
  background: linear-gradient(135deg, #10b981, #059669);
}

.metric-icon.monitor {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.metric-icon.success {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.status-row {
  margin-bottom: 24px;
}

.status-card {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #1f2937;
}

.header-icon {
  color: #6b7280;
}

.header-icon.warning {
  color: #f59e0b;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.status-item {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.status-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.status-value.error {
  color: #ef4444;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.running {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.stopped {
  background: #fee2e2;
  color: #991b1b;
}

.monitor-status {
  padding: 8px 0;
}

.status-progress {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #374151;
}

.progress-count {
  font-weight: 600;
  color: #1f2937;
}

.problems-card {
  margin-bottom: 24px;
}

.problems-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.problem-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.problem-info {
  flex: 1;
}

.problem-ip {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.problem-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.problem-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.problem-status.offline {
  background: #fee2e2;
  color: #991b1b;
}

.problem-status.online {
  background: #fef3c7;
  color: #92400e;
}

.problem-score,
.problem-errors {
  color: #6b7280;
}

.problem-progress {
  width: 120px;
  margin-left: 16px;
}

.error-stats-card {
  margin-bottom: 24px;
}

.error-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.error-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.error-type {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.error-count {
  font-size: 20px;
  font-weight: 700;
  color: #ef4444;
}

@media (max-width: 768px) {
  .dashboard-header h2 {
    font-size: 24px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .problem-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .problem-progress {
    width: 100%;
    margin-left: 0;
  }
  
  .error-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>