# 自动刷新器 Chrome 扩展

一个简单易用的浏览器标签页自动刷新工具，支持秒和分钟为单位的定时刷新。

## 功能特点

- 简洁美观的用户界面
- 支持秒(s)和分钟(m)为单位的刷新间隔
- 可随时启动和停止刷新
- 状态保持，重新打开浏览器后仍然有效

## 使用方法

1. 在Chrome浏览器中加载此扩展
2. 点击工具栏中的扩展图标打开控制面板
3. 输入刷新间隔，例如：30s（30秒）或5m（5分钟）
4. 点击"开始"按钮启动自动刷新
5. 需要停止时，再次点击扩展图标，然后点击"停止"按钮

## 图标说明

本扩展使用SVG格式的图标，已经配置好可以直接在Chrome中使用。如果您需要PNG格式的图标，可以使用以下方法转换：

### 转换步骤（可选）：

1. 打开 `icons/convert_to_png.html` 文件
2. 按照页面上的说明操作，为每个尺寸生成PNG图标
3. 如果需要使用PNG格式，请修改manifest.json文件中的图标路径

## 开发说明

- manifest.json: 扩展配置文件
- popup.html/css/js: 弹出窗口界面和逻辑
- background.js: 后台刷新逻辑

## 版本历史

- 1.0: 初始版本，基本刷新功能
- 1.1: 添加停止功能，优化UI界面