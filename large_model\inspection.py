#!/usr/bin/env python3
import subprocess
import datetime
import sys
import os
import concurrent.futures
import threading
from typing import List, Tu<PERSON>, Dict
import json
import argparse

class Config:
    def __init__(self):
        self.ROOT_IPS = [
            "***********", "***********", "***********", "***********", "***********",
            "***********", "***********", "***********", "***********1", "***********2",
            "***********3", "***********4", "***********5", "***********6", "***********7",
            "***********8", "***********7", "***********8", "***********9", "***********0",
            "***********1", "***********2", "***********3", "***********4", "************",
            "************", "************2", "************3", "************4", "************5", "***********11",
            "***********12", "***********13", "***********14", "***********15"
        ]
        
        self.SPECIAL_IPS = {
            "************2": {"user": "root", "password": "ZZHKG-DATACENTER+2025"}
        }
        
        self.KGZS_IPS = ["***********", "************"] + [f"10.102.11.{i}" for i in range(33, 57)]
        
        self.CREDENTIALS = {
            "root": {"password": "Admin@123_"},
            "kgzs": {"password": "Kgzs@2024"}
        }
        
        self.SSH_OPTIONS = "-o StrictHostKeyChecking=no -o ConnectTimeout=10 -o ServerAliveInterval=60"
        self.MAX_WORKERS = 10

class SSHManager:
    def __init__(self, config: Config):
        self.config = config
        self.lock = threading.Lock()
        
    def run_command(self, ip: str, user: str, command: str, use_sudo: bool = False) -> Tuple[str, str, bool]:
        try:
            if ip in self.config.SPECIAL_IPS and user == self.config.SPECIAL_IPS[ip]["user"]:
                password = self.config.SPECIAL_IPS[ip]["password"]
            else:
                password = self.config.CREDENTIALS[user]["password"]
            
            if user == "root":
                ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"{command}\""
            else:
                if use_sudo:
                    ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"echo '{password}' | sudo -S {command}\""
                else:
                    ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"{command}\""
            
            result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.stdout, result.stderr, result.returncode == 0
            
        except subprocess.TimeoutExpired:
            return "", f"连接超时 (IP: {ip})", False
        except Exception as e:
            return "", f"错误: {str(e)}", False

class Logger:
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime("%Y%m%d%H%M")
        self.log_filename = os.path.join(log_dir, f"{timestamp}.log")
        self.json_filename = os.path.join(log_dir, f"{timestamp}.json")
        self.lock = threading.Lock()
        self.results = []
        
    def write(self, content: str, print_console: bool = True, use_color: bool = False):
        with self.lock:
            clean_content = self._remove_ansi_codes(content)
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(clean_content)
            
            if print_console:
                if use_color:
                    print(content, end='')
                else:
                    print(clean_content, end='')
    
    def write_colored(self, content: str, color_code: str = "", print_console: bool = True):
        if color_code and sys.stdout.isatty():
            colored_content = f"{color_code}{content}\033[0m"
        else:
            colored_content = content
            
        with self.lock:
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(content)
            if print_console:
                print(colored_content, end='')
    
    def _remove_ansi_codes(self, text: str) -> str:
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        return ansi_escape.sub('', text)
                
    def add_result(self, result: Dict):
        with self.lock:
            self.results.append(result)
            
    def save_json(self):
        with open(self.json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

class LustreChecker:
    def __init__(self, config: Config, logger: Logger):
        self.config = config
        self.logger = logger
        self.ssh_manager = SSHManager(config)
        
    def check_host(self, ip: str, user: str, checks: List[str]) -> Dict:
        host_result = {
            "ip": ip,
            "user": user,
            "timestamp": (datetime.datetime.now() + datetime.timedelta(hours=8)).isoformat(),
            "checks": {}
        }
        
        for check in checks:
            if check == "list_nids":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "lctl list_nids", use_sudo=(user == "kgzs")
                )
                host_result["checks"]["list_nids"] = {
                    "success": success,
                    "output": stdout.strip() if success else stderr
                }
                
            elif check == "lustre_mount":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "df -h | grep -E '(lustre|o2ib)'", use_sudo=False
                )
                host_result["checks"]["lustre_mount"] = {
                    "success": success,
                    "output": stdout.strip() if success else stderr
                }
                
            elif check == "config_file":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "grep ExecStart /etc/systemd/system/exa-client-deploy.service 2>/dev/null || echo 'File not found'", 
                    use_sudo=False
                )
                host_result["checks"]["config_file"] = {
                    "success": success,
                    "output": stdout.strip() if stdout else stderr
                }
                
        return host_result
    
    def check_hosts_parallel(self, hosts: List[Tuple[str, str]], checks: List[str]):
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.MAX_WORKERS) as executor:
            future_to_host = {
                executor.submit(self.check_host, ip, user, checks): (ip, user) 
                for ip, user in hosts
            }
            
            for future in concurrent.futures.as_completed(future_to_host):
                ip, user = future_to_host[future]
                try:
                    result = future.result()
                    self.logger.add_result(result)
                    self.format_and_log_result(result)
                except Exception as e:
                    self.logger.write(f"检查 {ip} ({user}) 时出错: {str(e)}\n")
                    
    def format_and_log_result(self, result: Dict):
        ip = result["ip"]
        user = result["user"]
        
        self.logger.write_colored(f"\nIP: {ip} (用户: {user})\n", "\033[31m")
        
        for check_name, check_result in result["checks"].items():
            if check_result["success"]:
                self.logger.write_colored(f"{check_name}: ✓\n", "\033[32m")
                if check_result["output"]:
                    self.logger.write(f"{check_result['output']}\n")
            else:
                self.logger.write_colored(f"{check_name}: ✗ - {check_result['output']}\n", "\033[31m")
                
        self.logger.write("-" * 40 + "\n")
        
    def run_all_checks(self):
        start_time = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')
        self.logger.write_colored(f"检查开始时间: {start_time}\n", "\033[36m")
        self.logger.write(f"日志文件: {self.logger.log_filename}\n")
        self.logger.write(f"JSON结果: {self.logger.json_filename}\n\n")
        
        root_hosts = [(ip, "root") for ip in self.config.ROOT_IPS]
        kgzs_hosts = [(ip, "kgzs") for ip in self.config.KGZS_IPS]
        
        special_hosts = []
        for ip, info in self.config.SPECIAL_IPS.items():
            if ip not in self.config.ROOT_IPS and ip not in self.config.KGZS_IPS:
                special_hosts.append((ip, info["user"]))
        
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write_colored("1. 检查 lctl list_nids 输出\n", "\033[33m")
        self.logger.write("="*80 + "\n")
        
        self.logger.write_colored("\n### Root用户设备 ###\n", "\033[35m")
        self.check_hosts_parallel(root_hosts, ["list_nids"])
        
        self.logger.write_colored("\n### KGZS用户设备 ###\n", "\033[35m")
        self.check_hosts_parallel(kgzs_hosts, ["list_nids"])
        
        if special_hosts:
            self.logger.write_colored("\n### 特殊账户设备 ###\n", "\033[35m")
            self.check_hosts_parallel(special_hosts, ["list_nids"])
        
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write_colored("2. 检查 /lustre 挂载目录\n", "\033[33m")
        self.logger.write("="*80 + "\n")
        
        self.logger.write_colored("\n### Root用户设备 ###\n", "\033[35m")
        self.check_hosts_parallel(root_hosts, ["lustre_mount"])
        
        self.logger.write_colored("\n### KGZS用户设备 ###\n", "\033[35m")
        self.check_hosts_parallel(kgzs_hosts, ["lustre_mount"])
        
        if special_hosts:
            self.logger.write_colored("\n### 特殊账户设备 ###\n", "\033[35m")
            self.check_hosts_parallel(special_hosts, ["lustre_mount"])
        
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write_colored("3. 检查配置文件\n", "\033[33m")
        self.logger.write("="*80 + "\n")
        
        self.logger.write_colored("\n### KGZS用户设备 ###\n", "\033[35m")
        self.check_hosts_parallel(kgzs_hosts, ["config_file"])
        
        self.logger.write_colored("\n### Root用户设备 ###\n", "\033[35m")
        self.check_hosts_parallel(root_hosts, ["config_file"])
        
        if special_hosts:
            self.logger.write_colored("\n### 特殊账户设备 ###\n", "\033[35m")
            self.check_hosts_parallel(special_hosts, ["config_file"])
        
        self.logger.save_json()
        end_time = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')
        self.logger.write_colored(f"\n检查结束时间: {end_time}\n", "\033[36m")
        self.logger.write_colored(f"日志已保存到: {self.logger.log_filename}\n", "\033[32m")
        self.logger.write_colored(f"JSON结果已保存到: {self.logger.json_filename}\n", "\033[32m")

def main():
    parser = argparse.ArgumentParser(description="Lustre文件系统检查工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录")
    parser.add_argument("--max-workers", type=int, default=10, help="最大并发数")
    parser.add_argument("--config", help="配置文件路径（JSON格式）")
    parser.add_argument("--no-color", action="store_true", help="禁用彩色输出")
    
    args = parser.parse_args()
    
    config = Config()
    if args.max_workers:
        config.MAX_WORKERS = args.max_workers
        
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            custom_config = json.load(f)
            if "root_ips" in custom_config:
                config.ROOT_IPS = custom_config["root_ips"]
            if "kgzs_ips" in custom_config:
                config.KGZS_IPS = custom_config["kgzs_ips"]
            if "credentials" in custom_config:
                config.CREDENTIALS.update(custom_config["credentials"])
    
    logger = Logger(args.log_dir)
    
    if args.no_color:
        pass
    
    checker = LustreChecker(config, logger)
    checker.run_all_checks()
    
    current_time = datetime.datetime.now() + datetime.timedelta(hours=8)
    current_hour = current_time.hour
    
    if 6 <= current_hour < 15:
        subprocess.run("echo '' >> /lustre/bench/echo_test", shell=True)
        subprocess.run("date -d \"+8 hours\" \"+%Y%m%d%H%M\" >> /lustre/bench/echo_test", shell=True)
    else:
        subprocess.run("date -d \"+8 hours\" \"+%Y%m%d%H%M\" >> /lustre/bench/echo_test", shell=True)
    
    logger.write_colored(f"\n执行了时间检查，当前时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}\n", "\033[36m")

if __name__ == "__main__":
    main()


    