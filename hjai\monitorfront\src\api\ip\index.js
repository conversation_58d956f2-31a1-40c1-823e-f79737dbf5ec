import request from '@/utils/request'

/**
 * 获取IP地址列表
 * @param {Object} params - 查询参数
 * @param {boolean} params.connectable - 是否可连接
 * @returns {Promise} - 请求Promise
 */
export function getIpList (params) {
  return request({
    url: '/ip/list',
    method: 'get',
    params
  })
}

/**
 * 获取IP用户详细列表（分页）
 * @param {Object} data - 请求参数
 * @param {number} data.page - 页码，从1开始
 * @param {number} data.page_size - 每页记录数，默认10
 * @param {boolean} data.is_deleted - 是否删除过滤
 * @param {boolean} data.is_connectable - 是否可连通过滤
 * @returns {Promise} - 请求Promise
 */
export function getIpUserList (data) {
  return request({
    url: '/ip/user-list',
    method: 'post',
    data,
    useCache: true,
    cacheTime: 60000 // 缓存1分钟
  })
}

/**
 * 添加IP
 * @param {Object} data - IP数据
 * @param {string} data.ip - IP地址，必须是有效的IPv4格式（必填）
 * @param {string} data.user - 用户名（必填）
 * @param {string} data.password - 密码，为空时使用SSH密钥认证（可选）
 * @param {boolean} data.use_ssh_key - 是否使用SSH密钥认证（可选，默认false）
 * @param {string} data.ssh_key_path - SSH私钥文件路径（可选）
 * @returns {Promise} - 返回包含code和message的响应对象
 */
export function addIp (data) {
  return request({
    url: '/ip/add',
    method: 'post',
    data
  })
}

/**
 * 删除IP
 * @param {number} id - IP ID
 * @returns {Promise} - 请求Promise
 */
export function deleteIp (id) {
  return request({
    url: `/ip/delete/${id}`,
    method: 'delete'
  })
}
