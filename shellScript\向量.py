import jieba
from gensim.models import Word2Vec
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

def get_sentence_vector(sentence, model):
    words = list(jieba.cut(sentence))
    word_vectors = []
    for word in words:
        try:
            vector = model.wv[word]
            word_vectors.append(vector)
        except KeyError:
            continue
    if word_vectors:
        return np.mean(word_vectors, axis=0)
    return None

def calculate_sentence_similarities(sentences, model):
    n = len(sentences)
    similarity_matrix = np.zeros((n, n))
    sentence_vectors = [get_sentence_vector(sentence, model) for sentence in sentences]
    for i in range(n):
        for j in range(i+1, n):
            vec1 = sentence_vectors[i]
            vec2 = sentence_vectors[j]
            if vec1 is not None and vec2 is not None:
                similarity = cosine_similarity(vec1.reshape(1, -1), vec2.reshape(1, -1))[0][0]
                similarity_matrix[i][j] = similarity
                similarity_matrix[j][i] = similarity
    return similarity_matrix, sentence_vectors

if __name__ == "__main__":
    test_sentences = [
        "联合国就苏丹达尔富尔地区大规模暴力事件发出警告",
        "土耳其、芬兰、瑞典与北约代表将继续就瑞典\"入约\"问题进行谈判",
        "日本岐阜市陆上自卫队射击场内发生枪击事件 3人受伤",
        "国家游泳中心(水立方):恢复游泳、水乐园等水上项目运营",
        "我国首次在空间站开展舱外辐射生物学暴露实验",
        "苹果公司宣布将在未来3-5年出售其iPhone 15系列产品",
        "苹果我喜欢吃",
    ]
    tokenized_sentences = [list(jieba.cut(sentence)) for sentence in test_sentences]
    model = Word2Vec(tokenized_sentences, vector_size=100, window=5, min_count=1)
    similarity_matrix, sentence_vectors = calculate_sentence_similarities(test_sentences, model)

    print("各句子对的相似度：\n")
    for i in range(len(test_sentences)):
        for j in range(i+1, len(test_sentences)):
            print(f"句子{i+1}与句子{j+1}的相似度: {similarity_matrix[i][j]:.4f}")
            if similarity_matrix[i][j] >= 0.5:
                print("判断: 相似")
            else:
                print("判断: 不相似")
            print(f"句子{i+1}: {test_sentences[i]}")
            print(f"句子{j+1}: {test_sentences[j]}")
            print("-" * 80)

    query_vec = sentence_vectors[0]
    doc_vecs = sentence_vectors[1:]
    print(f"Query与自己的余弦距离： {1.00:.2f}")
    print("Query与Documents的余弦距离：")
    for vec in doc_vecs:
        if vec is not None and query_vec is not None:
            cos_sim = cosine_similarity(query_vec.reshape(1, -1), vec.reshape(1, -1))[0][0]
            cos_dist = 1 - cos_sim
            print(f"{cos_dist:.15f}")
        else:
            print("N/A")
    print(f"\nQuery与自己的欧氏距离： {0.00:.2f}")
    print("Query与Documents的欧氏距离：")
    for vec in doc_vecs:
        if vec is not None and query_vec is not None:
            euc_dist = np.linalg.norm(query_vec - vec)
            print(f"{euc_dist:.15f}")
        else:
            print("N/A")
