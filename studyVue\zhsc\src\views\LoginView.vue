<template>
  <div class="login-container">
    <div class="login-title">短信登入</div>
    <div class="login-form">
      <div class="login-form-item">
        <div class="login-form-item-label">手机号</div>
        <div class="login-form-item-input">
          <input type="text" v-model="form.phone" placeholder="请输入手机号" />
        </div>
      </div>
      <div class="login-form-item">
        <div class="login-form-item-label">验证码</div>
        <div class="login-form-item-input">
          <input type="text" v-model="form.code" placeholder="请输入验证码" />
          <div class="login-form-item-input-btn" @click="getVerificationCode">
            {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
          </div>
        </div>
      </div>
      <div class="login-form-item">
        <div class="login-form-item-label">图形验证码</div>
        <div class="login-form-item-input">
          <input type="text" v-model="form.captcha" placeholder="请输入图形验证码" />
          <div class="login-form-item-input-captcha">
            <div v-if="!ImageData.image" class="captcha-placeholder"></div>
            <img v-else :src="ImageData.image" alt="验证码" @click="refreshCaptcha" />
          </div>
        </div>
      </div>
      <button class="login-button" @click="submitForm" :disabled="isLoading">
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
    </div>
  </div>
</template>

<script>

import service from '@/api/request'
export default {
  data () {
    return {
      form: {
        phone: '',
        code: '',
        captcha: ''
      },
      captchaUrl: 'https://dummyimage.com/100x40/f5f7fa/333&text=验证码',
      countdown: 0,
      timer: null,
      ImageData: {
        image: '',
        md5: '',
        key: ''
      },
      captchaRequestCount: 0,
      captchaRequestTime: null,
      isLoadingCaptcha: false,
      isLoading: false // 登录加载状态
    }
  },
  methods: {
    showToast (message, duration = 3000) {
      this.$toast(message, duration)
    },
    refreshCaptcha () {
      // 检查请求频率限制
      const now = new Date()

      // 如果是第一次请求或者已经过了1分钟，重置计数器和时间
      if (!this.captchaRequestTime || (now - this.captchaRequestTime) > 60000) {
        this.captchaRequestCount = 0
        this.captchaRequestTime = now
      }

      // 检查是否超过每分钟3次的限制
      if (this.captchaRequestCount >= 3) {
        this.showToast('请求过于频繁，请稍后再试')
        return
      }

      // 增加请求计数
      this.captchaRequestCount++

      // 设置加载状态
      this.isLoadingCaptcha = true
      this.ImageData.image = ''

      service.get('/index.php?s=/api/captcha/image')
        .then(response => {
          // API返回的base64数据已经包含了完整的前缀，直接使用
          this.ImageData.image = response.data.base64
          this.ImageData.md5 = response.data.md5
          this.ImageData.key = response.data.key
          console.log('获取验证码成功：', response)
          this.isLoadingCaptcha = false
        })
        .catch(error => {
          console.error('获取验证码失败：', error)
          this.isLoadingCaptcha = false
        })
    },
    getVerificationCode () {
      if (this.countdown > 0) return
      if (!this.form.phone) {
        this.showToast('请输入手机号')
        return
      }
      if (!this.form.captcha) {
        this.showToast('请输入图形验证码')
        return
      }
      if (!this.ImageData.key) {
        this.showToast('请先获取图形验证码')
        return
      }

      // 发送短信验证码请求
      const params = {
        captchaCode: this.form.captcha,
        captchaKey: this.ImageData.key,
        mobile: this.form.phone
      }

      service.post('/index.php?s=/api/captcha/sendSmsCaptcha', params)
        .then(response => {
          if (response.status === 200) {
            // 发送成功，开始倒计时
            this.countdown = 60
            this.timer = setInterval(() => {
              if (this.countdown > 0) {
                this.countdown--
              } else {
                clearInterval(this.timer)
              }
            }, 1000)
            this.showToast(response.message || '验证码发送成功')
          } else {
            this.showToast(response.message || '验证码发送失败')
          }
        })
        .catch(error => {
          console.error('发送验证码失败：', error)
          this.showToast('验证码发送失败，请重试')
        })
    },
    submitForm () {
      // 如果正在加载中，不允许重复提交
      if (this.isLoading) {
        return
      }

      // 表单验证
      if (!this.form.phone) {
        this.showToast('请输入手机号')
        return
      }
      if (!this.form.code) {
        this.showToast('请输入验证码')
        return
      }
      if (!this.form.captcha) {
        this.showToast('请输入图形验证码')
        return
      }

      // 设置加载状态
      this.isLoading = true
      this.showToast('登录中，请稍候...')

      // 构建请求体，按照规定的格式
      const requestBody = {
        form: {
          smsCode: this.form.code,
          mobile: this.form.phone,
          isParty: false,
          partyData: {}
        }
      }

      // 发起登录请求
      service.post('/index.php?s=/api/passport/login', requestBody)
        .then(response => {
          if (response.status === 200) {
            // 登录成功，保存token到本地
            localStorage.setItem('token', response.data.token)
            localStorage.setItem('userId', response.data.userId)

            // 显示成功提示
            this.showToast('登录成功')

            // 获取重定向目标
            const redirect = this.$route.query.redirect || '/home'

            // 使用 replace 避免登录页留在历史记录中
            this.$router.replace(redirect)
          } else {
            this.showToast(response.message || '登录失败，请重试')
          }
        })
        .catch(error => {
          console.error('登录失败：', error)
          this.showToast('登录失败，请重试')
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  },
  mounted () {
    // 直接获取验证码
    this.refreshCaptcha()
  },
  beforeDestroy () {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style>
 .login-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 0 1rem;
    box-sizing: border-box;
    background-color: #f5f7fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
 }

 .login-title {
    margin-top: 1.5rem;
    font-size: 1.5rem;
    text-align: center;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
 }

 .login-form {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    background-color: #fff;
    box-sizing: border-box;
 }

 .login-form-item {
    margin-bottom: 1.25rem;
 }

 .login-form-item-label {
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 500;
 }

 .login-form-item-input {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
 }

 .login-form-item-input:focus-within {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
 }

 .login-form-item-input input {
    flex: 1;
    height: 44px;
    padding: 0 1rem;
    border: none;
    outline: none;
    font-size: 0.95rem;
    color: #303133;
    width: 100%;
    box-sizing: border-box;
    background-color: transparent;
 }

 .login-form-item-input input::placeholder {
    color: #c0c4cc;
 }

 .login-form-item-input-btn {
    padding: 0 0.75rem;
    height: 44px;
    line-height: 44px;
    background-color: #409eff;
    color: #fff;
    font-size: 0.85rem;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
    text-align: center;
    min-width: 90px;
    transition: background-color 0.3s;
 }

 .login-form-item-input-btn:hover {
    background-color: #66b1ff;
 }

 .login-form-item-input-btn:active {
    background-color: #3a8ee6;
    transform: scale(0.98);
 }

 .login-form-item-input-captcha {
    width: 110px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    cursor: pointer;
    border-left: 1px solid #dcdfe6;
 }

 .login-form-item-input-captcha img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
 }

 .captcha-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    border: 1px dashed #dcdfe6;
 }

 /* 登录按钮样式 */
 .login-button {
    width: 100%;
    height: 48px;
    background-color: #409eff;
    border: none;
    border-radius: 8px;
    color: #fff;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 1.5rem;
    transition: all 0.3s;
 }

 .login-button:hover {
    background-color: #66b1ff;
 }

 .login-button:active {
    background-color: #3a8ee6;
    transform: scale(0.98);
 }

 /* 禁用状态 */
 .login-button:disabled {
    background-color: #a0cfff;
    cursor: not-allowed;
    transform: none;
 }

 /* 响应式设计 - 更全面的断点 */
 @media screen and (max-width: 480px) {
    .login-form {
        padding: 1.25rem;
        max-width: 100%;
        margin: 0 auto;
        border-radius: 10px;
    }

    .login-title {
        margin-top: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .login-form-item-input-btn {
        min-width: 85px;
        font-size: 0.8rem;
    }
 }

 @media screen and (max-width: 375px) {
    .login-form {
        padding: 1rem;
        border-radius: 8px;
    }

    .login-title {
        font-size: 1.3rem;
        margin-bottom: 1.25rem;
    }

    .login-form-item-input-btn {
        font-size: 0.75rem;
        padding: 0 0.5rem;
        min-width: 75px;
    }

    .login-form-item-input input {
        padding: 0 0.75rem;
        font-size: 0.9rem;
    }

    .login-form-item-input-captcha {
        width: 100px;
    }

    .login-button {
        height: 44px;
        margin-top: 1.25rem;
        font-size: 0.95rem;
    }
 }

 /* 安全区适配 - 针对全面屏手机 */
 @supports (padding-bottom: env(safe-area-inset-bottom)) {
    .login-container {
        padding-bottom: env(safe-area-inset-bottom);
        padding-top: env(safe-area-inset-top);
    }
 }

 /* 触摸屏优化 */
 @media (hover: none) {
    .login-form-item-input-btn,
    .login-button,
    .login-form-item-input-captcha {
        -webkit-tap-highlight-color: transparent;
    }

    .login-form-item-input-btn:hover,
    .login-button:hover {
        background-color: #409eff; /* 避免触摸设备上的悬停效果 */
    }
 }
</style>
