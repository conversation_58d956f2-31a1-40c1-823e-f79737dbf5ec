import request from '@/utils/request'
//用户登录（已完成）
export function login(data) {
  return request({
    url: 'http://10.64.0.106:8888/api/base/login',
    method: 'post',
    data
  })
}

export function refreshToken() {
  return request({
    url: 'http://10.64.0.106:8888/api/base/refreshToken',
    method: 'post'
  })
}
//用户退出接口（已完成）
export function logout() {
  return request({
    url: 'http://10.64.0.106:8888/api/base/logout',
    method: 'post'
  })
}
