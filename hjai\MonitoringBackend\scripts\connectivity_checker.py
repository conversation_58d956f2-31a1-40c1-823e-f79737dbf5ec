import asyncio
import logging
import paramiko
import time
import os
import psutil
from typing import List, Dict, Tuple, Optional, Set
from models.ip_user import IPUser
from tortoise.exceptions import OperationalError
# 导入SSH连接工具
from utils.ssh_utils import SSHConnectionManager
import pytz
from datetime import datetime
# 导入新的时区处理工具
from config.timezone_utils import TZ
from contextlib import asynccontextmanager
from functools import lru_cache

from scripts.base_monitor import (
    BaseMonitor,
    server_cache,
    batch_update_server_status,
    get_optimal_concurrency
)

# 设置上海时区
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')

# 获取日志记录器
logger = logging.getLogger(__name__)

# 全局缓存和配置
class ServerCache:
    """服务器信息缓存类，减少数据库查询"""
    def __init__(self, ttl_seconds: int = 300):
        self.servers: List[Dict[str, str]] = []
        self.last_update: float = 0
        self.ttl_seconds: int = ttl_seconds
        self.failed_servers: Set[str] = set()  # 记录失败的服务器ID
    
    def needs_refresh(self) -> bool:
        """检查缓存是否需要刷新"""
        return time.time() - self.last_update > self.ttl_seconds or not self.servers
    
    async def refresh_cache(self):
        """刷新服务器列表缓存"""
        self.servers = await _fetch_all_servers()
        self.last_update = time.time()
    
    def mark_failed(self, server_id: str):
        """标记失败的服务器"""
        self.failed_servers.add(server_id)
    
    def mark_success(self, server_id: str):
        """标记成功的服务器，从失败集合中移除"""
        if server_id in self.failed_servers:
            self.failed_servers.remove(server_id)
    
    def prioritize_servers(self) -> List[Dict[str, str]]:
        """对服务器进行优先级排序，失败的服务器优先检查"""
        failed_servers = [s for s in self.servers if s['id'] in self.failed_servers]
        other_servers = [s for s in self.servers if s['id'] not in self.failed_servers]
        return failed_servers + other_servers

# 创建全局缓存实例
server_cache = ServerCache()

async def _fetch_all_servers() -> List[Dict[str, str]]:
    """从数据库获取所有未删除的服务器凭据（内部函数）"""
    try:
        # 尝试使用新版表结构查询
        try:
            servers = await IPUser.filter(is_deleted=False).all()
        except OperationalError:
            # 如果失败，说明表中没有is_deleted列，使用旧版查询
            logger.warning("表结构未更新，使用旧版查询")
            servers = await IPUser.all()
        
        return [
            {
                "id": server.id,
                "ip": server.ip,
                "username": server.username, 
                "password": server.password,
                "is_connectable": server.is_connectable
            } 
            for server in servers
        ]
    except Exception as e:
        logger.error(f"获取服务器凭据失败: {str(e)}")
        return []

async def get_all_servers() -> List[Dict[str, str]]:
    """获取所有服务器，优先使用缓存"""
    if server_cache.needs_refresh():
        logger.debug("服务器缓存过期，从数据库刷新数据")
        await server_cache.refresh_cache()
    return server_cache.servers

@asynccontextmanager
async def ssh_connection(server: Dict[str, str], timeout: int = 15) -> paramiko.SSHClient:
    """
    SSH连接上下文管理器，确保连接正确关闭
    
    Args:
        server: 服务器信息
        timeout: 连接超时时间(秒)
        
    Yields:
        paramiko.SSHClient: SSH客户端连接
    """
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        yield ssh_client
    finally:
        ssh_client.close()

async def check_server_connectivity(server: Dict[str, str], max_retries: int = 2) -> bool:
    """
    检查单个服务器的连通性，支持SSH密钥认证和密码认证

    Args:
        server: 服务器信息字典
        max_retries: 最大重试次数

    Returns:
        bool: 是否可连通
    """
    retry_count = 0
    last_error = None

    while retry_count <= max_retries:
        try:
            # 尝试连接服务器
            if retry_count == 0:
                logger.info(f"正在检查服务器 {server['ip']} 的连通性...")
            else:
                logger.debug(f"正在重试({retry_count}/{max_retries})连接服务器 {server['ip']}...")

            # 使用新的SSH连接管理器
            ssh_client, connected = SSHConnectionManager.connect_to_server(server, timeout=15)

            if connected and ssh_client:
                # 连接成功，关闭连接并更新状态
                ssh_client.close()
                server_cache.mark_success(server['id'])
                return True
            else:
                # 连接失败，重试
                retry_count += 1
                last_error = Exception("SSH连接失败")
                if retry_count <= max_retries:
                    # 指数退避，第1次等待1秒，第2次等待4秒
                    wait_time = 2 ** retry_count
                    logger.debug(f"连接失败，{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.warning(f"服务器 {server['ip']} 在 {max_retries} 次尝试后仍然连接失败")

        except Exception as e:
            # 其他未知错误
            logger.warning(f"服务器 {server['ip']} 连接出现未知错误: {str(e)}")
            last_error = e
            retry_count += 1
            if retry_count <= max_retries:
                # 指数退避，第1次等待1秒，第2次等待4秒
                wait_time = 2 ** retry_count
                logger.debug(f"连接失败: {str(e)}，{wait_time}秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logger.warning(f"服务器 {server['ip']} 在 {max_retries} 次尝试后仍然连接失败")
                break

    # 所有重试都失败，标记为失败状态
    server_cache.mark_failed(server['id'])
    return False

async def update_server_status_batch(status_updates: List[Tuple[str, bool]]) -> None:
    """
    批量更新服务器状态
    
    Args:
        status_updates: 包含(服务器ID, 状态)元组的列表
    """
    try:
        # 按批处理更新，避免一次性提交过多数据
        batch_size = 100
        for i in range(0, len(status_updates), batch_size):
            batch = status_updates[i:i+batch_size]
            
            # 使用事务进行批量更新
            async with IPUser._meta.db.transaction():
                for server_id, status in batch:
                    await IPUser.filter(id=server_id).update(is_connectable=status)
            
            logger.debug(f"已批量更新 {len(batch)} 台服务器状态")
            
    except Exception as e:
        logger.error(f"批量更新服务器状态失败: {str(e)}")

def get_optimal_concurrency() -> int:
    """
    根据系统资源获取最优并发数
    
    Returns:
        int: 推荐的并发连接数
    """
    try:
        # 获取系统信息
        cpu_count = os.cpu_count() or 4
        memory = psutil.virtual_memory()
        memory_free_gb = memory.available / (1024 ** 3)  # 可用内存(GB)
        
        # 基于CPU和内存计算并发数
        # 每个SSH连接大约使用5-10MB内存
        cpu_based = cpu_count * 5  # 每CPU核心5个连接
        memory_based = int(memory_free_gb * 10)  # 每GB可用内存10个连接
        
        # 取两者较小值，确保不超出系统资源
        concurrency = min(cpu_based, memory_based)
        
        # 限制在合理范围内
        concurrency = max(min(concurrency, 50), 10)  # 最少10，最多50
        
        logger.debug(f"系统状态: CPU核心数={cpu_count}, 可用内存={memory_free_gb:.1f}GB, 推荐并发数={concurrency}")
        return concurrency
        
    except Exception as e:
        logger.warning(f"获取系统资源信息失败: {str(e)}，使用默认并发数20")
        return 20

async def process_server_batch(servers: List[Dict[str, str]], semaphore: asyncio.Semaphore) -> List[Tuple[str, bool]]:
    """
    处理一批服务器的连通性检查
    
    Args:
        servers: 要处理的服务器列表
        semaphore: 控制并发的信号量
        
    Returns:
        List[Tuple[str, bool]]: 服务器状态更新列表，元组格式为(服务器ID, 状态)
    """
    async def check_with_semaphore(server):
        """使用信号量控制并发的检查函数"""
        async with semaphore:
            connected = await check_server_connectivity(server)
            return (server['id'], connected)
    
    # 创建任务并并行执行
    tasks = [check_with_semaphore(server) for server in servers]
    return await asyncio.gather(*tasks)

async def check_all_servers() -> None:
    """分批并行检查所有服务器的连通性"""
    # 获取所有服务器，优先检查上次失败的服务器
    servers = server_cache.prioritize_servers() if server_cache.servers else await get_all_servers()
    
    if not servers:
        logger.warning("未找到服务器凭据")
        return
    
    # 确定最佳并发数和批量大小
    concurrency = get_optimal_concurrency()
    batch_size = min(200, len(servers))  # 每批最多200台服务器
    semaphore = asyncio.Semaphore(concurrency)
    
    logger.info(f"开始检查 {len(servers)} 台服务器的连通性，并发数: {concurrency}, 批处理大小: {batch_size}")
    
    # 分批处理服务器
    all_status_updates = []
    total_connectable = 0
    
    # 按批次处理服务器
    for i in range(0, len(servers), batch_size):
        batch = servers[i:i+batch_size]
        logger.debug(f"开始处理第 {i//batch_size + 1} 批服务器，共 {len(batch)} 台")
        
        # 处理当前批次并收集结果
        status_updates = await process_server_batch(batch, semaphore)
        all_status_updates.extend(status_updates)
        
        # 统计可连接的服务器数量
        batch_connectable = sum(1 for _, status in status_updates if status)
        total_connectable += batch_connectable
        
        logger.debug(f"第 {i//batch_size + 1} 批处理完成，{batch_connectable}/{len(batch)} 台服务器可连通")
        
        # 批量更新数据库
        await update_server_status_batch([(server_id, status) for server_id, status in status_updates])
    
    # 记录最终统计结果
    logger.info(f"连通性检查完成，共 {len(servers)} 台服务器，其中 {total_connectable} 台可连通")

class ConnectivityChecker(BaseMonitor):
    """服务器连通性检查类"""
    
    def __init__(self):
        # 初始化，设置默认检查间隔为1小时(3600秒)
        super().__init__("连通性", 3600)
    
    async def check_server_connectivity(self, server: Dict[str, str]) -> bool:
        """
        检查单个服务器的连通性
        
        Args:
            server: 服务器信息字典
            
        Returns:
            bool: 是否可连通
        """
        # 尝试连接服务器，这里直接复用基类的连接方法，无需实际数据收集
        _, is_connected = await self.connect_to_server(server)
        return is_connected
        
    async def process_server_batch(self, servers: List[Dict[str, str]], semaphore: asyncio.Semaphore) -> List[Tuple[str, bool]]:
        """
        处理一批服务器的连通性检查
        
        Args:
            servers: 要处理的服务器列表
            semaphore: 控制并发的信号量
            
        Returns:
            List[Tuple[str, bool]]: 服务器状态更新列表，元组格式为(服务器ID, 状态)
        """
        async def check_with_semaphore(server):
            """使用信号量控制并发的检查函数"""
            async with semaphore:
                connected = await self.check_server_connectivity(server)
                return (server['id'], connected)
        
        # 创建任务并并行执行
        tasks = [check_with_semaphore(server) for server in servers]
        return await asyncio.gather(*tasks)

    async def check_all_servers(self) -> None:
        """分批并行检查所有服务器的连通性"""
        from scripts.base_monitor import get_all_servers
        
        # 获取所有服务器，优先检查上次失败的服务器
        servers = server_cache.prioritize_servers() if server_cache.servers else await get_all_servers(False)
        
        if not servers:
            logger.warning("未找到服务器凭据")
            return
        
        # 确定最佳并发数和批量大小
        concurrency = get_optimal_concurrency()
        batch_size = min(200, len(servers))  # 每批最多200台服务器
        semaphore = asyncio.Semaphore(concurrency)
        
        logger.info(f"开始检查 {len(servers)} 台服务器的连通性，并发数: {concurrency}, 批处理大小: {batch_size}")
        
        # 分批处理服务器
        all_status_updates = []
        total_connectable = 0
        
        # 按批次处理服务器
        for i in range(0, len(servers), batch_size):
            batch = servers[i:i+batch_size]
            logger.debug(f"开始处理第 {i//batch_size + 1} 批服务器，共 {len(batch)} 台")
            
            # 处理当前批次并收集结果
            status_updates = await self.process_server_batch(batch, semaphore)
            all_status_updates.extend(status_updates)
            
            # 统计可连接的服务器数量
            batch_connectable = sum(1 for _, status in status_updates if status)
            total_connectable += batch_connectable
            
            logger.debug(f"第 {i//batch_size + 1} 批处理完成，{batch_connectable}/{len(batch)} 台服务器可连通")
            
            # 批量更新数据库
            await batch_update_server_status(status_updates)
        
        # 记录最终统计结果
        logger.info(f"连通性检查完成，共 {len(servers)} 台服务器，其中 {total_connectable} 台可连通")

    async def start_connectivity_checker(self, interval_seconds: int = None) -> None:
        """
        启动连通性检查服务
        
        Args:
            interval_seconds: 检查间隔，单位秒，默认为self.default_interval(3600秒)
        """
        interval = interval_seconds if interval_seconds is not None else self.default_interval
        logger.info(f"服务器连通性检查服务已启动，检查间隔: {interval}秒")
        
        # 立即进行一次连通性检查
        await self.check_all_servers()
        
        while True:
            try:
                # 等待下一次检查
                logger.debug(f"等待 {interval} 秒后进行下一次连通性检查...")
                await asyncio.sleep(interval)
                
                # 记录当前时间
                now = datetime.now(SHANGHAI_TZ)
                logger.debug(f"开始连通性检查周期 - {now.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 执行连通性检查
                await self.check_all_servers()
                
            except Exception as e:
                logger.error(f"连通性检查过程出错: {str(e)}")
                # 发生错误时仍然等待，避免无限循环消耗资源
                await asyncio.sleep(60)

# 为保持向后兼容性添加的函数
async def start_connectivity_checker(interval_seconds: int = 3600) -> None:
    """
    启动连通性检查服务（兼容旧版API）
    
    Args:
        interval_seconds: 检查间隔，单位秒，默认3600秒(1小时)
    """
    logger.info("通过兼容函数启动连通性检查服务")
    checker = ConnectivityChecker()
    await checker.start_connectivity_checker(interval_seconds)