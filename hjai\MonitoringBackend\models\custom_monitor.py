"""自定义监控项模型

定义用户可自定义的监控项，允许用户通过CLI命令定义监控内容
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import pytz
from tortoise import fields
from models.base_model import BaseModel, SHANGHAI_TZ, get_shanghai_now
from config.timezone import add_tz

# 导入时区处理工具
from config.timezone import now, fix_db_time

class MonitorItem(BaseModel):
    """
    自定义监控项

    用户可以定义各种监控项，如CPU使用率、内存使用情况等
    """
    name = fields.CharField(max_length=100, description="监控项名称")
    command = fields.TextField(description="用于获取监控数据的命令")
    description = fields.TextField(null=True, description="监控项描述")
    data_type = fields.CharField(max_length=20, description="数据类型: string, number, json等")

    # 新增字段
    timeout = fields.IntField(default=30, description="命令执行超时时间(秒)")
    retry_count = fields.IntField(default=2, description="失败重试次数")
    enabled = fields.BooleanField(default=True, description="是否启用")
    category = fields.CharField(max_length=50, null=True, description="监控分类")
    security_level = fields.CharField(max_length=20, default="normal", description="安全级别")
    validation_rules = fields.JSONField(null=True, description="数据验证规则")
    
    class Meta:
        table = "monitor_items"
        ordering = ["name"]
    
    def __str__(self):
        return f"{self.name}"

class MonitorItemIP(BaseModel):
    """
    监控项与IP地址的关联
    
    定义哪些IP地址需要执行哪些监控项
    """
    monitor_item = fields.ForeignKeyField("models.MonitorItem", related_name="ip_addresses")
    ip_address = fields.CharField(max_length=100, description="IP地址或主机名")
    
    class Meta:
        table = "monitor_item_ips"
        unique_together = [("monitor_item", "ip_address")]
    
    def __str__(self):
        return f"{self.monitor_item.name} @ {self.ip_address}"

class MonitorData(BaseModel):
    """
    监控数据
    
    存储各监控项收集到的数据
    """
    monitor_item = fields.ForeignKeyField("models.MonitorItem", related_name="data")
    ip = fields.CharField(max_length=100, description="IP地址或主机名")
    timestamp = fields.DatetimeField(description="数据采集时间")
    value = fields.TextField(description="监控值")
    status = fields.IntField(default=0, description="状态: 0-正常, 1-警告, 2-错误")
    
    class Meta:
        table = "monitor_data"
        ordering = ["-timestamp"]
        
    def __str__(self):
        return f"{self.monitor_item.name} @ {self.ip}: {self.value}"
        
    async def save(self, *args, **kwargs):
        """
        重写保存方法，使用新的时区处理策略
        """
        # 导入新的时区工具
        from config.timezone_utils import TZ

        # 如果timestamp未设置，使用当前UTC时间（带时区信息）
        if not self.timestamp:
            self.timestamp = TZ.now_utc()  # 保持UTC时区信息

        # 调用父类的save方法继续处理
        return await super().save(*args, **kwargs)

    def get_timestamp_display(self) -> str:
        """获取时间戳的显示格式（上海时区）"""
        if not self.timestamp:
            return None
        from config.timezone_utils import TZ
        # 如果timestamp已经有时区信息，直接转换；否则假设为UTC
        if self.timestamp.tzinfo is None:
            return TZ.to_display_format(self.timestamp, source_tz=TZ.UTC)
        else:
            return TZ.to_display_format(self.timestamp)
        
    @classmethod
    async def create_data(cls, monitor_item_id: int, ip: str, value: str, status: int = 0) -> "MonitorData":
        """
        创建监控数据

        Args:
            monitor_item_id: 监控项ID
            ip: IP地址或主机名
            value: 监控值
            status: 状态

        Returns:
            创建的监控数据对象
        """
        # 导入新的时区工具
        from config.timezone_utils import TZ

        # 创建监控数据，使用UTC时间（带时区信息）
        data = await cls.create(
            monitor_item_id=monitor_item_id,
            ip=ip,
            timestamp=TZ.now_utc(),  # 保持UTC时区信息
            value=value,
            status=status
        )

        return data


class MonitorExecution(BaseModel):
    """
    监控执行记录

    记录每次监控执行的详细信息，用于性能分析和故障排查
    """
    monitor_item = fields.ForeignKeyField("models.MonitorItem", related_name="executions")
    ip = fields.CharField(max_length=100, description="执行监控的IP地址")
    start_time = fields.DatetimeField(description="开始执行时间")
    end_time = fields.DatetimeField(null=True, description="结束执行时间")
    status = fields.CharField(max_length=20, description="执行状态: success, failed, timeout")
    error_message = fields.TextField(null=True, description="错误信息")
    execution_time = fields.FloatField(null=True, description="执行时间(秒)")
    command_output = fields.TextField(null=True, description="命令输出")
    retry_count = fields.IntField(default=0, description="重试次数")

    class Meta:
        table = "monitor_executions"
        ordering = ["-start_time"]

    def __str__(self):
        return f"{self.monitor_item.name}@{self.ip} - {self.status}"

    @classmethod
    async def create_execution(
        cls,
        monitor_item_id: int,
        ip: str,
        status: str = "running",
        error_message: Optional[str] = None,
        execution_time: Optional[float] = None,
        command_output: Optional[str] = None,
        retry_count: int = 0
    ) -> "MonitorExecution":
        """
        创建监控执行记录

        Args:
            monitor_item_id: 监控项ID
            ip: IP地址
            status: 执行状态
            error_message: 错误信息
            execution_time: 执行时间
            command_output: 命令输出
            retry_count: 重试次数

        Returns:
            创建的监控执行记录
        """
        from config.timezone_utils import TZ

        now = TZ.now_utc()
        end_time = now if status in ['success', 'failed', 'timeout'] else None

        execution = await cls.create(
            monitor_item_id=monitor_item_id,
            ip=ip,
            start_time=now,
            end_time=end_time,
            status=status,
            error_message=error_message,
            execution_time=execution_time,
            command_output=command_output,
            retry_count=retry_count
        )

        return execution

    async def update_execution(
        self,
        status: Optional[str] = None,
        error_message: Optional[str] = None,
        execution_time: Optional[float] = None,
        command_output: Optional[str] = None,
        retry_count: Optional[int] = None
    ):
        """更新执行记录"""
        from config.timezone_utils import TZ

        if status:
            self.status = status
            if status in ['success', 'failed', 'timeout'] and not self.end_time:
                self.end_time = TZ.now_utc()

        if error_message is not None:
            self.error_message = error_message

        if execution_time is not None:
            self.execution_time = execution_time

        if command_output is not None:
            self.command_output = command_output

        if retry_count is not None:
            self.retry_count = retry_count

        await self.save()