"""
自定义监控执行器测试

测试自定义监控执行器的各项功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import json

from services.custom_monitor_executor import CustomMonitorExecutor
from utils.ssh_executor import ExecutionResult


class TestCustomMonitorExecutor:
    """自定义监控执行器测试类"""
    
    @pytest.fixture
    def executor(self):
        """创建执行器实例"""
        return CustomMonitorExecutor()
    
    @pytest.fixture
    def mock_monitor_item(self):
        """模拟监控项"""
        item = Mock()
        item.id = 1
        item.name = "test_monitor"
        item.command = "echo 'test'"
        item.data_type = "string"
        item.timeout = 30
        item.retry_count = 2
        return item
    
    @pytest.fixture
    def mock_server(self):
        """模拟服务器信息"""
        return {
            'ip': '*************',
            'username': 'test',
            'password': 'test123'
        }
    
    @pytest.mark.asyncio
    async def test_execute_monitor_item_success(self, executor, mock_monitor_item, mock_server):
        """测试成功执行监控项"""
        with patch.object(executor.security_validator, 'validate_command') as mock_validate, \
             patch.object(executor.ssh_executor, 'execute_command') as mock_execute, \
             patch('models.custom_monitor.MonitorExecution.create_execution') as mock_create_exec, \
             patch('models.custom_monitor.MonitorData.create_data') as mock_create_data:
            
            # 设置模拟
            mock_validate.return_value = (True, None)
            mock_execute.return_value = ExecutionResult(
                success=True,
                output="test_output",
                error=None,
                exit_code=0,
                execution_time=1.5,
                retry_count=0,
                server_ip="*************"
            )
            
            mock_execution = Mock()
            mock_execution.update_execution = AsyncMock()
            mock_create_exec.return_value = mock_execution
            mock_create_data.return_value = Mock()
            
            # 执行测试
            success, error, data = await executor.execute_monitor_item(
                mock_monitor_item, mock_server, save_execution_record=True
            )
            
            # 验证结果
            assert success is True
            assert error is None
            assert data == "test_output"
            mock_validate.assert_called_once()
            mock_execute.assert_called_once()
            mock_create_exec.assert_called_once()
            mock_create_data.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_monitor_item_security_failure(self, executor, mock_monitor_item, mock_server):
        """测试安全验证失败"""
        with patch.object(executor.security_validator, 'validate_command') as mock_validate, \
             patch('models.custom_monitor.MonitorExecution.create_execution') as mock_create_exec:
            
            # 设置模拟 - 安全验证失败
            mock_validate.return_value = (False, "Dangerous command")
            mock_execution = Mock()
            mock_execution.update_execution = AsyncMock()
            mock_create_exec.return_value = mock_execution
            
            # 执行测试
            success, error, data = await executor.execute_monitor_item(
                mock_monitor_item, mock_server
            )
            
            # 验证结果
            assert success is False
            assert "命令安全验证失败" in error
            assert data is None
    
    @pytest.mark.asyncio
    async def test_execute_monitor_item_command_failure(self, executor, mock_monitor_item, mock_server):
        """测试命令执行失败"""
        with patch.object(executor.security_validator, 'validate_command') as mock_validate, \
             patch.object(executor.ssh_executor, 'execute_command') as mock_execute, \
             patch('models.custom_monitor.MonitorExecution.create_execution') as mock_create_exec:
            
            # 设置模拟
            mock_validate.return_value = (True, None)
            mock_execute.return_value = ExecutionResult(
                success=False,
                output=None,
                error="Command not found",
                exit_code=127,
                execution_time=0.5,
                retry_count=2,
                server_ip="*************"
            )
            
            mock_execution = Mock()
            mock_execution.update_execution = AsyncMock()
            mock_create_exec.return_value = mock_execution
            
            # 执行测试
            success, error, data = await executor.execute_monitor_item(
                mock_monitor_item, mock_server
            )
            
            # 验证结果
            assert success is False
            assert error == "Command not found"
            assert data is None
    
    @pytest.mark.asyncio
    async def test_execute_monitor_item_parse_failure(self, executor, mock_monitor_item, mock_server):
        """测试数据解析失败"""
        # 设置监控项为JSON类型
        mock_monitor_item.data_type = "json"
        
        with patch.object(executor.security_validator, 'validate_command') as mock_validate, \
             patch.object(executor.ssh_executor, 'execute_command') as mock_execute, \
             patch('models.custom_monitor.MonitorExecution.create_execution') as mock_create_exec:
            
            # 设置模拟
            mock_validate.return_value = (True, None)
            mock_execute.return_value = ExecutionResult(
                success=True,
                output="invalid json",  # 无效的JSON
                error=None,
                exit_code=0,
                execution_time=1.0,
                retry_count=0,
                server_ip="*************"
            )
            
            mock_execution = Mock()
            mock_execution.update_execution = AsyncMock()
            mock_create_exec.return_value = mock_execution
            
            # 执行测试
            success, error, data = await executor.execute_monitor_item(
                mock_monitor_item, mock_server
            )
            
            # 验证结果
            assert success is False
            assert "数据解析失败" in error
            assert data is None
    
    def test_parse_command_output_string(self, executor):
        """测试字符串输出解析"""
        data, error = executor._parse_command_output("test output", "string")
        assert data == "test output"
        assert error is None
    
    def test_parse_command_output_number(self, executor):
        """测试数字输出解析"""
        # 测试整数
        data, error = executor._parse_command_output("123", "number")
        assert data == 123
        assert error is None
        
        # 测试浮点数
        data, error = executor._parse_command_output("123.45", "number")
        assert data == 123.45
        assert error is None
        
        # 测试无效数字
        data, error = executor._parse_command_output("not_a_number", "number")
        assert data == "not_a_number"  # 返回原始字符串
        assert error is None
    
    def test_parse_command_output_json(self, executor):
        """测试JSON输出解析"""
        # 测试有效JSON
        json_data = {"key": "value", "number": 123}
        data, error = executor._parse_command_output(json.dumps(json_data), "json")
        assert data == json_data
        assert error is None
        
        # 测试无效JSON
        data, error = executor._parse_command_output("invalid json", "json")
        assert data is None
        assert "JSON解析失败" in error
    
    def test_parse_command_output_boolean(self, executor):
        """测试布尔输出解析"""
        # 测试true值
        for true_val in ['true', 'True', '1', 'yes', 'on', 'enabled']:
            data, error = executor._parse_command_output(true_val, "boolean")
            assert data is True
            assert error is None
        
        # 测试false值
        for false_val in ['false', 'False', '0', 'no', 'off', 'disabled']:
            data, error = executor._parse_command_output(false_val, "boolean")
            assert data is False
            assert error is None
        
        # 测试其他值
        data, error = executor._parse_command_output("maybe", "boolean")
        assert data == "maybe"  # 返回原始值
        assert error is None
    
    def test_parse_command_output_empty(self, executor):
        """测试空输出解析"""
        data, error = executor._parse_command_output("", "string")
        assert data is None
        assert "命令输出为空" in error
        
        data, error = executor._parse_command_output(None, "string")
        assert data is None
        assert "命令输出为空" in error
    
    def test_parse_command_output_unknown_type(self, executor):
        """测试未知数据类型解析"""
        data, error = executor._parse_command_output("test", "unknown_type")
        assert data == "test"
        assert error is None
    
    @pytest.mark.asyncio
    async def test_execute_monitor_items_batch(self, executor):
        """测试批量执行监控项"""
        # 创建模拟监控项
        monitor_items = [
            Mock(id=1, name="monitor1"),
            Mock(id=2, name="monitor2")
        ]
        
        # 创建模拟服务器
        servers = [
            {'ip': '*************', 'username': 'test'},
            {'ip': '*************', 'username': 'test'}
        ]
        
        with patch.object(executor, 'execute_monitor_item') as mock_execute:
            # 设置模拟 - 所有执行都成功
            mock_execute.return_value = (True, None, "test_data")
            
            # 执行批量测试
            results = await executor.execute_monitor_items_batch(
                monitor_items, servers, max_concurrent=2
            )
            
            # 验证结果
            assert len(results) == 2  # 两台服务器
            for server_ip in ['*************', '*************']:
                assert server_ip in results
                assert len(results[server_ip]) == 2  # 每台服务器两个监控项
                for monitor_id in [1, 2]:
                    assert monitor_id in results[server_ip]
                    assert results[server_ip][monitor_id]['success'] is True
    
    @pytest.mark.asyncio
    async def test_execute_monitor_items_batch_with_failures(self, executor):
        """测试批量执行监控项（包含失败）"""
        monitor_items = [Mock(id=1, name="monitor1")]
        servers = [{'ip': '*************', 'username': 'test'}]
        
        with patch.object(executor, 'execute_monitor_item') as mock_execute:
            # 设置模拟 - 执行失败
            mock_execute.return_value = (False, "Execution failed", None)
            
            # 执行批量测试
            results = await executor.execute_monitor_items_batch(
                monitor_items, servers
            )
            
            # 验证结果
            assert '*************' in results
            assert 1 in results['*************']
            assert results['*************'][1]['success'] is False
            assert results['*************'][1]['error'] == "Execution failed"
    
    def test_update_stats_success(self, executor):
        """测试成功执行的统计更新"""
        result = ExecutionResult(
            success=True,
            execution_time=1.5,
            retry_count=0
        )
        
        initial_total = executor.stats['total_executions']
        initial_success = executor.stats['successful_executions']
        
        executor._update_stats(result, 1.5)
        
        assert executor.stats['total_executions'] == initial_total + 1
        assert executor.stats['successful_executions'] == initial_success + 1
        assert executor.stats['average_execution_time'] > 0
    
    def test_update_stats_failure(self, executor):
        """测试失败执行的统计更新"""
        result = ExecutionResult(
            success=False,
            error="Command failed",
            execution_time=0.5,
            retry_count=2
        )
        
        initial_total = executor.stats['total_executions']
        initial_failed = executor.stats['failed_executions']
        initial_retry = executor.stats['retry_executions']
        
        executor._update_stats(result, 0.5)
        
        assert executor.stats['total_executions'] == initial_total + 1
        assert executor.stats['failed_executions'] == initial_failed + 1
        assert executor.stats['retry_executions'] == initial_retry + 1
    
    def test_update_stats_timeout(self, executor):
        """测试超时执行的统计更新"""
        result = ExecutionResult(
            success=False,
            error="Command timeout",
            execution_time=30.0,
            retry_count=0
        )
        
        initial_timeout = executor.stats['timeout_executions']
        
        executor._update_stats(result, 30.0)
        
        assert executor.stats['timeout_executions'] == initial_timeout + 1
    
    def test_get_stats(self, executor):
        """测试获取统计信息"""
        stats = executor.get_stats()
        
        required_keys = [
            'total_executions', 'successful_executions', 'failed_executions',
            'timeout_executions', 'retry_executions', 'total_execution_time',
            'average_execution_time', 'success_rate', 'ssh_executor_stats'
        ]
        
        for key in required_keys:
            assert key in stats
        
        assert isinstance(stats['success_rate'], (int, float))
        assert isinstance(stats['ssh_executor_stats'], dict)
    
    def test_reset_stats(self, executor):
        """测试重置统计信息"""
        # 先更新一些统计
        result = ExecutionResult(success=True, execution_time=1.0, retry_count=0)
        executor._update_stats(result, 1.0)
        
        # 验证统计不为零
        assert executor.stats['total_executions'] > 0
        
        # 重置统计
        executor.reset_stats()
        
        # 验证统计被重置
        assert executor.stats['total_executions'] == 0
        assert executor.stats['successful_executions'] == 0
        assert executor.stats['failed_executions'] == 0
        assert executor.stats['total_execution_time'] == 0.0
