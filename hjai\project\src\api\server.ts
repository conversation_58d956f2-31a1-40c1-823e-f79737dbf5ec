import api from './index'

export interface IPUser {
  id: number
  ip: string
  username: string
  is_deleted: boolean
  is_connectable: boolean
  use_ssh_key: boolean
  ssh_key_path?: string
}

export interface AddIPUserRequest {
  ip: string
  user: string
  password?: string
  use_ssh_key?: boolean
  ssh_key_path?: string
}

export interface UserListRequest {
  page: number
  page_size?: number
  is_deleted?: boolean
  is_connectable?: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  page_size: number
  pages: number
}

export const serverAPI = {
  // Get IP list
  getIPList: () => api.get<string[]>('/ip/list'),

  // Get user list with pagination
  getUserList: (params: UserListRequest) => 
    api.post<PaginatedResponse<IPUser>>('/ip/user-list', params),

  // Add new IP user
  addIPUser: (data: AddIPUserRequest) => 
    api.post('/ip/add', data),

  // Delete IP user (soft delete)
  deleteIPUser: (ip: string) => 
    api.delete(`/ip/${ip}`)
}