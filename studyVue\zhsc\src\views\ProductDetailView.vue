<template>
  <div class="product-detail-container">
    <!-- 返回顶部按钮 -->
    <van-icon name="arrow-left" class="back-icon" @click="goBack" />

    <!-- 商品轮播图 -->
    <div class="product-swiper">
      <van-swipe :autoplay="3000" indicator-color="#ff6b6b">
        <van-swipe-item v-for="(item, index) in product.images" :key="index">
          <img :src="item" class="swipe-img" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 商品基本信息 -->
    <div class="product-info">
      <div class="product-price">¥{{ product.price }}</div>
      <div class="product-name">{{ product.name }}</div>
      <div class="product-desc">{{ product.description }}</div>
      <div class="product-sales">销量 {{ product.sales }}+</div>
    </div>

    <!-- 商品规格 -->
    <div class="product-section">
      <div class="section-header">
        <span>商品规格</span>
        <van-icon name="arrow" />
      </div>
      <div class="spec-tags">
        <span v-for="(spec, index) in product.specs" :key="index" class="spec-tag">
          {{ spec }}
        </span>
      </div>
    </div>

    <!-- 商品评价 -->
    <div class="product-section">
      <div class="section-header">
        <span>商品评价 ({{ product.comments.length }})</span>
        <span class="view-all">查看全部<van-icon name="arrow" /></span>
      </div>

      <div v-if="product.comments.length > 0" class="comment-list">
        <div class="comment-item" v-for="(comment, index) in product.comments.slice(0, 2)" :key="index">
          <div class="comment-user">
            <img :src="comment.avatar" class="user-avatar" />
            <span class="user-name">{{ comment.username }}</span>
            <div class="comment-rate">
              <van-icon name="star" v-for="i in comment.rate" :key="i" color="#ff6b6b" />
              <van-icon name="star-o" v-for="i in 5-comment.rate" :key="i+5" color="#999" />
            </div>
          </div>
          <div class="comment-content">{{ comment.content }}</div>
          <div class="comment-time">{{ comment.time }}</div>
        </div>
      </div>
      <div v-else class="no-comment">暂无评价</div>
    </div>

    <!-- 商品详情 -->
    <div class="product-section">
      <div class="section-header">
        <span>商品详情</span>
      </div>
      <div class="product-detail-content" v-html="product.detail"></div>
    </div>

    <!-- 底部操作栏 -->
    <div class="product-actions">
      <div class="action-left">
        <div class="action-item" @click="goHome">
          <van-icon name="wap-home-o" />
          <span>首页</span>
        </div>
        <div class="action-item" @click="toggleFavorite">
          <van-icon :name="isFavorite ? 'like' : 'like-o'" :color="isFavorite ? '#ff6b6b' : ''" />
          <span>收藏</span>
        </div>
        <div class="action-item" @click="goToCart">
          <van-icon name="cart-o" :badge="cartCount > 0 ? cartCount : ''" />
          <span>购物车</span>
        </div>
      </div>
      <div class="action-right">
        <van-button type="warning" class="action-button" @click="addToCart">加入购物车</van-button>
        <van-button type="danger" class="action-button" @click="buyNow">立即购买</van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      productId: null,
      isFavorite: false,
      cartCount: 0,
      // 模拟商品数据，实际应从API获取
      product: {
        id: 1,
        name: '高品质商品名称 - 新款时尚精选',
        price: '299.00',
        originalPrice: '399.00',
        description: '这里是商品的简要描述，介绍商品的主要特点和亮点。',
        sales: 999,
        images: [
          'https://img01.yzcdn.cn/vant/apple-1.jpg',
          'https://img01.yzcdn.cn/vant/apple-2.jpg',
          'https://img01.yzcdn.cn/vant/apple-3.jpg'
        ],
        specs: ['默认规格', '精选款', '特惠装', '礼盒装'],
        comments: [
          {
            username: '用户***123',
            avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
            rate: 5,
            content: '商品质量非常好，物流很快，包装也很精美，很满意的一次购物！',
            time: '2023-12-15'
          },
          {
            username: '用户***456',
            avatar: 'https://img.yzcdn.cn/vant/dog.jpeg',
            rate: 4,
            content: '商品不错，性价比高，推荐购买。',
            time: '2023-12-10'
          }
        ],
        detail: `
          <div style="padding: 15px;">
            <p style="font-size: 14px; color: #333; margin-bottom: 10px;">商品详情内容</p>
            <img src="https://img01.yzcdn.cn/vant/apple-1.jpg" style="width: 100%; margin-bottom: 10px;" />
            <p style="font-size: 14px; color: #666; margin-bottom: 10px;">这是商品的详细介绍，包含商品的材质、尺寸、产地等信息。</p>
            <img src="https://img01.yzcdn.cn/vant/apple-2.jpg" style="width: 100%; margin-bottom: 10px;" />
            <p style="font-size: 14px; color: #666; margin-bottom: 10px;">这里可以介绍商品的使用方法、注意事项等。</p>
          </div>
        `
      }
    }
  },
  created () {
    // 从路由获取商品ID
    this.productId = this.$route.params.id
    // 如果有商品ID，则请求商品详情
    if (this.productId) {
      this.loadProductDetail()
    }
  },
  methods: {
    loadProductDetail () {
      // 这里应该调用API获取商品详情
      // 示例代码，实际应该替换为真实API调用
      /*
      service.get(`/index.php?s=/api/goods/detail`, {
        params: { goodsId: this.productId }
      })
        .then(response => {
          if (response.status === 200) {
            this.product = response.data
          } else {
            this.$toast('获取商品详情失败')
          }
        })
        .catch(error => {
          console.error('获取商品详情失败:', error)
          this.$toast('获取商品详情失败，请重试')
        })
      */
    },
    goBack () {
      this.$router.go(-1)
    },
    goHome () {
      this.$router.push('/home')
    },
    toggleFavorite () {
      this.isFavorite = !this.isFavorite
      this.$toast(this.isFavorite ? '已收藏' : '已取消收藏')
    },
    goToCart () {
      // 这里应该跳转到购物车页面
      this.$toast('前往购物车')
    },
    addToCart () {
      // 这里应该调用API将商品加入购物车
      this.cartCount++
      this.$toast('已加入购物车')
    },
    buyNow () {
      // 这里应该跳转到下单页面
      this.$toast('立即购买')
    }
  }
}
</script>

<style>
.product-detail-container {
  padding-bottom: 50px;
  background-color: #f5f7fa;
}

.back-icon {
  position: fixed;
  top: 15px;
  left: 15px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  font-size: 18px;
}

.product-swiper {
  height: 300px;
  background-color: #fff;
}

.swipe-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.product-price {
  font-size: 22px;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 8px;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.product-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

.product-section {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 10px;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.view-all {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.spec-tags {
  display: flex;
  flex-wrap: wrap;
}

.spec-tag {
  background-color: #f5f7fa;
  color: #666;
  padding: 5px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 15px;
  font-size: 12px;
}

.comment-list {
  margin-top: 10px;
}

.comment-item {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f2f2f2;
}

.comment-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 5px;
}

.user-name {
  font-size: 13px;
  color: #666;
  margin-right: 10px;
}

.comment-rate {
  margin-left: auto;
}

.comment-content {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.5;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.no-comment {
  text-align: center;
  color: #999;
  padding: 20px 0;
  font-size: 14px;
}

.product-detail-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.product-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #eee;
  z-index: 10;
}

.action-left {
  display: flex;
  flex: 4;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  color: #666;
}

.action-item .van-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.action-right {
  display: flex;
  flex: 6;
}

.action-button {
  flex: 1;
  height: 100%;
  font-size: 14px;
  border-radius: 0;
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .product-actions {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .product-detail-container {
    padding-bottom: calc(50px + env(safe-area-inset-bottom));
  }
}
</style>
