from fastapi import FastAPI
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import os
import time
from datetime import datetime
import pytz

# 导入新的时区处理模块
from config.timezone_utils import TZ
# 保持向后兼容
from config.timezone import now, utcnow, SHANGHAI_TZ

# 打印当前系统时间，确认时区设置
utc_now = TZ.now_utc()
shanghai_now = TZ.now_shanghai()
print(f"数据库模块加载时间 (UTC): {utc_now}")
print(f"数据库模块加载时间 (上海时区): {shanghai_now}")

# 数据库连接配置
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.mysql",
            "credentials": {
                "host": "localhost",
                "port": 3306,
                "user": "root",
                "password": "root",
                "database": "monitoring",
                "minsize": 1,
                "maxsize": 10,
                "charset": "utf8mb4",
                "echo": False,
                # 设置MySQL时区为UTC，在应用程序层处理时区转换
                "init_command": "SET time_zone='+00:00'; SET NAMES utf8mb4",
                "connect_timeout": 60,
                "sql_mode": "STRICT_TRANS_TABLES",
            }
        }
    },
    "apps": {
        "models": {
            "models": [
                "models.base_model",
                "models.ip_user", 
                "models.gpu_stats", 
                "models.memory_stats", 
                "models.network_stats", 
                "models.cpu_stats",
                "models.custom_monitor"
            ],
            "default_connection": "default",
        }
    },
    "use_tz": True,
    "timezone": "UTC"
}

async def init_db():
    """初始化数据库连接"""
    # 设置Tortoise使用UTC时区
    Tortoise.timezone = "UTC"
    
    # 初始化ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 设置会话时区并验证
    conn = Tortoise.get_connection("default")
    try:
        # 设置MySQL会话时区为UTC
        await conn.execute_query("SET GLOBAL time_zone = '+00:00'")
        await conn.execute_query("SET time_zone = '+00:00'")

        # 验证MySQL时区设置
        result = await conn.execute_query_dict("SELECT @@global.time_zone AS global_tz, @@session.time_zone AS session_tz, NOW() AS db_time")
        print(f"数据库时区设置 - 全局: {result[0]['global_tz']}, 会话: {result[0]['session_tz']}")
        print(f"数据库当前时间: {result[0]['db_time']}")

        # 使用新的时区工具验证时间一致性
        py_utc_now = TZ.now_utc().replace(tzinfo=None)  # 移除时区信息以便比较
        db_time = datetime.fromisoformat(str(result[0]['db_time']))
        time_diff = abs((py_utc_now - db_time).total_seconds())

        print(f"Python当前UTC时间: {py_utc_now}")
        print(f"Python时间与数据库时间差: {time_diff}秒")

        if time_diff > 5:
            print("⚠️  警告: Python时间与数据库时间差异较大，可能存在时区配置问题")
            print(f"Python UTC: {py_utc_now}")
            print(f"Database: {db_time}")
        else:
            print("✅ 数据库时区配置正确，时间同步正常")

    except Exception as e:
        print(f"❌ 设置数据库时区失败: {e}")
        print("请检查数据库连接和权限设置")
    
    # 生成数据库表结构（如果不存在）
    await Tortoise.generate_schemas(safe=False)

async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections()

def init_tortoise(app: FastAPI):
    """初始化Tortoise ORM"""
    # 设置Tortoise的时区为UTC
    Tortoise.timezone = "UTC"
    
    # 注册Tortoise
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=True,
        add_exception_handlers=True
    )