from tortoise import fields
from datetime import datetime
import pytz
from models.base_model import BaseModel, SHANGHAI_TZ, get_shanghai_now
from config.timezone import add_tz

class MemoryStats(BaseModel):
    """服务器内存监控数据"""
    
    ip = fields.CharField(max_length=50, description="服务器IP地址")
    total = fields.IntField(description="总内存(MB)")
    used = fields.IntField(description="已用内存(MB)")
    free = fields.IntField(description="空闲内存(MB)")
    available = fields.IntField(description="可用内存(MB)")
    usage_percent = fields.FloatField(description="内存使用百分比(%)")
    swap_total = fields.IntField(description="交换分区总量(MB)")
    swap_used = fields.IntField(description="交换分区已用(MB)")
    timestamp = fields.DatetimeField(description="记录时间")
    
    class Meta:
        table = "memory_stats"
        description = "内存监控数据"
    
    def __str__(self):
        return f"{self.ip} - {self.timestamp} - 使用率:{self.usage_percent}%"
        
    async def save(self, *args, **kwargs):
        """
        重写保存方法，使用新的时区处理策略
        """
        # 导入新的时区工具
        from config.timezone_utils import TZ

        # 如果timestamp未设置，使用当前UTC时间（带时区信息）
        if not self.timestamp:
            self.timestamp = TZ.now_utc()  # 保持UTC时区信息

        # 调用父类的save方法继续处理
        return await super().save(*args, **kwargs)

    def get_timestamp_display(self) -> str:
        """获取时间戳的显示格式（上海时区）"""
        if not self.timestamp:
            return None
        from config.timezone_utils import TZ
        # 如果timestamp已经有时区信息，直接转换；否则假设为UTC
        if self.timestamp.tzinfo is None:
            return TZ.to_display_format(self.timestamp, source_tz=TZ.UTC)
        else:
            return TZ.to_display_format(self.timestamp)