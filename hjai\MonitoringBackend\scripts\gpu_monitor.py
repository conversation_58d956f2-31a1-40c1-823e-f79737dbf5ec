import logging
import paramiko
import statistics
from typing import Dict, Any, List
from datetime import datetime
import pytz

from models.gpu_stats import GPUStatsSummary, GPUStatsDetail
from scripts.base_monitor import BaseMonitor
from config.settings import get_shanghai_time

# 获取日志记录器
logger = logging.getLogger(__name__)

class GPUMonitor(BaseMonitor):
    """GPU监控类"""
    
    def __init__(self):
        # 初始化，设置默认监控间隔为5分钟(300秒)
        super().__init__("GPU", 300)
    
    def get_gpu_info(self, ssh_client: paramiko.SSHClient) -> List[Dict[str, Any]]:
        """
        通过SSH执行nvidia-smi命令获取GPU信息
        
        Args:
            ssh_client: SSH客户端连接
            
        Returns:
            List[Dict[str, Any]]: GPU信息列表
        """
        try:
            # 执行nvidia-smi命令
            stdin, stdout, stderr = ssh_client.exec_command(
                "nvidia-smi --query-gpu=index,name,utilization.gpu,memory.used,memory.total,temperature.gpu "
                "--format=csv,noheader,nounits"
            )
            
            # 读取输出
            output = stdout.read().decode("utf-8").strip()
            error = stderr.read().decode("utf-8").strip()
            
            if error:
                logger.error(f"执行nvidia-smi出错: {error}")
                return []
            
            # 解析输出
            gpu_info = []
            for line in output.split("\n"):
                if line.strip():
                    parts = [part.strip() for part in line.split(",")]
                    if len(parts) >= 6:
                        # 计算内存使用百分比
                        memory_used = float(parts[3])
                        memory_total = float(parts[4])
                        memory_percent = (memory_used / memory_total * 100) if memory_total > 0 else 0
                        
                        gpu_info.append({
                            "gpu_index": int(parts[0]),
                            "gpu_name": parts[1],
                            "gpu_usage": float(parts[2]),
                            "gpu_memory_used": memory_used,
                            "gpu_memory_total": memory_total,
                            "gpu_memory_percent": memory_percent,
                            "gpu_temperature": float(parts[5])
                        })
                        
            return gpu_info
        except Exception as e:
            logger.error(f"获取GPU信息失败: {str(e)}")
            return []
    
    async def save_gpu_stats(self, ip: str, gpu_info: List[Dict[str, Any]]) -> bool:
        """
        保存GPU统计数据到数据库
        
        Args:
            ip: 服务器IP
            gpu_info: GPU信息列表
            
        Returns:
            bool: 是否保存成功
        """
        if not gpu_info:
            logger.warning(f"没有GPU数据可保存: {ip}")
            return False
        
        try:
            # 计算摘要数据
            avg_usage = statistics.mean([gpu["gpu_usage"] for gpu in gpu_info])
            total_memory_used = sum([gpu["gpu_memory_used"] for gpu in gpu_info])
            total_memory_total = sum([gpu["gpu_memory_total"] for gpu in gpu_info])
            memory_usage_percent = (total_memory_used / total_memory_total * 100) if total_memory_total > 0 else 0
            avg_temperature = statistics.mean([gpu["gpu_temperature"] for gpu in gpu_info])
            
            # 获取当前UTC时间用于数据库存储
            from config.timezone_utils import TZ
            current_time = TZ.now_utc()  # 保持UTC时区信息
            
            # 创建摘要记录
            summary = await GPUStatsSummary.create(
                ip=ip,
                total_gpu_count=len(gpu_info),
                avg_usage=avg_usage,
                total_memory_used=total_memory_used,
                total_memory_total=total_memory_total,
                memory_usage_percent=memory_usage_percent,
                avg_temperature=avg_temperature,
                timestamp=current_time  # 手动设置时间戳
            )
            
            # 创建详细记录
            for gpu in gpu_info:
                await GPUStatsDetail.create(
                    summary=summary,
                    gpu_index=gpu["gpu_index"],
                    gpu_name=gpu["gpu_name"],
                    gpu_usage=gpu["gpu_usage"],
                    gpu_memory_used=gpu["gpu_memory_used"],
                    gpu_memory_total=gpu["gpu_memory_total"],
                    gpu_memory_percent=gpu["gpu_memory_percent"],
                    gpu_temperature=gpu["gpu_temperature"]
                )
                
            logger.debug(f"成功保存服务器 {ip} 的GPU统计数据到数据库")
            return True
        except Exception as e:
            logger.error(f"保存GPU统计数据失败: {str(e)}")
            return False
    
    async def start_monitoring(self, interval_seconds: int = None) -> None:
        """
        开始定时监控所有服务器GPU
        
        Args:
            interval_seconds: 监控间隔，单位秒，默认为self.default_interval(300秒)
        """
        # 使用基类的通用监控启动方法
        await super().start_monitoring(
            data_collector=self.get_gpu_info,
            data_saver=self.save_gpu_stats,
            only_connectable=True,
            interval_seconds=interval_seconds
        )

# 为保持向后兼容性添加的函数
async def start_monitoring(interval_seconds: int = 300) -> None:
    """
    开始定时监控所有服务器GPU（兼容旧版API）
    
    Args:
        interval_seconds: 监控间隔，单位秒，默认300秒(5分钟)
    """
    logger.info("通过兼容函数启动GPU监控服务")
    monitor = GPUMonitor()
    await monitor.start_monitoring(interval_seconds) 