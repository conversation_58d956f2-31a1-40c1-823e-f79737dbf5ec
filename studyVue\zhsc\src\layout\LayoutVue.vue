<template>
  <div>
    <router-view />
    <van-tabbar v-model="active" route>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/user" icon="user-o">用户</van-tabbar-item>
      <van-tabbar-item to="/login" icon="friends-o">登录</van-tabbar-item>
    </van-tabbar>
  </div>
</template>
<script>
export default {
  data () {
    return {
      active: 0
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler (to) {
        // 根据路由自动设置激活的标签
        if (to.path === '/home') this.active = 0
        else if (to.path === '/user') this.active = 1
        else if (to.path === '/login') this.active = 2
      }
    }
  }
}
</script>
