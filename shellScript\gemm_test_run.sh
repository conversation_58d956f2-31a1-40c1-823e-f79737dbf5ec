#!/bin/bash

# 结果文件
RESULT_FILE="bench_results.txt"
FINAL_TABLE="result.md"

# 检查CUDA环境
check_cuda_env() {
    echo "检查CUDA环境..." | tee -a "$RESULT_FILE"
    
    # 检查CUDA库路径
    if [ ! -f "/usr/local/cuda/lib64/libcublasLt.so.12" ] && [ ! -f "/usr/lib/x86_64-linux-gnu/libcublasLt.so.12" ]; then
        echo "警告: 找不到libcublasLt.so.12库文件，尝试设置LD_LIBRARY_PATH" | tee -a "$RESULT_FILE"
        
        # 尝试查找库文件位置 - 先尝试常见的CUDA安装路径
        for path in "/usr/local/cuda/lib64" "/usr/local/cuda-*/lib64" "/opt/cuda/lib64" "/opt/nvidia/cuda/lib64"; do
            if ls $path/libcublasLt.so* 1>/dev/null 2>&1; then
                CUDA_LIB_PATH=$path
                echo "找到CUDA库路径: $CUDA_LIB_PATH，添加到LD_LIBRARY_PATH" | tee -a "$RESULT_FILE"
                break
            fi
        done
        
        # 如果常见路径没找到，尝试全局搜索
        if [ -z "$CUDA_LIB_PATH" ]; then
            echo "在常见路径未找到库文件，尝试全局搜索（可能需要一些时间）..." | tee -a "$RESULT_FILE"
            CUDA_LIB_PATH=$(find /usr -name "libcublasLt.so*" 2>/dev/null | head -n 1 | xargs dirname 2>/dev/null)
        fi
        
        if [ -n "$CUDA_LIB_PATH" ]; then
            echo "找到CUDA库路径: $CUDA_LIB_PATH，添加到LD_LIBRARY_PATH" | tee -a "$RESULT_FILE"
            export LD_LIBRARY_PATH=$CUDA_LIB_PATH:$LD_LIBRARY_PATH
            
            # 验证库文件是否可用
            if ! ls $CUDA_LIB_PATH/libcublasLt.so* 1>/dev/null 2>&1; then
                echo "错误: 虽然找到了CUDA库路径，但无法访问库文件。请检查权限。" | tee -a "$RESULT_FILE"
                return 1
            fi
        else
            echo "错误: 无法找到CUDA库文件。请确保已安装CUDA工具包。" | tee -a "$RESULT_FILE"
            echo "解决方案:" | tee -a "$RESULT_FILE"
            echo "1. 安装CUDA工具包: 'sudo apt-get install cuda-toolkit' 或 'sudo apt install nvidia-cuda-toolkit'" | tee -a "$RESULT_FILE"
            echo "2. 如果已安装CUDA，请确保库文件路径已添加到系统路径:" | tee -a "$RESULT_FILE"
            echo "   - 在/etc/ld.so.conf.d/中添加CUDA库路径" | tee -a "$RESULT_FILE"
            echo "   - 运行'sudo ldconfig'更新库缓存" | tee -a "$RESULT_FILE"
            echo "3. 或者手动设置环境变量: 'export LD_LIBRARY_PATH=/path/to/cuda/lib64:\$LD_LIBRARY_PATH'" | tee -a "$RESULT_FILE"
            return 1
        fi
    else
        # 设置标准CUDA库路径
        if [ -d "/usr/local/cuda/lib64" ]; then
            export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH
            echo "已添加标准CUDA库路径: /usr/local/cuda/lib64" | tee -a "$RESULT_FILE"
        fi
        if [ -d "/usr/lib/x86_64-linux-gnu" ]; then
            export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH
            echo "已添加系统库路径: /usr/lib/x86_64-linux-gnu" | tee -a "$RESULT_FILE"
        fi
    fi
    
    # 打印当前LD_LIBRARY_PATH以便调试
    echo "当前LD_LIBRARY_PATH: $LD_LIBRARY_PATH" | tee -a "$RESULT_FILE"
    
    return 0
}

# 清空旧文件
> "$RESULT_FILE"
> "$FINAL_TABLE"

# 预定义测试类型顺序
TEST_TYPES=("FP8" "INT8" "FP16" "BF16" "TF32" "FP32")

# 运行所有测试
run_benchmarks() {
    # 检查可执行文件是否存在
    if [ ! -f "./cublasMatmulBench" ] || [ ! -x "./cublasMatmulBench" ]; then
        echo "错误: cublasMatmulBench 可执行文件不存在或没有执行权限" | tee -a "$RESULT_FILE"
        return 1
    fi
    
    # 创建运行命令的函数，确保每次都使用正确的环境变量
    run_cmd() {
        # 确保每次运行命令时都使用正确的库路径
        LD_LIBRARY_PATH=$LD_LIBRARY_PATH ./cublasMatmulBench "$@" | tee -a "$RESULT_FILE"
        return ${PIPESTATUS[0]}
    }
    
    echo "Running FP8 GEMM..." | tee -a "$RESULT_FILE"
    run_cmd -P=qqssq -m=15360 -n=18176 -k=8192 -T=1000 -ta=1 -B=0 -p=0
    
    echo "Running INT8 GEMM..." | tee -a "$RESULT_FILE"
    run_cmd -P=bisb_imma -m=40960 -n=52548 -k=16384 -T=1000 -ta=1 -B=0 -p=0
    
    echo "Running FP16 GEMM..." | tee -a "$RESULT_FILE"
    run_cmd -P=hsh -m=15360 -n=18176 -k=8192 -T=1000 -tb=1 -B=0 -p=0
    
    echo "Running BF16 GEMM..." | tee -a "$RESULT_FILE"
    run_cmd -P=tst -m=15360 -n=18176 -k=16384 -T=1000 -tb=1 -B=0 -p=0
    
    echo "Running TF32 GEMM..." | tee -a "$RESULT_FILE"
    run_cmd -P=sss_fast_tf32 -m=15360 -n=18176 -k=4096 -T=1000 -tb=1 -B=0 -p=0
    
    echo "Running FP32 GEMM..." | tee -a "$RESULT_FILE"
    run_cmd -P=sss -m=15360 -n=18176 -k=4096 -T=1000 -tb=1 -B=0 -p=0
    
    return 0
}

# 结果解析与表格生成
generate_report() {
    # 生成表格头
    echo "| 测试类型 | 矩阵尺寸 (M×N×K) | 实测性能 (TFLOPS) |" > "$FINAL_TABLE"
    echo "|----------|-------------------|--------------------|" >> "$FINAL_TABLE"

    # 提取结果数据
    declare -A results
    current_type_index=0
    
    while read -r line; do
        # 匹配测试类型头
        if [[ $line == "Running"*"GEMM..." ]]; then
            current_type=${TEST_TYPES[$current_type_index]}
            ((current_type_index++))
        fi
        
        # 匹配性能结果行
        if [[ $line =~ "CUDA : elapsed" ]]; then
            # 从当前测试类型对应的命令行中提取矩阵尺寸
            case "$current_type" in
                "FP8")
                    dimensions="15360×18176×8192"
                    ;;
                "INT8")
                    dimensions="40960×52548×16384"
                    ;;
                "FP16")
                    dimensions="15360×18176×8192"
                    ;;
                "BF16")
                    dimensions="15360×18176×16384"
                    ;;
                "TF32")
                    dimensions="15360×18176×4096"
                    ;;
                "FP32")
                    dimensions="15360×18176×4096"
                    ;;
            esac
            
            # 提取Gflops并转换
            gflops=$(grep -oP 'Gflops = \K[\d.]+' <<< "$line")
            tflops=$(bc <<< "scale=2; $gflops/1000")
            
            # 存储结果
            results[$current_type]="$dimensions|$tflops"
        fi
    done < "$RESULT_FILE"

    # 填充表格
    for type in "${TEST_TYPES[@]}"; do
        data=${results[$type]}
        if [[ -n $data ]]; then
            dim=$(cut -d'|' -f1 <<< "$data")
            perf=$(cut -d'|' -f2 <<< "$data")
            printf "| %-8s | %-17s | %-18s |\n" "$type" "$dim" "$perf" >> "$FINAL_TABLE"
        else
            printf "| %-8s | %-17s | %-18s |\n" "$type" "N/A" "N/A" >> "$FINAL_TABLE"
        fi
    done

    echo "测试结果已保存到 $FINAL_TABLE"
}

# 执行主流程
if check_cuda_env; then
    echo "CUDA环境检查通过，开始运行测试..." | tee -a "$RESULT_FILE"
    run_benchmarks
    generate_report
    
    # 打印结果
    column -t -s '|' -o ' | ' "$FINAL_TABLE"  # 格式化输出
else
    echo "错误: CUDA环境检查失败，无法运行测试。" | tee -a "$RESULT_FILE"
    echo "请确保CUDA工具包已正确安装，并且库文件路径已正确设置。" | tee -a "$RESULT_FILE"
    
    # 生成错误报告
    echo "| 测试类型 | 矩阵尺寸 (M×N×K) | 实测性能 (TFLOPS) |" > "$FINAL_TABLE"
    echo "|----------|-------------------|--------------------|" >> "$FINAL_TABLE"
    for type in "${TEST_TYPES[@]}"; do
        printf "| %-8s | %-17s | %-18s |\n" "$type" "环境错误" "N/A" >> "$FINAL_TABLE"
    done
    
    # 打印结果
    column -t -s '|' -o ' | ' "$FINAL_TABLE"  # 格式化输出
fi
