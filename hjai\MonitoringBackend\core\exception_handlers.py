"""
异常处理模块

本模块包含应用程序的异常处理器，用于统一处理各种异常并返回标准格式的响应。
"""

import logging
from fastapi import Request, status, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError

# 获取日志记录器
logger = logging.getLogger(__name__)

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理请求验证错误，返回统一格式的响应
    
    Args:
        request: 请求对象
        exc: 验证错误异常
        
    Returns:
        JSONResponse: 统一格式的错误响应
    """
    # 格式化验证错误信息
    error_messages = []
    for error in exc.errors():
        loc = " -> ".join(str(x) for x in error["loc"] if x != "body")
        msg = error["msg"]
        error_messages.append(f"{loc}: {msg}")
    
    error_message = "；".join(error_messages)
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "message": f"请求数据验证失败: {error_message}"
        }
    )

async def http_exception_handler(request: Request, exc: HTTPException):
    """
    处理HTTPException异常，返回统一格式的响应
    
    Args:
        request: 请求对象
        exc: HTTP异常
        
    Returns:
        JSONResponse: 统一格式的错误响应
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail
        }
    )

async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理器，确保请求始终能得到响应
    
    Args:
        request: 请求对象
        exc: 异常
        
    Returns:
        JSONResponse: 统一格式的错误响应
    """
    # 记录错误
    logger.error(f"处理请求时出错: {str(exc)}")
    
    # 返回友好的错误信息，使用统一的响应格式
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    return JSONResponse(
        status_code=status_code,
        content={
            "code": status_code,
            "message": "服务器处理请求时出错，请稍后再试"
        }
    ) 