"""
应用配置模块

本模块包含应用程序的配置参数。
"""

import os
import pytz
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 应用基本信息
APP_TITLE = "监控后端API"
APP_DESCRIPTION = "航锦云监控系统"
APP_VERSION = "1.0.0"
APP_DOCS_URL = "/docs"
APP_REDOC_URL = "/redoc"

# 时区设置
TIMEZONE = "Asia/Shanghai"
os.environ["TZ"] = TIMEZONE

# 监控系统配置
CONNECTIVITY_INTERVAL = 3600  # 每1小时检查一次连通性
RESOURCE_INTERVAL = 300       # 每5分钟监控一次资源

# CORS配置
CORS_ORIGINS = ["*"]  # 允许所有源，生产环境中应该限制为特定的前端域
CORS_CREDENTIALS = True
CORS_METHODS = ["*"]  # 允许所有HTTP方法
CORS_HEADERS = ["*"]  # 允许所有HTTP头部

def setup_app_config(app: FastAPI):
    """
    设置应用配置
    
    Args:
        app: FastAPI应用实例
    """
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=CORS_ORIGINS,
        allow_credentials=CORS_CREDENTIALS,
        allow_methods=CORS_METHODS,
        allow_headers=CORS_HEADERS,
    ) 