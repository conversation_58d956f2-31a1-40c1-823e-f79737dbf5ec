"""
Uvicorn配置模块

本模块包含Uvicorn服务器的配置参数。
"""

from config.logging_config import UVICORN_LOG_CONFIG

# Uvicorn服务器配置
HOST = "*************"  # 绑定到所有网络接口，允许外部访问
PORT = 8000
LIFESPAN = "on"  # 显式启用lifespan模式
TIMEOUT_KEEP_ALIVE = 5  # 减少保持活动连接的超时时间，更快响应
LOG_CONFIG = UVICORN_LOG_CONFIG

def get_uvicorn_config():
    """
    获取Uvicorn配置
    
    Returns:
        dict: Uvicorn配置字典
    """
    return {
        "host": HOST,
        "port": PORT,
        "lifespan": LIFESPAN,
        "timeout_keep_alive": TIMEOUT_KEEP_ALIVE,
        "log_config": LOG_CONFIG
    } 