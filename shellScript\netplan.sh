#!/bin/bash

# 交互式 Netplan 配置脚本
# 此脚本用于修改 Ubuntu 系统的 Netplan 配置

# 重置终端设置，确保退格键正常工作
stty sane
stty erase ^H

# 定义配置文件路径
NETPLAN_CONFIG="/etc/netplan/90-NM-14f59568-5076-387a-aef6-10adfcca2e26.yaml"

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
  echo "请以 root 权限运行此脚本"
  exit 1
fi

# 检查配置文件路径是否存在
if [ ! -f "$NETPLAN_CONFIG" ]; then
  echo "错误: Netplan 配置文件不存在: $NETPLAN_CONFIG"
  echo "请检查配置文件路径是否正确"
  exit 1
fi

# 检查配置文件是否可读写
if [ ! -r "$NETPLAN_CONFIG" ] || [ ! -w "$NETPLAN_CONFIG" ]; then
  echo "错误: 无法读写 Netplan 配置文件: $NETPLAN_CONFIG"
  echo "请检查文件权限"
  exit 1
fi


# 备份当前配置
backup_config() {
  local backup_file="${NETPLAN_CONFIG}.bak.$(date +%Y%m%d%H%M%S)"
  cp "$NETPLAN_CONFIG" "$backup_file"
  echo "已备份当前配置到: $backup_file"
}

# 显示当前配置
show_current_config() {
  echo "当前 Netplan 配置:"
  echo "-------------------"
  cat "$NETPLAN_CONFIG"
  echo "-------------------"
}

# 修改网络接口配置
modify_interface() {
  echo "请选择要修改的接口类型:"
  echo "1) 以太网接口"
  echo "2) 绑定接口"
  read -p "请输入选项 [1-2]: " interface_type
  
  case $interface_type in
    1)
      modify_ethernet_interface
      ;;
    2)
      modify_bond_interface
      ;;
    *)
      echo "无效选项"
      ;;
  esac
}

# 修改以太网接口
modify_ethernet_interface() {
  # 获取当前配置的以太网接口列表
  local interfaces=$(grep -A1 "ethernets:" "$NETPLAN_CONFIG" | grep -v "ethernets:" | awk '{print $1}' | sed 's/://g')
  
  echo "当前配置的以太网接口:"
  local i=1
  for iface in $interfaces; do
    echo "$i) $iface"
    i=$((i+1))
  done
  echo "$i) 添加新接口"
  
  read -p "请选择要修改的接口 [1-$i]: " iface_choice
  
  if [ "$iface_choice" -eq "$i" ]; then
    # 添加新接口
    read -p "请输入新接口名称: " new_iface
    read -p "是否启用 DHCP? (y/n): " dhcp_choice
    
    if [ "$dhcp_choice" = "y" ]; then
      sed -i "/ethernets:/a\\    $new_iface:\\n      dhcp4: true" "$NETPLAN_CONFIG"
    else
      read -p "请输入 IP 地址 (格式: *************/24): " ip_addr
      read -p "请输入网关 IP: " gateway
      read -p "请输入 DNS 服务器 (用逗号分隔): " dns_servers
      
      # 格式化 DNS 服务器列表
      dns_formatted=""
      IFS=',' read -ra DNS_ARRAY <<< "$dns_servers"
      for dns in "${DNS_ARRAY[@]}"; do
        dns_formatted="$dns_formatted        - $dns\\n"
      done
      
      sed -i "/ethernets:/a\\    $new_iface:\\n      dhcp4: false\\n      addresses: [$ip_addr]\\n      gateway4: $gateway\\n      nameservers:\\n        addresses:\\n$dns_formatted" "$NETPLAN_CONFIG"
    fi
  else
    # 修改现有接口
    local selected_iface=$(echo "$interfaces" | sed -n "${iface_choice}p")
    echo "修改接口: $selected_iface"
    
    read -p "是否启用 DHCP? (y/n): " dhcp_choice
    
    if [ "$dhcp_choice" = "y" ]; then
      sed -i "/$selected_iface:/,/^\s*[a-z]/ s/dhcp4:.*/dhcp4: true/" "$NETPLAN_CONFIG"
      # 删除静态 IP 配置
      sed -i "/$selected_iface:/,/^\s*[a-z]/ {/addresses:/d; /gateway4:/d; /nameservers:/d;}" "$NETPLAN_CONFIG"
    else
      read -p "请输入 IP 地址 (格式: *************/24): " ip_addr
      read -p "请输入网关 IP: " gateway
      read -p "请输入 DNS 服务器 (用逗号分隔): " dns_servers
      
      # 更新 DHCP 设置
      sed -i "/$selected_iface:/,/^\s*[a-z]/ s/dhcp4:.*/dhcp4: false/" "$NETPLAN_CONFIG"
      
      # 检查是否已有 addresses 配置
      if grep -q "$selected_iface:.*addresses:" "$NETPLAN_CONFIG" -A5; then
        sed -i "/$selected_iface:/,/^\s*[a-z]/ s/addresses:.*/addresses: [$ip_addr]/" "$NETPLAN_CONFIG"
      else
        sed -i "/$selected_iface:/a\\      addresses: [$ip_addr]" "$NETPLAN_CONFIG"
      fi
      
      # 更新网关
      if grep -q "$selected_iface:.*gateway4:" "$NETPLAN_CONFIG" -A5; then
        sed -i "/$selected_iface:/,/^\s*[a-z]/ s/gateway4:.*/gateway4: $gateway/" "$NETPLAN_CONFIG"
      else
        sed -i "/$selected_iface:/a\\      gateway4: $gateway" "$NETPLAN_CONFIG"
      fi
      
      # 更新 DNS
      if grep -q "$selected_iface:.*nameservers:" "$NETPLAN_CONFIG" -A5; then
        # 删除旧的 DNS 配置
        sed -i "/$selected_iface:/,/^\s*[a-z]/ {/nameservers:/,/^\s*[a-z]/ d}" "$NETPLAN_CONFIG"
      fi
      
      # 格式化 DNS 服务器列表
      dns_formatted=""
      IFS=',' read -ra DNS_ARRAY <<< "$dns_servers"
      for dns in "${DNS_ARRAY[@]}"; do
        dns_formatted="$dns_formatted        - $dns"
      done
      
      # 添加新的 DNS 配置
      sed -i "/$selected_iface:/a\\      nameservers:\\n        addresses:\\n$dns_formatted" "$NETPLAN_CONFIG"
    fi
  fi
  
  # 询问是否立即应用配置
  read -p "是否立即应用网络配置? (y/n): " apply_now
  if [ "$apply_now" = "y" ] || [ "$apply_now" = "Y" ]; then
    apply_config
  else
    echo "配置已保存，但尚未应用。您可以稍后通过主菜单中的'应用配置'选项应用更改。"
  fi
}

# 修改绑定接口
modify_bond_interface() {
  # 检查是否已有绑定接口配置
  if grep -q "bonds:" "$NETPLAN_CONFIG"; then
    # 获取当前配置的绑定接口列表
    local bonds=$(grep -A1 "bonds:" "$NETPLAN_CONFIG" | grep -v "bonds:" | awk '{print $1}' | sed 's/://g')
    
    echo "当前配置的绑定接口:"
    local i=1
    for bond in $bonds; do
      echo "$i) $bond"
      i=$((i+1))
    done
    echo "$i) 添加新绑定接口"
    
    read -p "请选择要修改的绑定接口 [1-$i]: " bond_choice
    
    if [ "$bond_choice" -eq "$i" ]; then
      # 添加新绑定接口
      add_new_bond
    else
      # 修改现有绑定接口
      local selected_bond=$(echo "$bonds" | sed -n "${bond_choice}p")
      modify_existing_bond "$selected_bond"
    fi
  else
    echo "当前没有配置绑定接口，将添加新的绑定接口"
    # 在配置文件末尾添加 bonds 部分
    echo "  bonds:" >> "$NETPLAN_CONFIG"
    add_new_bond
  fi
}

# 添加新的绑定接口
add_new_bond() {
  read -p "请输入新绑定接口名称 (例如: bond0): " bond_name
  
  # 获取可用的以太网接口
  local interfaces=$(grep -A1 "ethernets:" "$NETPLAN_CONFIG" | grep -v "ethernets:" | awk '{print $1}' | sed 's/://g')
  
  echo "可用的以太网接口:"
  local i=1
  for iface in $interfaces; do
    echo "$i) $iface"
    i=$((i+1))
  done
  
  read -p "请选择要绑定的接口 (用逗号分隔多个选项): " iface_choices
  
  # 解析选择的接口
  selected_ifaces=""
  IFS=',' read -ra IFACE_ARRAY <<< "$iface_choices"
  for choice in "${IFACE_ARRAY[@]}"; do
    if [ -n "$selected_ifaces" ]; then
      selected_ifaces="$selected_ifaces, "
    fi
    selected_ifaces="$selected_ifaces$(echo "$interfaces" | sed -n "${choice}p")"
  done
  
  read -p "请输入 IP 地址 (格式: *************/24): " ip_addr
  read -p "请输入网关 IP: " gateway
  read -p "请输入 DNS 服务器 (用逗号分隔): " dns_servers
  
  # 绑定模式选择
  echo "请选择绑定模式:"
  echo "1) balance-rr (轮询)"
  echo "2) active-backup (主备)"
  echo "3) balance-xor (XOR)"
  echo "4) broadcast (广播)"
  echo "5) 802.3ad (LACP)"
  echo "6) balance-tlb (适配器传输负载均衡)"
  echo "7) balance-alb (适配器负载均衡)"
  read -p "请选择绑定模式 [1-7]: " mode_choice
  
  case $mode_choice in
    1) bond_mode="balance-rr" ;;
    2) bond_mode="active-backup" ;;
    3) bond_mode="balance-xor" ;;
    4) bond_mode="broadcast" ;;
    5) bond_mode="802.3ad" ;;
    6) bond_mode="balance-tlb" ;;
    7) bond_mode="balance-alb" ;;
    *) bond_mode="802.3ad" ;;
  esac
  
  # 选择传输哈希策略
  echo "请选择传输哈希策略 (适用于 balance-xor, 802.3ad, balance-tlb 模式):"
  echo "1) layer2 (基于 MAC 地址)"
  echo "2) layer3+4 (基于 IP 地址和端口)"
  echo "3) layer2+3 (基于 MAC 和 IP 地址)"
  read -p "请选择传输哈希策略 [1-3]: " hash_choice
  
  case $hash_choice in
    1) hash_policy="layer2" ;;
    2) hash_policy="layer3+4" ;;
    3) hash_policy="layer2+3" ;;
    *) hash_policy="layer3+4" ;;
  esac
  
  # 格式化 DNS 服务器列表
  dns_formatted=""
  IFS=',' read -ra DNS_ARRAY <<< "$dns_servers"
  for dns in "${DNS_ARRAY[@]}"; do
    dns_formatted="$dns_formatted          - $dns\\n"
  done
  
  # 添加绑定接口配置
  if ! grep -q "bonds:" "$NETPLAN_CONFIG"; then
    echo "  bonds:" >> "$NETPLAN_CONFIG"
  fi
  
  sed -i "/bonds:/a\\    $bond_name:\\n      interfaces: [$selected_ifaces]\\n      addresses: [$ip_addr]\\n      routes:\\n        - to: default\\n          via: $gateway\\n      nameservers:\\n        addresses:\\n$dns_formatted      parameters:\\n        mode: $bond_mode\\n        transmit-hash-policy: $hash_policy" "$NETPLAN_CONFIG"
  
  # 询问是否立即应用配置
  read -p "是否立即应用网络配置? (y/n): " apply_now
  if [ "$apply_now" = "y" ] || [ "$apply_now" = "Y" ]; then
    apply_config
  else
    echo "配置已保存，但尚未应用。您可以稍后通过主菜单中的'应用配置'选项应用更改。"
  fi
}

# 修改现有绑定接口
modify_existing_bond() {
  local bond_name=$1
  echo "修改绑定接口: $bond_name"
  
  # 获取可用的以太网接口
  local interfaces=$(grep -A1 "ethernets:" "$NETPLAN_CONFIG" | grep -v "ethernets:" | awk '{print $1}' | sed 's/://g')
  
  echo "可用的以太网接口:"
  local i=1
  for iface in $interfaces; do
    echo "$i) $iface"
    i=$((i+1))
  done
  
  read -p "请选择要绑定的接口 (用逗号分隔多个选项): " iface_choices
  
  # 解析选择的接口
  selected_ifaces=""
  IFS=',' read -ra IFACE_ARRAY <<< "$iface_choices"
  for choice in "${IFACE_ARRAY[@]}"; do
    if [ -n "$selected_ifaces" ]; then
      selected_ifaces="$selected_ifaces, "
    fi
    selected_ifaces="$selected_ifaces$(echo "$interfaces" | sed -n "${choice}p")"
  done
  
  read -p "请输入 IP 地址 (格式: *************/24): " ip_addr
  read -p "请输入网关 IP: " gateway
  read -p "请输入 DNS 服务器 (用逗号分隔): " dns_servers
  
  # 绑定模式选择
  echo "请选择绑定模式:"
  echo "1) balance-rr (轮询)"
  echo "2) active-backup (主备)"
  echo "3) balance-xor (XOR)"
  echo "4) broadcast (广播)"
  echo "5) 802.3ad (LACP)"
  echo "6) balance-tlb (适配器传输负载均衡)"
  echo "7) balance-alb (适配器负载均衡)"
  read -p "请选择绑定模式 [1-7]: " mode_choice
  
  case $mode_choice in
    1) bond_mode="balance-rr" ;;
    2) bond_mode="active-backup" ;;
    3) bond_mode="balance-xor" ;;
    4) bond_mode="broadcast" ;;
    5) bond_mode="802.3ad" ;;
    6) bond_mode="balance-tlb" ;;
    7) bond_mode="balance-alb" ;;
    *) bond_mode="802.3ad" ;;
  esac
  
  # 选择传输哈希策略
  echo "请选择传输哈希策略 (适用于 balance-xor, 802.3ad, balance-tlb 模式):"
  echo "1) layer2 (基于 MAC 地址)"
  echo "2) layer3+4 (基于 IP 地址和端口)"
  echo "3) layer2+3 (基于 MAC 和 IP 地址)"
  read -p "请选择传输哈希策略 [1-3]: " hash_choice
  
  case $hash_choice in
    1) hash_policy="layer2" ;;
    2) hash_policy="layer3+4" ;;
    3) hash_policy="layer2+3" ;;
    *) hash_policy="layer3+4" ;;
  esac
  
  # 更新绑定接口配置
  # 首先删除旧的配置
  sed -i "/$bond_name:/,/^\s*[a-z]/ d" "$NETPLAN_CONFIG"
  
  # 格式化 DNS 服务器列表
  dns_formatted=""
  IFS=',' read -ra DNS_ARRAY <<< "$dns_servers"
  for dns in "${DNS_ARRAY[@]}"; do
    dns_formatted="$dns_formatted          - $dns\\n"
  done
  
  # 添加新的配置
  sed -i "/bonds:/a\\    $bond_name:\\n      interfaces: [$selected_ifaces]\\n      addresses: [$ip_addr]\\n      routes:\\n        - to: default\\n          via: $gateway\\n      nameservers:\\n        addresses:\\n$dns_formatted      parameters:\\n        mode: $bond_mode\\n        transmit-hash-policy: $hash_policy" "$NETPLAN_CONFIG"
}

# 应用配置
apply_config() {
  echo "正在应用 Netplan 配置..."
  netplan try || {
    echo "配置验证失败，请检查配置文件"
    return 1
  }
  netplan apply
  echo "配置已成功应用"
}

# 主菜单
main_menu() {
  while true; do
    echo ""
    echo "Netplan 配置工具"
    echo "================="
    echo "1) 显示当前配置"
    echo "2) 备份当前配置"
    echo "3) 修改网络接口"
    echo "4) 应用配置"
    echo "0) 退出"
    
    read -p "请选择操作 [0-4]: " choice
    
    case $choice in
      1)
        show_current_config
        ;;
      2)
        backup_config
        ;;
      3)
        modify_interface
        ;;
      4)
        apply_config
        ;;
      0)
        echo "退出程序"
        exit 0
        ;;
      *)
        echo "无效选项，请重新选择"
        ;;
    esac
  done
}

# 启动脚本时先显示当前配置
echo "欢迎使用 Netplan 配置工具"
echo "=========================="
show_current_config

# 询问用户是否需要修改配置
read -p "是否需要修改网络配置? (y/n): " modify_choice

if [ "$modify_choice" = "y" ] || [ "$modify_choice" = "Y" ]; then
  # 如果需要修改，进入主菜单
  main_menu
else
  # 如果不需要修改，直接退出
  echo "未进行任何修改，退出程序"
  exit 0
fi

# 启动主菜单（这行将被上面的代码替代）
# main_menu