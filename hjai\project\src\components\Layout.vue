<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMenu, ElMenuItem, ElIcon, ElHeader, ElAside, ElMain, ElContainer } from 'element-plus'
import {
  Monitor,
  Setting,
  DataAnalysis,
  Operation,
  Checked,
  Platform
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const collapsed = ref(false)

const menuItems = [
  {
    index: '/',
    title: '监控概览',
    icon: Monitor
  },
  {
    index: '/servers',
    title: '服务器管理',
    icon: Platform
  },
  {
    index: '/monitor-items',
    title: '监控项管理',
    icon: Setting
  },
  {
    index: '/testing',
    title: '监控测试',
    icon: Checked
  },
  {
    index: '/status',
    title: '监控状态',
    icon: DataAnalysis
  },
  {
    index: '/batch',
    title: '批量操作',
    icon: Operation
  }
]

const activeIndex = computed(() => route.path)

const handleMenuSelect = (index: string) => {
  router.push(index)
}

const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}
</script>

<template>
  <div class="layout-container">
    <ElContainer class="layout">
      <ElAside :width="collapsed ? '64px' : '250px'" class="sidebar">
        <div class="logo-container">
          <div class="logo">
            <ElIcon size="24" color="#3B82F6">
              <Monitor />
            </ElIcon>
            <span v-if="!collapsed" class="logo-text">监控系统</span>
          </div>
          <button @click="toggleCollapse" class="collapse-btn">
            <ElIcon size="16">
              <component :is="collapsed ? 'Expand' : 'Fold'" />
            </ElIcon>
          </button>
        </div>
        
        <ElMenu
          :default-active="activeIndex"
          class="sidebar-menu"
          :collapse="collapsed"
          :unique-opened="true"
          @select="handleMenuSelect"
        >
          <ElMenuItem
            v-for="item in menuItems"
            :key="item.index"
            :index="item.index"
            class="menu-item"
          >
            <ElIcon size="18">
              <component :is="item.icon" />
            </ElIcon>
            <span>{{ item.title }}</span>
          </ElMenuItem>
        </ElMenu>
      </ElAside>

      <ElContainer class="main-container">
        <ElHeader class="header">
          <div class="header-content">
            <h1 class="page-title">航锦云监控系统</h1>
            <div class="header-actions">
              <span class="status-indicator online">系统运行中</span>
            </div>
          </div>
        </ElHeader>

        <ElMain class="main-content">
          <RouterView />
        </ElMain>
      </ElContainer>
    </ElContainer>
  </div>
</template>

<style scoped>
.layout-container {
  height: 100vh;
  background: #f8fafc;
}

.layout {
  height: 100%;
}

.sidebar {
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  height: 64px;
  box-sizing: border-box;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  white-space: nowrap;
}

.collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.sidebar-menu {
  border: none;
  padding: 8px 0;
}

.menu-item {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background: #f3f4f6 !important;
  color: #3B82F6 !important;
}

.menu-item.is-active {
  background: #eff6ff !important;
  color: #3B82F6 !important;
  border-right: 3px solid #3B82F6;
}

.main-container {
  background: #f8fafc;
}

.header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.online {
  background: #d1fae5;
  color: #065f46;
}

.status-indicator.online::before {
  content: '';
  width: 6px;
  height: 6px;
  background: #10b981;
  border-radius: 50%;
}

.main-content {
  padding: 24px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .logo-text {
    display: none;
  }
  
  .sidebar {
    width: 64px !important;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .page-title {
    font-size: 18px;
  }
}
</style>