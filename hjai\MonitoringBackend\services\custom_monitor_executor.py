"""
自定义监控执行器

负责执行自定义监控项，包括命令执行管理、结果解析和验证、错误处理和重试等
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

from models.custom_monitor import MonitorItem, MonitorData, MonitorExecution
from utils.ssh_executor import get_ssh_executor, ExecutionResult
from .security_validator import SecurityValidator
from config.timezone_utils import TZ

logger = logging.getLogger(__name__)


class CustomMonitorExecutor:
    """自定义监控执行器"""
    
    def __init__(self):
        self.ssh_executor = get_ssh_executor()
        self.security_validator = SecurityValidator()
        
        # 执行统计
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'timeout_executions': 0,
            'retry_executions': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0
        }
    
    async def execute_monitor_item(
        self,
        monitor_item: MonitorItem,
        server: Dict[str, Any],
        save_execution_record: bool = True
    ) -> Tuple[bool, Optional[str], Optional[Any]]:
        """
        执行单个监控项
        
        Args:
            monitor_item: 监控项对象
            server: 服务器信息
            save_execution_record: 是否保存执行记录
            
        Returns:
            Tuple[bool, Optional[str], Optional[Any]]: (是否成功, 错误信息, 解析后的数据)
        """
        execution_record = None
        start_time = time.time()
        
        try:
            # 创建执行记录
            if save_execution_record:
                execution_record = await MonitorExecution.create_execution(
                    monitor_item_id=monitor_item.id,
                    ip=server['ip'],
                    status='running'
                )
            
            # 安全性验证
            is_safe, security_error = self.security_validator.validate_command(monitor_item.command)
            if not is_safe:
                error_msg = f"命令安全验证失败: {security_error}"
                await self._handle_execution_failure(
                    execution_record, error_msg, time.time() - start_time
                )
                return False, error_msg, None
            
            # 执行命令
            result = await self.ssh_executor.execute_command(
                server=server,
                command=monitor_item.command,
                timeout=monitor_item.timeout,
                max_retries=monitor_item.retry_count
            )
            
            execution_time = time.time() - start_time
            
            # 更新统计信息
            self._update_stats(result, execution_time)
            
            if result.success:
                # 解析和验证结果
                parsed_data, parse_error = self._parse_command_output(
                    result.output, monitor_item.data_type
                )
                
                if parse_error:
                    error_msg = f"数据解析失败: {parse_error}"
                    await self._handle_execution_failure(
                        execution_record, error_msg, execution_time, result.output
                    )
                    return False, error_msg, None
                
                # 保存监控数据
                await self._save_monitor_data(
                    monitor_item, server['ip'], parsed_data, 0  # 0表示正常状态
                )
                
                # 更新执行记录
                if execution_record:
                    await execution_record.update_execution(
                        status='success',
                        execution_time=execution_time,
                        command_output=result.output,
                        retry_count=result.retry_count
                    )
                
                logger.debug(f"监控项执行成功: {monitor_item.name}@{server['ip']}")
                return True, None, parsed_data
            
            else:
                # 执行失败
                error_msg = result.error or "命令执行失败"
                await self._handle_execution_failure(
                    execution_record, error_msg, execution_time, 
                    result.output, result.retry_count
                )
                return False, error_msg, None
                
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"监控执行异常: {str(e)}"
            logger.error(f"监控项执行异常: {monitor_item.name}@{server['ip']} - {error_msg}")
            
            await self._handle_execution_failure(
                execution_record, error_msg, execution_time
            )
            return False, error_msg, None
    
    async def execute_monitor_items_batch(
        self,
        monitor_items: List[MonitorItem],
        servers: List[Dict[str, Any]],
        max_concurrent: int = 10
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量执行监控项
        
        Args:
            monitor_items: 监控项列表
            servers: 服务器列表
            max_concurrent: 最大并发数
            
        Returns:
            Dict[str, Dict[str, Any]]: 执行结果 {server_ip: {monitor_item_id: result}}
        """
        results = {}
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_single(monitor_item: MonitorItem, server: Dict[str, Any]):
            async with semaphore:
                success, error, data = await self.execute_monitor_item(monitor_item, server)
                return {
                    'monitor_item_id': monitor_item.id,
                    'monitor_item_name': monitor_item.name,
                    'success': success,
                    'error': error,
                    'data': data,
                    'server_ip': server['ip']
                }
        
        # 创建所有任务
        tasks = []
        for server in servers:
            for monitor_item in monitor_items:
                tasks.append(execute_single(monitor_item, server))
        
        # 执行所有任务
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        for task_result in task_results:
            if isinstance(task_result, Exception):
                logger.error(f"批量执行任务异常: {task_result}")
                continue
            
            server_ip = task_result['server_ip']
            monitor_item_id = task_result['monitor_item_id']
            
            if server_ip not in results:
                results[server_ip] = {}
            
            results[server_ip][monitor_item_id] = task_result
        
        return results
    
    def _parse_command_output(self, output: str, data_type: str) -> Tuple[Any, Optional[str]]:
        """
        解析命令输出
        
        Args:
            output: 命令输出
            data_type: 数据类型
            
        Returns:
            Tuple[Any, Optional[str]]: (解析后的数据, 错误信息)
        """
        if not output:
            return None, "命令输出为空"
        
        try:
            if data_type == "string":
                return output, None
                
            elif data_type == "number":
                # 尝试解析为数字
                try:
                    if '.' in output:
                        return float(output), None
                    else:
                        return int(output), None
                except ValueError:
                    return output, None  # 如果无法解析为数字，返回原始字符串
                    
            elif data_type == "json":
                return json.loads(output), None
                
            elif data_type == "boolean":
                lower_output = output.lower().strip()
                if lower_output in ['true', '1', 'yes', 'on', 'enabled']:
                    return True, None
                elif lower_output in ['false', '0', 'no', 'off', 'disabled']:
                    return False, None
                else:
                    return output, None  # 返回原始值
                    
            else:
                # 未知数据类型，返回原始字符串
                return output, None
                
        except json.JSONDecodeError as e:
            return None, f"JSON解析失败: {str(e)}"
        except Exception as e:
            return None, f"数据解析异常: {str(e)}"
    
    async def _save_monitor_data(
        self,
        monitor_item: MonitorItem,
        ip: str,
        data: Any,
        status: int
    ):
        """保存监控数据"""
        try:
            # 将数据转换为字符串存储
            if isinstance(data, (dict, list)):
                value_str = json.dumps(data, ensure_ascii=False)
            else:
                value_str = str(data)
            
            await MonitorData.create_data(
                monitor_item_id=monitor_item.id,
                ip=ip,
                value=value_str,
                status=status
            )
            
        except Exception as e:
            logger.error(f"保存监控数据失败: {e}")
    
    async def _handle_execution_failure(
        self,
        execution_record: Optional[MonitorExecution],
        error_message: str,
        execution_time: float,
        command_output: Optional[str] = None,
        retry_count: int = 0
    ):
        """处理执行失败"""
        if execution_record:
            await execution_record.update_execution(
                status='failed',
                error_message=error_message,
                execution_time=execution_time,
                command_output=command_output,
                retry_count=retry_count
            )
    
    def _update_stats(self, result: ExecutionResult, execution_time: float):
        """更新统计信息"""
        self.stats['total_executions'] += 1
        self.stats['total_execution_time'] += execution_time
        
        if result.success:
            self.stats['successful_executions'] += 1
        else:
            self.stats['failed_executions'] += 1
            
            if 'timeout' in (result.error or '').lower():
                self.stats['timeout_executions'] += 1
        
        if result.retry_count > 0:
            self.stats['retry_executions'] += 1
        
        # 更新平均执行时间
        if self.stats['total_executions'] > 0:
            self.stats['average_execution_time'] = (
                self.stats['total_execution_time'] / self.stats['total_executions']
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return {
            **self.stats,
            'success_rate': (
                self.stats['successful_executions'] / self.stats['total_executions'] * 100
                if self.stats['total_executions'] > 0 else 0
            ),
            'ssh_executor_stats': self.ssh_executor.get_stats()
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'timeout_executions': 0,
            'retry_executions': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0
        }
