"""
安全验证器

负责监控命令的安全性验证，防止命令注入等安全问题
"""

import re
import logging
from typing import Tuple, List, Optional

logger = logging.getLogger(__name__)


class SecurityValidator:
    """安全验证器类"""
    
    # 危险命令黑名单
    DANGEROUS_COMMANDS = [
        'rm', 'rmdir', 'del', 'delete', 'format',
        'mkfs', 'fdisk', 'parted', 'dd',
        'shutdown', 'reboot', 'halt', 'poweroff',
        'passwd', 'chpasswd', 'usermod', 'userdel',
        'chmod', 'chown', 'chgrp',
        'iptables', 'ufw', 'firewall-cmd',
        'systemctl', 'service', 'init',
        'crontab', 'at', 'batch',
        'mount', 'umount', 'fsck',
        'kill', 'killall', 'pkill',
        'su', 'sudo', 'doas'
    ]
    
    # 危险字符和模式
    DANGEROUS_PATTERNS = [
        r'[;&|`$()]',  # 命令分隔符和特殊字符
        r'>\s*/dev/',  # 重定向到设备文件
        r'>\s*/etc/',  # 重定向到系统配置目录
        r'>\s*/bin/',  # 重定向到系统二进制目录
        r'>\s*/sbin/', # 重定向到系统管理二进制目录
        r'>\s*/usr/',  # 重定向到用户程序目录
        r'>\s*/var/',  # 重定向到变量数据目录
        r'>\s*/tmp/',  # 重定向到临时目录
        r'<\s*/dev/', # 从设备文件读取
        r'\$\(',       # 命令替换
        r'`[^`]*`',    # 反引号命令替换
        r'\|\s*sh',    # 管道到shell
        r'\|\s*bash',  # 管道到bash
        r'\|\s*zsh',   # 管道到zsh
        r'\|\s*csh',   # 管道到csh
        r'wget\s+',    # 下载命令
        r'curl\s+.*-o', # curl下载
        r'nc\s+',      # netcat
        r'telnet\s+',  # telnet
        r'ssh\s+',     # ssh连接
        r'scp\s+',     # scp传输
        r'rsync\s+',   # rsync同步
    ]
    
    # 允许的安全命令模式
    SAFE_COMMAND_PATTERNS = [
        r'^cat\s+/proc/',           # 读取proc文件系统
        r'^cat\s+/sys/',            # 读取sys文件系统
        r'^ls(\s+.*)?$',            # 列出文件（可带参数）
        r'^ps(\s+.*)?$',            # 进程信息（可带参数）
        r'^top(\s+.*)?$',           # 系统监控（可带参数）
        r'^htop(\s+.*)?$',          # 系统监控（可带参数）
        r'^free(\s+.*)?$',          # 内存信息（可带参数）
        r'^df(\s+.*)?$',            # 磁盘空间（可带参数）
        r'^du(\s+.*)?$',            # 目录大小（可带参数）
        r'^uptime(\s+.*)?$',        # 系统运行时间（可带参数）
        r'^uname(\s+.*)?$',         # 系统信息（可带参数）
        r'^whoami(\s+.*)?$',        # 当前用户（可带参数）
        r'^id(\s+.*)?$',            # 用户ID信息（可带参数）
        r'^date(\s+.*)?$',          # 日期时间（可带参数）
        r'^hostname(\s+.*)?$',      # 主机名（可带参数）
        r'^ifconfig(\s+.*)?$',      # 网络配置（可带参数）
        r'^ip(\s+.*)?$',            # 网络配置（可带参数）
        r'^netstat(\s+.*)?$',       # 网络状态（可带参数）
        r'^ss(\s+.*)?$',            # 网络状态（可带参数）
        r'^lscpu(\s+.*)?$',         # CPU信息（可带参数）
        r'^lsblk(\s+.*)?$',         # 块设备信息（可带参数）
        r'^lsusb(\s+.*)?$',         # USB设备信息（可带参数）
        r'^lspci(\s+.*)?$',         # PCI设备信息（可带参数）
        r'^iostat(\s+.*)?$',        # IO统计（可带参数）
        r'^vmstat(\s+.*)?$',        # 虚拟内存统计（可带参数）
        r'^sar(\s+.*)?$',           # 系统活动报告（可带参数）
        r'^nvidia-smi(\s+.*)?$',    # NVIDIA GPU信息（可带参数）
        r'^sensors(\s+.*)?$',       # 硬件传感器（可带参数）
        r'^w\s*$',                  # 当前登录用户
        r'^who\s*$',                # 当前登录用户
        r'^last\s+',                # 登录历史
        r'^history\s+',             # 命令历史
        r'^env\s*$',                # 环境变量
        r'^echo\s+',                # 输出文本
        r'^grep\s+',                # 文本搜索
        r'^awk\s+',                 # 文本处理
        r'^sed\s+',                 # 文本编辑
        r'^head\s+',                # 显示文件头部
        r'^tail\s+',                # 显示文件尾部
        r'^wc\s+',                  # 字符统计
        r'^find\s+.*-name',         # 文件查找
        r'^locate\s+',              # 文件定位
        r'^which\s+',               # 命令位置
        r'^whereis\s+',             # 命令位置
    ]
    
    def validate_command(self, command: str) -> Tuple[bool, Optional[str]]:
        """
        验证命令的安全性
        
        Args:
            command: 要验证的命令
            
        Returns:
            Tuple[bool, Optional[str]]: (是否安全, 错误信息)
        """
        if not command or not command.strip():
            return False, "命令不能为空"
        
        command = command.strip()
        
        # 1. 检查命令长度
        if len(command) > 1000:
            return False, "命令长度过长"
        
        # 2. 检查危险命令
        is_dangerous, danger_msg = self._check_dangerous_commands(command)
        if is_dangerous:
            return False, danger_msg
        
        # 3. 检查危险模式
        is_dangerous_pattern, pattern_msg = self._check_dangerous_patterns(command)
        if is_dangerous_pattern:
            return False, pattern_msg
        
        # 4. 检查是否为安全命令
        is_safe_command = self._check_safe_commands(command)
        if not is_safe_command:
            return False, "命令不在安全命令列表中"
        
        return True, None
    
    def _check_dangerous_commands(self, command: str) -> Tuple[bool, Optional[str]]:
        """检查危险命令"""
        command_lower = command.lower()
        
        for dangerous_cmd in self.DANGEROUS_COMMANDS:
            # 检查命令是否以危险命令开头
            if re.match(rf'\b{re.escape(dangerous_cmd)}\b', command_lower):
                return True, f"包含危险命令: {dangerous_cmd}"
            
            # 检查命令中是否包含危险命令
            if re.search(rf'\|\s*{re.escape(dangerous_cmd)}\b', command_lower):
                return True, f"管道中包含危险命令: {dangerous_cmd}"
        
        return False, None
    
    def _check_dangerous_patterns(self, command: str) -> Tuple[bool, Optional[str]]:
        """检查危险模式"""
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, command, re.IGNORECASE):
                return True, f"包含危险模式: {pattern}"
        
        return False, None
    
    def _check_safe_commands(self, command: str) -> bool:
        """检查是否为安全命令"""
        command_lower = command.lower()
        
        for safe_pattern in self.SAFE_COMMAND_PATTERNS:
            if re.match(safe_pattern, command_lower):
                return True
        
        return False
    
    def get_command_risk_level(self, command: str) -> str:
        """
        获取命令的风险级别
        
        Args:
            command: 要评估的命令
            
        Returns:
            str: 风险级别 (low, medium, high, critical)
        """
        if not command or not command.strip():
            return "critical"
        
        command = command.strip().lower()
        
        # 检查是否包含危险命令
        for dangerous_cmd in self.DANGEROUS_COMMANDS:
            if re.search(rf'\b{re.escape(dangerous_cmd)}\b', command):
                return "critical"
        
        # 检查危险模式
        high_risk_patterns = [r'[;&|`$()]', r'>\s*/', r'<\s*/', r'\$\(', r'`[^`]*`']
        for pattern in high_risk_patterns:
            if re.search(pattern, command):
                return "high"
        
        # 检查中等风险模式
        medium_risk_patterns = [r'\|\s*\w+', r'>\s*\w+', r'<\s*\w+']
        for pattern in medium_risk_patterns:
            if re.search(pattern, command):
                return "medium"
        
        # 检查是否为安全命令
        if self._check_safe_commands(command):
            return "low"
        
        return "medium"
    
    def sanitize_command(self, command: str) -> str:
        """
        清理命令中的危险字符
        
        Args:
            command: 原始命令
            
        Returns:
            str: 清理后的命令
        """
        if not command:
            return ""
        
        # 移除危险字符
        sanitized = re.sub(r'[;&|`$()]', '', command)
        
        # 移除多余的空格
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        return sanitized
    
    def get_security_recommendations(self, command: str) -> List[str]:
        """
        获取安全建议
        
        Args:
            command: 要分析的命令
            
        Returns:
            List[str]: 安全建议列表
        """
        recommendations = []
        
        if not command or not command.strip():
            recommendations.append("请提供有效的命令")
            return recommendations
        
        command_lower = command.lower()
        
        # 检查命令长度
        if len(command) > 500:
            recommendations.append("建议缩短命令长度以提高安全性")
        
        # 检查特殊字符
        if re.search(r'[;&|`$()]', command):
            recommendations.append("避免使用特殊字符，如 ; & | ` $ ( )")
        
        # 检查重定向
        if re.search(r'[><]', command):
            recommendations.append("谨慎使用重定向操作符")
        
        # 检查管道
        if '|' in command:
            recommendations.append("谨慎使用管道操作，确保所有命令都是安全的")
        
        # 检查是否为只读操作
        readonly_patterns = [r'^cat\s+', r'^ls\s+', r'^ps\s+', r'^free\s*$', r'^df\s+']
        is_readonly = any(re.match(pattern, command_lower) for pattern in readonly_patterns)
        
        if not is_readonly:
            recommendations.append("建议使用只读命令以确保系统安全")
        
        return recommendations
