from tortoise import fields
from tortoise.contrib.pydantic import pydantic_model_creator
from pydantic import BaseModel, field_validator, ValidationError
from datetime import datetime
from typing import Dict, Union, Literal, Optional
import pytz
from models.base_model import BaseModel as TortoiseBaseModel, SHANGHAI_TZ, get_shanghai_now
# 导入新的时区处理工具
from config.timezone_utils import TZ
# 保持向后兼容
from config.timezone import add_tz

class GPUStatsSummary(TortoiseBaseModel):
    """服务器GPU监控数据总体统计"""
    
    ip = fields.CharField(max_length=50, description="服务器IP地址")
    total_gpu_count = fields.IntField(description="GPU总数量", default=8)
    avg_usage = fields.FloatField(description="平均GPU利用率(%)")
    total_memory_used = fields.FloatField(description="总使用显存(MB)")
    total_memory_total = fields.FloatField(description="总显存容量(MB)")
    memory_usage_percent = fields.FloatField(description="显存使用百分比(%)")
    avg_temperature = fields.FloatField(description="平均温度(℃)")
    timestamp = fields.DatetimeField(description="记录时间")
    
    class Meta:
        table = "gpu_stats_summary"
        description = "GPU监控数据总体统计"
    
    def __str__(self):
        return f"{self.ip} - {self.timestamp} - 使用率:{self.avg_usage}%"
        
    async def save(self, *args, **kwargs):
        """重写保存方法，确保timestamp字段使用上海时区"""
        # 如果timestamp未设置，使用当前上海时间
        if not self.timestamp:
            now_time = get_shanghai_now()
            self.timestamp = add_tz(now_time)
        elif not getattr(self.timestamp, 'tzinfo', None):
            # 如果现有的时间戳没有时区信息，添加时区信息
            self.timestamp = add_tz(self.timestamp)
            
        # 调用父类的save方法继续处理
        return await super().save(*args, **kwargs)


GPUStatsSummary_Pydantic = pydantic_model_creator(GPUStatsSummary)


class GPUStatsDetail(TortoiseBaseModel):
    """单个GPU详细监控数据"""
    
    summary = fields.ForeignKeyField(
        'models.GPUStatsSummary',
        related_name='details',
        description="关联的总体统计",
        on_delete=fields.CASCADE  # 当摘要记录被删除时，级联删除详细记录
    )
    gpu_index = fields.IntField(description="GPU序号(0-7)")
    gpu_name = fields.CharField(max_length=100, description="GPU名称")
    gpu_usage = fields.FloatField(description="GPU利用率(%)")
    gpu_memory_used = fields.FloatField(description="已用显存(MB)")
    gpu_memory_total = fields.FloatField(description="总显存(MB)")
    gpu_memory_percent = fields.FloatField(description="显存使用百分比(%)")
    gpu_temperature = fields.FloatField(description="GPU温度(℃)")
    
    class Meta:
        table = "gpu_stats_detail"
        description = "GPU详细监控数据"
    
    def __str__(self):
        return f"GPU{self.gpu_index} - 使用率:{self.gpu_usage}%"


GPUStatsDetail_Pydantic = pydantic_model_creator(GPUStatsDetail)


class GPUStatsResponse(BaseModel):
    """精简的GPU统计数据响应模型"""
    ip: str
    timestamp: datetime
    avg_usage: float
    total_memory_used: float
    total_memory_total: float
    memory_usage_percent: float
    avg_temperature: float
    gpu_index: Optional[int] = None  # None表示汇总数据，整数表示特定GPU索引
    is_summary: bool = True  # True表示汇总数据，False表示单个GPU数据

    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


class GPUStatsRequest(BaseModel):
    """GPU统计数据查询请求模型"""
    ip: str
    start_time: datetime
    end_time: datetime
    gpu_index: Union[Literal["all"], int] = "all"
    
    @field_validator('ip')
    @classmethod
    def validate_ip(cls, v):
        if not v:
            raise ValueError("IP地址不能为空")
        return v
    
    @field_validator('start_time')
    @classmethod
    def validate_start_time(cls, v):
        if not v:
            raise ValueError("开始时间不能为空")
        return v
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v, values):
        if not v:
            raise ValueError("结束时间不能为空")
        
        # 检查开始时间和结束时间的逻辑关系
        if 'start_time' in values.data and values.data['start_time'] and v <= values.data['start_time']:
            raise ValueError("结束时间必须晚于开始时间")
        
        return v
    
    @field_validator('gpu_index')
    @classmethod
    def validate_gpu_index(cls, v):
        if v != "all" and (not isinstance(v, int) or v < 0 or v > 7):
            raise ValueError("GPU索引必须为'all'或0-7之间的整数")
        return v


class GPUCountRequest(BaseModel):
    """GPU计数请求模型"""
    ip: str
    gpu_count: bool = True
    
    @field_validator('ip')
    @classmethod
    def validate_ip(cls, v):
        if not v:
            raise ValueError("IP地址不能为空")
        return v


class GPUCountResponse(BaseModel):
    """GPU计数响应模型"""
    ip: str
    gpu_counts: Dict[str, str]  # {"0": "gpu0", "1": "gpu1", ...}

    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }