# 自定义监控项安全配置指南

## 概述

自定义监控项模块内置了多层安全机制，本文档详细说明了安全配置和最佳实践，帮助您构建安全可靠的监控环境。

## 安全架构

### 多层安全防护

```
┌─────────────────────────────────────────────────────────────┐
│                    API访问控制层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ 身份认证        │  │ 权限控制        │  │ 访问日志        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    命令安全验证层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ 命令黑名单      │  │ 模式检测        │  │ 安全级别评估    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据传输安全层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ SSH加密传输     │  │ 连接池管理      │  │ 超时控制        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据安全处理层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ 敏感信息过滤    │  │ 数据验证        │  │ 错误信息脱敏    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 访问控制配置

### 1. 身份认证

#### API密钥认证

```python
# 生成API密钥
from core.access_control import get_access_control_manager

acm = get_access_control_manager()
user = acm.get_user("username")
api_key = user.generate_api_key(expires_in_days=30)
print(f"API Key: {api_key}")
```

#### 使用API密钥

```bash
# 在请求头中包含API密钥
curl -H "X-API-Key: your-api-key" \
  "http://your-server/monitor/items"
```

#### Session认证

```bash
# 登录获取session
curl -X POST http://your-server/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your-username",
    "password": "your-password"
  }'

# 使用session token
curl -H "Authorization: Bearer session-token" \
  "http://your-server/monitor/items"
```

### 2. 权限控制

#### 角色定义

```python
# 角色权限映射
ROLE_PERMISSIONS = {
    "admin": [
        "monitor:create", "monitor:read", "monitor:update", "monitor:delete",
        "monitor:execute", "monitor:test", "server:read", "server:manage",
        "data:read", "data:export", "data:delete", "system:config",
        "system:admin", "system:stats", "user:create", "user:read",
        "user:update", "user:delete"
    ],
    "operator": [
        "monitor:create", "monitor:read", "monitor:update", "monitor:execute",
        "monitor:test", "server:read", "data:read", "data:export", "system:stats"
    ],
    "viewer": [
        "monitor:read", "server:read", "data:read", "system:stats"
    ],
    "guest": [
        "monitor:read", "data:read"
    ]
}
```

#### 用户管理

```python
# 创建用户
acm = get_access_control_manager()

# 创建操作员用户
operator = acm.create_user(
    username="monitor_operator",
    email="<EMAIL>",
    password="secure_password",
    roles=[Role.OPERATOR]
)

# 创建只读用户
viewer = acm.create_user(
    username="monitor_viewer",
    email="<EMAIL>",
    password="secure_password",
    roles=[Role.VIEWER]
)
```

### 3. 访问日志

#### 日志配置

```python
# 配置访问日志
import logging

# 设置日志级别
logging.getLogger("access_control").setLevel(logging.INFO)

# 配置日志格式
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

#### 查看访问日志

```python
# 获取用户访问日志
acm = get_access_control_manager()
logs = acm.get_access_logs(user_id="user123", limit=100)

for log in logs:
    print(f"{log.timestamp}: {log.user_id} - {log.action} - {log.success}")
```

## 命令安全配置

### 1. 安全验证器配置

#### 自定义危险命令列表

```python
# 扩展危险命令黑名单
from services.security_validator import SecurityValidator

validator = SecurityValidator()

# 添加自定义危险命令
custom_dangerous_commands = [
    'custom_dangerous_cmd',
    'company_specific_cmd',
    'internal_tool'
]

validator.DANGEROUS_COMMANDS.extend(custom_dangerous_commands)
```

#### 自定义安全命令模式

```python
# 添加自定义安全命令模式
custom_safe_patterns = [
    r'^/usr/local/bin/company_monitor',  # 公司自定义监控脚本
    r'^/opt/monitoring/',               # 监控工具目录
    r'^company_cmd\s+--read-only'       # 公司命令的只读模式
]

validator.SAFE_COMMAND_PATTERNS.extend(custom_safe_patterns)
```

### 2. 命令白名单配置

#### 严格模式配置

```python
# 启用严格模式 - 只允许白名单中的命令
STRICT_MODE = True

# 白名单命令列表
ALLOWED_COMMANDS = [
    'cat /proc/cpuinfo',
    'cat /proc/meminfo', 
    'cat /proc/loadavg',
    'free -m',
    'df -h',
    'ps aux',
    'uptime',
    'whoami',
    'hostname',
    'date',
    'uname -a'
]

def validate_command_strict(command):
    """严格模式命令验证"""
    if STRICT_MODE:
        return command.strip() in ALLOWED_COMMANDS
    return True
```

### 3. 安全级别配置

#### 安全级别策略

```python
# 安全级别配置
SECURITY_POLICIES = {
    "critical": {
        "allowed": False,
        "require_approval": True,
        "max_timeout": 0
    },
    "high": {
        "allowed": False,
        "require_approval": True,
        "max_timeout": 10
    },
    "medium": {
        "allowed": True,
        "require_approval": True,
        "max_timeout": 30
    },
    "low": {
        "allowed": True,
        "require_approval": False,
        "max_timeout": 120
    }
}
```

## SSH连接安全

### 1. SSH密钥配置

#### 生成SSH密钥对

```bash
# 生成专用的监控密钥
ssh-keygen -t rsa -b 4096 -f ~/.ssh/monitoring_key -C "<EMAIL>"

# 设置适当的权限
chmod 600 ~/.ssh/monitoring_key
chmod 644 ~/.ssh/monitoring_key.pub
```

#### 配置目标服务器

```bash
# 在目标服务器上添加公钥
cat ~/.ssh/monitoring_key.pub >> ~/.ssh/authorized_keys

# 设置权限
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

#### 监控系统配置

```python
# SSH密钥配置
SSH_CONFIG = {
    "default_key_path": "/path/to/monitoring_key",
    "key_passphrase": "optional_passphrase",
    "strict_host_key_checking": False,  # 仅在测试环境
    "connect_timeout": 15,
    "auth_timeout": 10
}
```

### 2. 专用监控用户

#### 创建监控专用用户

```bash
# 在目标服务器创建监控用户
sudo useradd -r -s /bin/bash -d /home/<USER>
sudo mkdir -p /home/<USER>/.ssh
sudo chown monitor:monitor /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh
```

#### 配置sudo权限（如需要）

```bash
# 编辑sudoers文件
sudo visudo

# 添加监控用户权限（仅允许特定命令）
monitor ALL=(ALL) NOPASSWD: /bin/cat /proc/*, /usr/bin/free, /bin/df, /bin/ps
```

### 3. 连接池安全配置

#### 连接池参数

```python
# 安全的连接池配置
CONNECTION_POOL_CONFIG = {
    "max_connections_per_server": 3,      # 限制单服务器连接数
    "max_total_connections": 50,          # 限制总连接数
    "max_idle_time": 300,                 # 5分钟空闲超时
    "max_lifetime": 1800,                 # 30分钟最大生命周期
    "health_check_interval": 60,          # 1分钟健康检查
    "connection_timeout": 15              # 15秒连接超时
}
```

## 数据安全配置

### 1. 敏感信息过滤

#### 配置敏感信息模式

```python
# 自定义敏感信息模式
CUSTOM_SENSITIVE_PATTERNS = [
    r'company_secret\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
    r'internal_token\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
    r'database_url\s*[:=]\s*["\']?([^"\'\s]+)["\']?',
    r'\b\d{4}-\d{4}-\d{4}-\d{4}\b',  # 信用卡号
    r'\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}\b'  # IBAN
]

# 添加到验证器
from core.data_validator import get_data_validator
validator = get_data_validator()
validator.sensitive_patterns.extend(CUSTOM_SENSITIVE_PATTERNS)
```

### 2. 数据加密存储

#### 敏感数据加密

```python
import cryptography.fernet

# 生成加密密钥
key = Fernet.generate_key()
cipher_suite = Fernet(key)

# 加密敏感数据
def encrypt_sensitive_data(data):
    return cipher_suite.encrypt(data.encode()).decode()

# 解密敏感数据
def decrypt_sensitive_data(encrypted_data):
    return cipher_suite.decrypt(encrypted_data.encode()).decode()
```

### 3. 数据传输安全

#### HTTPS配置

```nginx
# Nginx HTTPS配置
server {
    listen 443 ssl http2;
    server_name monitoring.company.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location /monitor/ {
        proxy_pass http://backend;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 网络安全配置

### 1. 防火墙配置

#### 监控服务器防火墙

```bash
# 允许HTTPS访问
sudo ufw allow 443/tcp

# 允许SSH访问（限制来源IP）
sudo ufw allow from ***********/24 to any port 22

# 拒绝其他连接
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 启用防火墙
sudo ufw enable
```

#### 目标服务器防火墙

```bash
# 只允许监控服务器SSH访问
sudo ufw allow from MONITOR_SERVER_IP to any port 22

# 拒绝其他SSH连接
sudo ufw deny 22/tcp
```

### 2. 网络隔离

#### VPN配置

```bash
# 使用VPN连接监控网络
# 配置OpenVPN客户端
sudo openvpn --config monitoring.ovpn
```

#### 专用网络

```yaml
# Docker网络配置
version: '3.8'
services:
  monitoring:
    networks:
      - monitoring_network
      
networks:
  monitoring_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 审计和监控

### 1. 安全审计

#### 审计日志配置

```python
# 安全审计配置
AUDIT_CONFIG = {
    "log_all_commands": True,
    "log_failed_attempts": True,
    "log_privilege_escalation": True,
    "retention_days": 90,
    "alert_on_suspicious_activity": True
}
```

#### 审计报告

```python
# 生成安全审计报告
def generate_security_audit_report():
    acm = get_access_control_manager()
    
    # 获取失败的登录尝试
    failed_logins = acm.get_access_logs(action="login", success=False)
    
    # 获取权限提升尝试
    privilege_escalation = acm.get_access_logs(action="privilege_escalation")
    
    # 获取危险命令尝试
    dangerous_commands = get_dangerous_command_attempts()
    
    return {
        "failed_logins": len(failed_logins),
        "privilege_escalation_attempts": len(privilege_escalation),
        "dangerous_command_attempts": len(dangerous_commands),
        "report_date": datetime.now().isoformat()
    }
```

### 2. 实时监控

#### 安全事件监控

```python
# 安全事件监控
class SecurityMonitor:
    def __init__(self):
        self.alert_thresholds = {
            "failed_logins_per_hour": 10,
            "dangerous_commands_per_day": 5,
            "privilege_escalation_per_day": 3
        }
    
    def check_security_alerts(self):
        # 检查失败登录次数
        recent_failures = self.get_recent_failed_logins(hours=1)
        if len(recent_failures) > self.alert_thresholds["failed_logins_per_hour"]:
            self.send_alert("High number of failed logins detected")
        
        # 检查危险命令尝试
        dangerous_attempts = self.get_dangerous_command_attempts(hours=24)
        if len(dangerous_attempts) > self.alert_thresholds["dangerous_commands_per_day"]:
            self.send_alert("Dangerous command attempts detected")
```

## 安全最佳实践

### 1. 定期安全检查

#### 安全检查清单

- [ ] 定期更新SSH密钥（建议每6个月）
- [ ] 审查用户权限和角色分配
- [ ] 检查危险命令黑名单更新
- [ ] 验证防火墙规则有效性
- [ ] 审计访问日志异常活动
- [ ] 测试备份和恢复流程
- [ ] 更新安全补丁和依赖

#### 自动化安全检查

```python
# 自动化安全检查脚本
def automated_security_check():
    checks = []
    
    # 检查过期的API密钥
    expired_keys = check_expired_api_keys()
    checks.append(("Expired API Keys", len(expired_keys)))
    
    # 检查弱密码
    weak_passwords = check_weak_passwords()
    checks.append(("Weak Passwords", len(weak_passwords)))
    
    # 检查未使用的用户账户
    inactive_users = check_inactive_users(days=90)
    checks.append(("Inactive Users", len(inactive_users)))
    
    return checks
```

### 2. 应急响应

#### 安全事件响应流程

1. **检测阶段**
   - 自动监控系统检测异常
   - 人工发现安全问题

2. **分析阶段**
   - 确定事件类型和严重程度
   - 分析影响范围

3. **响应阶段**
   - 立即隔离受影响系统
   - 阻止进一步的损害

4. **恢复阶段**
   - 修复安全漏洞
   - 恢复正常服务

5. **总结阶段**
   - 分析事件原因
   - 改进安全措施

#### 应急响应脚本

```bash
#!/bin/bash
# 安全事件应急响应脚本

# 禁用所有API访问
curl -X POST http://localhost/admin/disable-api

# 强制所有用户重新登录
curl -X POST http://localhost/admin/invalidate-sessions

# 备份当前日志
cp /var/log/monitoring/* /backup/emergency-$(date +%Y%m%d-%H%M%S)/

# 发送紧急通知
echo "Security incident detected at $(date)" | mail -s "URGENT: Security Alert" <EMAIL>
```

## 总结

通过实施这些安全配置和最佳实践，您可以构建一个安全可靠的自定义监控环境。关键要点包括：

1. **多层防护**：实施API访问控制、命令验证、数据加密等多层安全机制
2. **最小权限原则**：为用户和系统分配最小必要权限
3. **持续监控**：实时监控安全事件和异常活动
4. **定期审计**：定期检查和更新安全配置
5. **应急准备**：制定完善的安全事件响应流程

安全是一个持续的过程，需要根据威胁环境的变化不断调整和改进安全措施。
