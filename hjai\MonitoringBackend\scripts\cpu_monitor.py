import logging
from typing import Dict, Any
import paramiko

from models.cpu_stats import CPUStats, CPUTopProcess
from scripts.base_monitor import BaseMonitor
from config.settings import get_shanghai_time

# 获取日志记录器
logger = logging.getLogger(__name__)

class CPUMonitor(BaseMonitor):
    """CPU监控类"""
    
    def __init__(self):
        # 初始化，设置默认监控间隔为5分钟(300秒)
        super().__init__("CPU", 300)
    
    def get_cpu_info(self, ssh_client: paramiko.SSHClient) -> Dict[str, Any]:
        """
        通过SSH执行命令获取CPU信息
        
        Args:
            ssh_client: SSH客户端连接
        
        Returns:
            Dict[str, Any]: CPU信息字典
        """
        try:
            cpu_info = {}
            
            # 获取CPU型号信息
            stdin, stdout, stderr = ssh_client.exec_command("cat /proc/cpuinfo | grep 'model name' | head -1")
            model_output = stdout.read().decode("utf-8").strip()
            if model_output:
                cpu_info["model"] = model_output.split(":")[1].strip() if ":" in model_output else model_output
            
            # 获取CPU核心数
            stdin, stdout, stderr = ssh_client.exec_command("nproc")
            cores_output = stdout.read().decode("utf-8").strip()
            if cores_output.isdigit():
                cpu_info["cores"] = int(cores_output)
            
            # 获取CPU频率
            stdin, stdout, stderr = ssh_client.exec_command("cat /proc/cpuinfo | grep 'cpu MHz' | head -1")
            freq_output = stdout.read().decode("utf-8").strip()
            if freq_output and ":" in freq_output:
                cpu_info["frequency"] = float(freq_output.split(":")[1].strip())
            
            # 获取CPU负载
            stdin, stdout, stderr = ssh_client.exec_command("cat /proc/loadavg")
            load_output = stdout.read().decode("utf-8").strip()
            if load_output:
                load_parts = load_output.split()
                if len(load_parts) >= 3:
                    cpu_info["load_1min"] = float(load_parts[0])
                    cpu_info["load_5min"] = float(load_parts[1])
                    cpu_info["load_15min"] = float(load_parts[2])
            
            # 获取CPU使用率 (使用top命令)
            stdin, stdout, stderr = ssh_client.exec_command("top -bn1 | grep '%Cpu(s)'")
            cpu_usage_output = stdout.read().decode("utf-8").strip()
            
            if cpu_usage_output:
                # 解析top输出获取CPU使用率
                parts = cpu_usage_output.split(",")
                for part in parts:
                    if "us" in part:  # 用户空间使用率
                        try:
                            cpu_info["user_percent"] = float(part.split()[0])
                        except (ValueError, IndexError):
                            pass
                    elif "sy" in part:  # 系统空间使用率
                        try:
                            cpu_info["system_percent"] = float(part.split()[0])
                        except (ValueError, IndexError):
                            pass
                    elif "id" in part:  # 空闲率
                        try:
                            cpu_info["idle_percent"] = float(part.split()[0])
                        except (ValueError, IndexError):
                            pass
                    elif "wa" in part:  # IO等待率
                        try:
                            cpu_info["iowait_percent"] = float(part.split()[0])
                        except (ValueError, IndexError):
                            pass
            
            # 获取进程数
            stdin, stdout, stderr = ssh_client.exec_command("ps -e | wc -l")
            process_count = stdout.read().decode("utf-8").strip()
            if process_count.isdigit():
                cpu_info["process_count"] = int(process_count)
            
            # 计算总使用率
            if "idle_percent" in cpu_info:
                cpu_info["usage_percent"] = 100 - cpu_info["idle_percent"]
            elif "user_percent" in cpu_info and "system_percent" in cpu_info:
                cpu_info["usage_percent"] = cpu_info["user_percent"] + cpu_info["system_percent"]
            
            # 获取前5个CPU占用最高的进程
            stdin, stdout, stderr = ssh_client.exec_command("ps aux --sort=-%cpu | head -6")
            top_processes = stdout.read().decode("utf-8").strip().split("\n")
            
            if len(top_processes) > 1:  # 第一行是标题
                cpu_info["top_processes"] = []
                headers = top_processes[0].split()
                
                # 找到CPU列的索引
                cpu_index = -1
                for i, header in enumerate(headers):
                    if header == "%CPU":
                        cpu_index = i
                        break
                
                if cpu_index >= 0:
                    for i in range(1, len(top_processes)):
                        process = top_processes[i].split()
                        if len(process) > cpu_index:
                            try:
                                cpu_info["top_processes"].append({
                                    "user": process[0],
                                    "pid": process[1],
                                    "cpu_percent": float(process[cpu_index]),
                                    "command": " ".join(process[10:]) if len(process) > 10 else ""
                                })
                            except (ValueError, IndexError):
                                pass
            
            return cpu_info
        except Exception as e:
            logger.error(f"获取CPU信息失败: {str(e)}")
            return {}
    
    async def save_cpu_stats(self, ip: str, cpu_info: Dict[str, Any]) -> bool:
        """
        保存CPU统计数据到数据库
        
        Args:
            ip: 服务器IP
            cpu_info: CPU信息字典
        """
        if not cpu_info:
            logger.warning(f"没有CPU数据可保存: {ip}")
            return False
        
        try:
            # 获取当前UTC时间用于数据库存储
            from config.timezone_utils import TZ
            current_time = TZ.now_utc()  # 保持UTC时区信息
            
            # 创建CPU统计记录
            cpu_stats = await CPUStats.create(
                ip=ip,
                model=cpu_info.get("model"),
                cores=cpu_info.get("cores"),
                frequency=cpu_info.get("frequency"),
                usage_percent=cpu_info.get("usage_percent", 0),
                user_percent=cpu_info.get("user_percent"),
                system_percent=cpu_info.get("system_percent"),
                iowait_percent=cpu_info.get("iowait_percent"),
                load_1min=cpu_info.get("load_1min"),
                load_5min=cpu_info.get("load_5min"),
                load_15min=cpu_info.get("load_15min"),
                process_count=cpu_info.get("process_count"),
                timestamp=current_time  # 手动设置时间戳
            )
            
            # 保存CPU占用最高的进程
            if "top_processes" in cpu_info and cpu_info["top_processes"]:
                for process in cpu_info["top_processes"]:
                    await CPUTopProcess.create(
                        cpu_stats=cpu_stats,
                        pid=process["pid"],
                        user=process["user"],
                        cpu_percent=process["cpu_percent"],
                        command=process["command"]
                    )
            
            logger.debug(f"成功保存服务器 {ip} 的CPU统计数据到数据库")
            return True
        except Exception as e:
            logger.error(f"保存CPU统计数据失败: {str(e)}")
            return False
    
    async def start_monitoring(self, interval_seconds: int = None) -> None:
        """
        开始定时监控所有服务器CPU
        
        Args:
            interval_seconds: 监控间隔，单位秒，默认为self.default_interval(300秒)
        """
        # 使用基类的通用监控启动方法
        await super().start_monitoring(
            data_collector=self.get_cpu_info,
            data_saver=self.save_cpu_stats,
            only_connectable=True,
            interval_seconds=interval_seconds
        )

# 为保持向后兼容性添加的函数
async def start_monitoring(interval_seconds: int = 300) -> None:
    """
    开始定时监控所有服务器CPU（兼容旧版API）
    
    Args:
        interval_seconds: 监控间隔，单位秒，默认300秒(5分钟)
    """
    logger.info("通过兼容函数启动CPU监控服务")
    monitor = CPUMonitor()
    await monitor.start_monitoring(interval_seconds) 