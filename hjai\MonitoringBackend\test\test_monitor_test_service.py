"""
监控测试服务测试

测试监控测试服务的各项功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import paramiko

from services.monitor_test_service import MonitorTestService
from services.security_validator import SecurityValidator


class TestMonitorTestService:
    """监控测试服务测试类"""
    
    @pytest.fixture
    def service(self):
        """创建服务实例"""
        return MonitorTestService()
    
    @pytest.fixture
    def mock_ssh_client(self):
        """模拟SSH客户端"""
        client = Mock(spec=paramiko.SSHClient)
        client.close = Mock()
        return client
    
    @pytest.mark.asyncio
    async def test_test_monitor_command_success(self, service):
        """测试成功的监控命令测试"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate, \
             patch.object(service, '_test_ssh_connection') as mock_ssh_conn, \
             patch.object(service, '_execute_test_command') as mock_exec:
            
            # 设置模拟
            mock_validate.return_value = (True, None)
            mock_ssh_client = Mock()
            mock_ssh_conn.return_value = (mock_ssh_client, True)
            mock_exec.return_value = (True, "test output", None, 1.5)
            
            # 执行测试
            success, error = await service.test_monitor_command(
                command="echo 'test'",
                ip="*************",
                credentials={"username": "test", "password": "test"},
                timeout=30,
                data_type="string"
            )
            
            # 验证结果
            assert success is True
            assert error is None
            mock_validate.assert_called_once_with("echo 'test'")
            mock_ssh_conn.assert_called_once()
            mock_exec.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_test_monitor_command_security_failure(self, service):
        """测试安全验证失败的情况"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate:
            # 设置模拟 - 安全验证失败
            mock_validate.return_value = (False, "Dangerous command detected")
            
            # 执行测试
            success, error = await service.test_monitor_command(
                command="rm -rf /",
                ip="*************",
                credentials={"username": "test", "password": "test"}
            )
            
            # 验证结果
            assert success is False
            assert "命令安全验证失败" in error
            assert "Dangerous command detected" in error
    
    @pytest.mark.asyncio
    async def test_test_monitor_command_ssh_failure(self, service):
        """测试SSH连接失败的情况"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate, \
             patch.object(service, '_test_ssh_connection') as mock_ssh_conn:
            
            # 设置模拟
            mock_validate.return_value = (True, None)
            mock_ssh_conn.return_value = (None, False)
            
            # 执行测试
            success, error = await service.test_monitor_command(
                command="echo 'test'",
                ip="*************",
                credentials={"username": "test", "password": "test"}
            )
            
            # 验证结果
            assert success is False
            assert "SSH连接失败" in error
    
    @pytest.mark.asyncio
    async def test_test_monitor_command_execution_failure(self, service):
        """测试命令执行失败的情况"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate, \
             patch.object(service, '_test_ssh_connection') as mock_ssh_conn, \
             patch.object(service, '_execute_test_command') as mock_exec:
            
            # 设置模拟
            mock_validate.return_value = (True, None)
            mock_ssh_client = Mock()
            mock_ssh_conn.return_value = (mock_ssh_client, True)
            mock_exec.return_value = (False, None, "Command not found", 0.5)
            
            # 执行测试
            success, error = await service.test_monitor_command(
                command="nonexistent_command",
                ip="*************",
                credentials={"username": "test", "password": "test"}
            )
            
            # 验证结果
            assert success is False
            assert "命令执行失败" in error
            assert "Command not found" in error
    
    def test_validate_output_format_string(self, service):
        """测试字符串格式验证"""
        is_valid, error = service._validate_output_format("test output", "string")
        assert is_valid is True
        assert error is None
    
    def test_validate_output_format_number(self, service):
        """测试数字格式验证"""
        # 测试整数
        is_valid, error = service._validate_output_format("123", "number")
        assert is_valid is True
        assert error is None
        
        # 测试浮点数
        is_valid, error = service._validate_output_format("123.45", "number")
        assert is_valid is True
        assert error is None
        
        # 测试无效数字
        is_valid, error = service._validate_output_format("not_a_number", "number")
        assert is_valid is False
        assert "不是有效数字" in error
    
    def test_validate_output_format_json(self, service):
        """测试JSON格式验证"""
        # 测试有效JSON
        is_valid, error = service._validate_output_format('{"key": "value"}', "json")
        assert is_valid is True
        assert error is None
        
        # 测试无效JSON
        is_valid, error = service._validate_output_format('invalid json', "json")
        assert is_valid is False
        assert "不是有效JSON格式" in error
    
    def test_validate_output_format_boolean(self, service):
        """测试布尔格式验证"""
        # 测试true值
        for true_val in ['true', 'True', '1', 'yes', 'YES']:
            is_valid, error = service._validate_output_format(true_val, "boolean")
            assert is_valid is True
            assert error is None
        
        # 测试false值
        for false_val in ['false', 'False', '0', 'no', 'NO']:
            is_valid, error = service._validate_output_format(false_val, "boolean")
            assert is_valid is True
            assert error is None
        
        # 测试无效布尔值
        is_valid, error = service._validate_output_format("maybe", "boolean")
        assert is_valid is True  # 返回原始值
        assert error is None
    
    def test_validate_output_format_empty(self, service):
        """测试空输出验证"""
        is_valid, error = service._validate_output_format("", "string")
        assert is_valid is False
        assert "命令输出为空" in error
    
    def test_validate_output_format_too_long(self, service):
        """测试过长输出验证"""
        long_output = "x" * 20000  # 超过10000字符限制
        is_valid, error = service._validate_output_format(long_output, "string")
        assert is_valid is False
        assert "输出内容过长" in error
    
    @pytest.mark.asyncio
    async def test_validate_monitor_config_valid(self, service):
        """测试有效的监控配置验证"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate:
            mock_validate.return_value = (True, None)
            
            is_valid, messages = await service.validate_monitor_config(
                name="test_monitor",
                command="echo 'test'",
                data_type="string",
                timeout=30,
                category="test"
            )
            
            assert is_valid is True
            assert "命令安全验证通过" in messages
    
    @pytest.mark.asyncio
    async def test_validate_monitor_config_invalid_name(self, service):
        """测试无效名称的监控配置验证"""
        is_valid, messages = await service.validate_monitor_config(
            name="",  # 空名称
            command="echo 'test'",
            data_type="string"
        )
        
        assert is_valid is False
        assert any("监控项名称不能为空" in msg for msg in messages)
    
    @pytest.mark.asyncio
    async def test_validate_monitor_config_invalid_command(self, service):
        """测试无效命令的监控配置验证"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate:
            mock_validate.return_value = (False, "Dangerous command")
            
            is_valid, messages = await service.validate_monitor_config(
                name="test_monitor",
                command="rm -rf /",
                data_type="string"
            )
            
            assert is_valid is False
            assert any("命令安全验证失败" in msg for msg in messages)
    
    @pytest.mark.asyncio
    async def test_validate_monitor_config_invalid_data_type(self, service):
        """测试无效数据类型的监控配置验证"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate:
            mock_validate.return_value = (True, None)
            
            is_valid, messages = await service.validate_monitor_config(
                name="test_monitor",
                command="echo 'test'",
                data_type="invalid_type"  # 无效数据类型
            )
            
            assert is_valid is False
            assert any("数据类型必须是以下之一" in msg for msg in messages)
    
    @pytest.mark.asyncio
    async def test_validate_monitor_config_invalid_timeout(self, service):
        """测试无效超时时间的监控配置验证"""
        with patch.object(service.security_validator, 'validate_command') as mock_validate:
            mock_validate.return_value = (True, None)
            
            is_valid, messages = await service.validate_monitor_config(
                name="test_monitor",
                command="echo 'test'",
                data_type="string",
                timeout=500  # 超过300秒限制
            )
            
            assert is_valid is False
            assert any("超时时间必须在1-300秒之间" in msg for msg in messages)
    
    def test_get_command_suggestions(self, service):
        """测试获取命令建议"""
        # 获取所有建议
        suggestions = service.get_command_suggestions()
        assert len(suggestions) > 0
        assert all('category' in s for s in suggestions)
        assert all('name' in s for s in suggestions)
        assert all('command' in s for s in suggestions)
        
        # 按分类过滤
        system_suggestions = service.get_command_suggestions("system")
        assert len(system_suggestions) > 0
        assert all(s['category'] == 'system' for s in system_suggestions)
    
    @pytest.mark.asyncio
    async def test_batch_test_monitor_items(self, service):
        """测试批量测试监控项"""
        test_configs = [
            {
                'command': 'echo "test1"',
                'ip': '*************',
                'credentials': {'username': 'test', 'password': 'test'},
                'data_type': 'string'
            },
            {
                'command': 'echo "test2"',
                'ip': '*************',
                'credentials': {'username': 'test', 'password': 'test'},
                'data_type': 'string'
            }
        ]
        
        with patch.object(service, 'test_monitor_command') as mock_test:
            # 第一个成功，第二个失败
            mock_test.side_effect = [
                (True, None),
                (False, "Connection failed")
            ]
            
            results = await service.batch_test_monitor_items(test_configs)
            
            assert results['total'] == 2
            assert results['success'] == 1
            assert results['failed'] == 1
            assert len(results['details']) == 2
            assert results['details'][0]['success'] is True
            assert results['details'][1]['success'] is False
