import axios from 'axios'
import { getCache, setCache, generateCacheKey } from './cache'

// 创建axios实例
const service = axios.create({
  baseURL: 'http://192.168.128.1:8000/', // 基础URL
  timeout: 5000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 设置请求头
    config.headers.accept = 'application/json'

    // 检查是否需要使用缓存
    if (config.useCache && config.method.toLowerCase() === 'get') {
      const cacheKey = generateCacheKey(config.url, config.params)
      const cachedData = getCache(cacheKey)

      if (cachedData) {
        // 如果有缓存，取消请求并返回缓存数据
        const source = axios.CancelToken.source()
        config.cancelToken = source.token
        source.cancel(JSON.stringify(cachedData))
      }
    }

    return config
  },
  error => {
    console.error(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果请求配置中设置了使用缓存，则缓存响应数据
    const config = response.config
    if (config.useCache && config.method.toLowerCase() === 'get') {
      const cacheKey = generateCacheKey(config.url, config.params)
      setCache(cacheKey, response.data, config.cacheTime)
    }

    return response.data
  },
  error => {
    // 如果是因为缓存而取消的请求，直接返回缓存的数据
    if (axios.isCancel(error)) {
      return JSON.parse(error.message)
    }

    // 请求失败重试机制
    const config = error.config
    if (!config || !config.retry) {
      console.error('请求错误:', error)
      return Promise.reject(error)
    }

    // 设置重试计数器
    config.__retryCount = config.__retryCount || 0

    // 检查是否已经达到最大重试次数
    if (config.__retryCount >= config.retry) {
      console.error('请求重试失败:', error)
      return Promise.reject(error)
    }

    // 增加重试计数
    config.__retryCount += 1
    console.log(`请求重试 (${config.__retryCount}/${config.retry}):`, config.url)

    // 创建新的Promise来处理重试
    const backoff = new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, config.retryDelay || 1000)
    })

    // 重试请求
    return backoff.then(() => {
      return service(config)
    })
  }
)

/**
 * 创建请求
 * @param {Object} config - 请求配置
 * @param {boolean} [config.useCache=false] - 是否使用缓存
 * @param {number} [config.cacheTime] - 缓存时间（毫秒）
 * @param {number} [config.retry=0] - 重试次数
 * @param {number} [config.retryDelay=1000] - 重试延迟（毫秒）
 * @returns {Promise} - 请求Promise
 */
export default service
