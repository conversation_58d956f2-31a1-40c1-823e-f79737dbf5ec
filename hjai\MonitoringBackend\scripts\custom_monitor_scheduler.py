"""
自定义监控调度器

负责调度和执行自定义监控项，集成到现有的监控系统中
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.custom_monitor import MonitorItem, MonitorItemIP
from models.ip_user import IPUser
from services.custom_monitor_executor import CustomMonitorExecutor
from core.monitor_status_manager import get_status_manager
from core.error_handler import get_error_handler
from utils.ssh_connection_pool import get_connection_pool
from config.timezone_utils import TZ

logger = logging.getLogger(__name__)


class CustomMonitorScheduler:
    """自定义监控调度器"""
    
    def __init__(
        self,
        default_interval: int = 300,  # 5分钟
        max_concurrent_executions: int = 20,
        batch_size: int = 10
    ):
        self.default_interval = default_interval
        self.max_concurrent_executions = max_concurrent_executions
        self.batch_size = batch_size
        
        # 组件实例
        self.executor = CustomMonitorExecutor()
        self.status_manager = get_status_manager()
        self.error_handler = get_error_handler()
        self.connection_pool = get_connection_pool()
        
        # 调度状态
        self.running = False
        self.scheduler_task = None
        self.last_execution_time = None
        
        # 执行统计
        self.stats = {
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_cycle_time': 0.0,
            'last_cycle_time': None
        }
    
    async def start(self, interval: Optional[int] = None):
        """启动自定义监控调度器"""
        if self.running:
            logger.warning("自定义监控调度器已在运行")
            return
        
        if interval:
            self.default_interval = interval
        
        self.running = True
        
        # 启动连接池
        if not hasattr(self.connection_pool, '_running') or not self.connection_pool._running:
            await self.connection_pool.start()
        
        # 启动调度任务
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        logger.info(f"自定义监控调度器已启动，执行间隔: {self.default_interval}秒")
    
    async def stop(self):
        """停止自定义监控调度器"""
        self.running = False
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        logger.info("自定义监控调度器已停止")
    
    async def _scheduler_loop(self):
        """调度循环"""
        while self.running:
            try:
                cycle_start_time = time.time()
                
                logger.info("开始自定义监控执行周期")
                
                # 执行监控
                success = await self._execute_monitoring_cycle()
                
                cycle_time = time.time() - cycle_start_time
                
                # 更新统计信息
                self._update_cycle_stats(success, cycle_time)
                
                logger.info(f"自定义监控周期完成，耗时: {cycle_time:.2f}秒，成功: {success}")
                
                # 等待下次执行
                await asyncio.sleep(self.default_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"自定义监控调度异常: {str(e)}")
                self.error_handler.handle_error(
                    "自定义监控调度异常",
                    exception=e,
                    context={'scheduler': 'custom_monitor'}
                )
                # 发生异常时等待一段时间后重试
                await asyncio.sleep(60)
    
    async def _execute_monitoring_cycle(self) -> bool:
        """执行监控周期"""
        try:
            # 获取启用的监控项
            monitor_items = await self._get_enabled_monitor_items()
            if not monitor_items:
                logger.debug("没有启用的自定义监控项")
                return True
            
            # 获取服务器列表
            servers = await self._get_servers_for_monitoring()
            if not servers:
                logger.debug("没有可用的服务器")
                return True
            
            logger.info(f"开始执行 {len(monitor_items)} 个监控项，覆盖 {len(servers)} 台服务器")
            
            # 按批次执行监控
            success_count = 0
            total_count = 0
            
            for i in range(0, len(monitor_items), self.batch_size):
                batch_items = monitor_items[i:i + self.batch_size]
                
                batch_results = await self.executor.execute_monitor_items_batch(
                    monitor_items=batch_items,
                    servers=servers,
                    max_concurrent=self.max_concurrent_executions
                )
                
                # 处理批次结果
                batch_success, batch_total = self._process_batch_results(batch_results)
                success_count += batch_success
                total_count += batch_total
                
                # 批次间短暂暂停
                if i + self.batch_size < len(monitor_items):
                    await asyncio.sleep(1)
            
            # 更新执行统计
            self.stats['total_executions'] += total_count
            self.stats['successful_executions'] += success_count
            self.stats['failed_executions'] += (total_count - success_count)
            
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            logger.info(f"监控周期执行完成: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")
            
            return success_rate >= 80  # 成功率80%以上认为周期成功
            
        except Exception as e:
            logger.error(f"监控周期执行失败: {str(e)}")
            self.error_handler.handle_error(
                "监控周期执行失败",
                exception=e,
                context={'cycle_time': datetime.now().isoformat()}
            )
            return False
    
    async def _get_enabled_monitor_items(self) -> List[MonitorItem]:
        """获取启用的监控项"""
        try:
            # 尝试使用新版表结构查询（包含is_deleted字段）
            try:
                return await MonitorItem.filter(enabled=True, is_deleted=False).all()
            except Exception:
                # 如果失败，说明表中没有is_deleted列，使用旧版查询
                logger.warning("监控项表结构未更新，使用旧版查询")
                return await MonitorItem.filter(enabled=True).all()
        except Exception as e:
            logger.error(f"获取监控项失败: {str(e)}")
            return []
    
    async def _get_servers_for_monitoring(self) -> List[Dict[str, Any]]:
        """获取用于监控的服务器列表"""
        try:
            # 尝试使用新版表结构查询（包含is_deleted和is_connectable字段）
            try:
                ip_users = await IPUser.filter(is_deleted=False, is_connectable=True).all()
            except Exception:
                # 如果失败，说明表中没有is_deleted或is_connectable列，使用旧版查询
                logger.warning("IP用户表结构未更新，使用旧版查询")
                try:
                    ip_users = await IPUser.filter(is_connectable=True).all()
                except Exception:
                    # 如果is_connectable字段也不存在，查询所有用户
                    ip_users = await IPUser.all()

            servers = []
            for ip_user in ip_users:
                # 检查是否有use_ssh_key字段，如果没有则根据密码判断
                use_ssh_key = getattr(ip_user, 'use_ssh_key', not bool(ip_user.password))
                ssh_key_path = getattr(ip_user, 'ssh_key_path', None)

                server_info = {
                    'ip': ip_user.ip,
                    'username': ip_user.username,
                    'password': ip_user.password,
                    'use_ssh_key': use_ssh_key,
                    'ssh_key_path': ssh_key_path
                }
                servers.append(server_info)

            return servers

        except Exception as e:
            logger.error(f"获取服务器列表失败: {str(e)}")
            return []
    
    def _process_batch_results(self, batch_results: Dict[str, Dict[str, Any]]) -> tuple[int, int]:
        """处理批次执行结果"""
        success_count = 0
        total_count = 0
        
        for server_ip, server_results in batch_results.items():
            for monitor_item_id, result in server_results.items():
                total_count += 1
                
                # 更新监控状态
                self.status_manager.update_monitor_item_status(
                    server_ip=server_ip,
                    monitor_item_id=int(monitor_item_id),
                    monitor_item_name=result.get('monitor_item_name', 'Unknown'),
                    success=result.get('success', False),
                    error_message=result.get('error'),
                    execution_time=0.0  # TODO: 添加执行时间记录
                )
                
                if result.get('success', False):
                    success_count += 1
                else:
                    # 记录失败详情
                    logger.debug(f"监控执行失败: {server_ip}:{monitor_item_id} - {result.get('error')}")
        
        return success_count, total_count
    
    def _update_cycle_stats(self, success: bool, cycle_time: float):
        """更新周期统计信息"""
        self.stats['total_cycles'] += 1
        
        if success:
            self.stats['successful_cycles'] += 1
        else:
            self.stats['failed_cycles'] += 1
        
        # 更新平均周期时间
        if self.stats['average_cycle_time'] == 0:
            self.stats['average_cycle_time'] = cycle_time
        else:
            total_time = self.stats['average_cycle_time'] * (self.stats['total_cycles'] - 1) + cycle_time
            self.stats['average_cycle_time'] = total_time / self.stats['total_cycles']
        
        self.stats['last_cycle_time'] = TZ.now_utc().isoformat()
        self.last_execution_time = TZ.now_utc()
    
    async def execute_monitor_item_now(
        self,
        monitor_item_id: int,
        server_ips: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """立即执行指定的监控项"""
        try:
            # 获取监控项
            monitor_item = await MonitorItem.filter(id=monitor_item_id).first()
            if not monitor_item:
                return {'success': False, 'error': f'监控项 {monitor_item_id} 不存在'}
            
            # 获取目标服务器
            if server_ips:
                # 使用指定的服务器
                ip_users = await IPUser.filter(ip__in=server_ips).all()
            else:
                # 获取该监控项关联的所有服务器
                monitor_ips = await MonitorItemIP.filter(monitor_item_id=monitor_item_id).all()
                if not monitor_ips:
                    return {'success': False, 'error': '监控项没有关联任何服务器'}
                
                ip_list = [mip.ip_address for mip in monitor_ips]
                ip_users = await IPUser.filter(ip__in=ip_list).all()
            
            if not ip_users:
                return {'success': False, 'error': '没有找到可用的服务器'}
            
            # 构造服务器信息
            servers = []
            for ip_user in ip_users:
                server_info = {
                    'ip': ip_user.ip,
                    'username': ip_user.username,
                    'password': ip_user.password,
                    'use_ssh_key': not bool(ip_user.password),
                    'ssh_key_path': None
                }
                servers.append(server_info)
            
            # 执行监控
            results = {}
            for server in servers:
                success, error, data = await self.executor.execute_monitor_item(
                    monitor_item, server, save_execution_record=True
                )
                
                results[server['ip']] = {
                    'success': success,
                    'error': error,
                    'data': data
                }
                
                # 更新状态
                self.status_manager.update_monitor_item_status(
                    server_ip=server['ip'],
                    monitor_item_id=monitor_item.id,
                    monitor_item_name=monitor_item.name,
                    success=success,
                    error_message=error
                )
            
            return {'success': True, 'results': results}
            
        except Exception as e:
            error_msg = f"立即执行监控项失败: {str(e)}"
            logger.error(error_msg)
            self.error_handler.handle_error(error_msg, exception=e)
            return {'success': False, 'error': error_msg}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        return {
            **self.stats,
            'running': self.running,
            'default_interval': self.default_interval,
            'max_concurrent_executions': self.max_concurrent_executions,
            'batch_size': self.batch_size,
            'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
            'executor_stats': self.executor.get_stats(),
            'connection_pool_stats': self.connection_pool.get_stats()
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_cycle_time': 0.0,
            'last_cycle_time': None
        }
        self.executor.reset_stats()


# 全局自定义监控调度器实例
_custom_monitor_scheduler: Optional[CustomMonitorScheduler] = None


def get_custom_monitor_scheduler() -> CustomMonitorScheduler:
    """获取全局自定义监控调度器实例"""
    global _custom_monitor_scheduler
    if _custom_monitor_scheduler is None:
        _custom_monitor_scheduler = CustomMonitorScheduler()
    return _custom_monitor_scheduler
