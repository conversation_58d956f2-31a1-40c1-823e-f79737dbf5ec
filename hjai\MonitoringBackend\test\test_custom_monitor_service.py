"""
自定义监控服务测试

测试自定义监控服务的各项功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from services.custom_monitor_service import CustomMonitorService
from services.monitor_test_service import MonitorTestService
from models.custom_monitor import MonitorItem, MonitorItemIP, MonitorData


class TestCustomMonitorService:
    """自定义监控服务测试类"""
    
    @pytest.fixture
    async def service(self):
        """创建服务实例"""
        return CustomMonitorService()
    
    @pytest.fixture
    async def mock_monitor_item(self):
        """模拟监控项"""
        item = Mock()
        item.id = 1
        item.name = "test_monitor"
        item.command = "echo 'test'"
        item.description = "Test monitor item"
        item.data_type = "string"
        item.timeout = 30
        item.retry_count = 2
        item.category = "test"
        item.security_level = "normal"
        item.enabled = True
        return item
    
    @pytest.mark.asyncio
    async def test_create_monitor_item_success(self, service):
        """测试成功创建监控项"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter, \
             patch('models.custom_monitor.MonitorItem.create') as mock_create, \
             patch.object(service.test_service, 'test_monitor_command') as mock_test:
            
            # 设置模拟
            mock_filter.return_value.first = AsyncMock(return_value=None)
            mock_create.return_value = Mock(id=1, name="test_monitor")
            mock_test.return_value = (True, None)
            
            # 执行测试
            result, test_success, error = await service.create_monitor_item(
                name="test_monitor",
                command="echo 'test'",
                description="Test monitor",
                data_type="string",
                test_ip="*************",
                test_credentials={"username": "test", "password": "test"}
            )
            
            # 验证结果
            assert result is not None
            assert test_success is True
            assert error is None
            mock_create.assert_called_once()
            mock_test.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_monitor_item_duplicate_name(self, service):
        """测试创建重复名称的监控项"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter:
            # 设置模拟 - 返回已存在的项目
            existing_item = Mock()
            mock_filter.return_value.first = AsyncMock(return_value=existing_item)
            
            # 执行测试
            result, test_success, error = await service.create_monitor_item(
                name="existing_monitor",
                command="echo 'test'"
            )
            
            # 验证结果
            assert result is None
            assert test_success is False
            assert "已存在名称为" in error
    
    @pytest.mark.asyncio
    async def test_create_monitor_item_test_failure(self, service):
        """测试监控项测试失败的情况"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter, \
             patch('models.custom_monitor.MonitorItem.create') as mock_create, \
             patch.object(service.test_service, 'test_monitor_command') as mock_test:
            
            # 设置模拟
            mock_filter.return_value.first = AsyncMock(return_value=None)
            mock_create.return_value = Mock(id=1, name="test_monitor")
            mock_test.return_value = (False, "Connection failed")
            
            # 执行测试
            result, test_success, error = await service.create_monitor_item(
                name="test_monitor",
                command="echo 'test'",
                test_ip="*************",
                test_credentials={"username": "test", "password": "test"}
            )
            
            # 验证结果
            assert result is not None  # 仍然创建了监控项
            assert test_success is False
            assert error == "Connection failed"
    
    @pytest.mark.asyncio
    async def test_update_monitor_item_success(self, service):
        """测试成功更新监控项"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter:
            # 设置模拟
            mock_item = Mock()
            mock_item.save = AsyncMock()
            mock_item.name = "updated_name"
            mock_filter.return_value.first = AsyncMock(return_value=mock_item)
            
            # 执行测试
            success, error = await service.update_monitor_item(1, name="updated_name")
            
            # 验证结果
            assert success is True
            assert error is None
            assert mock_item.name == "updated_name"
            mock_item.save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_monitor_item_not_found(self, service):
        """测试更新不存在的监控项"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter:
            # 设置模拟 - 返回None
            mock_filter.return_value.first = AsyncMock(return_value=None)
            
            # 执行测试
            success, error = await service.update_monitor_item(999, name="updated_name")
            
            # 验证结果
            assert success is False
            assert "未找到ID为 999 的监控项" in error
    
    @pytest.mark.asyncio
    async def test_delete_monitor_item_success(self, service):
        """测试成功删除监控项"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter, \
             patch('models.custom_monitor.MonitorItemIP.filter') as mock_ip_filter, \
             patch('models.custom_monitor.MonitorData.filter') as mock_data_filter:
            
            # 设置模拟
            mock_item = Mock()
            mock_item.delete = AsyncMock()
            mock_item.name = "test_monitor"
            mock_filter.return_value.first = AsyncMock(return_value=mock_item)
            mock_ip_filter.return_value.delete = AsyncMock()
            mock_data_filter.return_value.delete = AsyncMock()
            
            # 执行测试
            success, error = await service.delete_monitor_item(1)
            
            # 验证结果
            assert success is True
            assert error is None
            mock_item.delete.assert_called_once()
            mock_ip_filter.return_value.delete.assert_called_once()
            mock_data_filter.return_value.delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_monitor_item_ip_success(self, service):
        """测试成功添加监控项IP关联"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_item_filter, \
             patch('models.custom_monitor.MonitorItemIP.filter') as mock_ip_filter, \
             patch('models.custom_monitor.MonitorItemIP.create') as mock_create:
            
            # 设置模拟
            mock_item = Mock()
            mock_item.name = "test_monitor"
            mock_item_filter.return_value.first = AsyncMock(return_value=mock_item)
            mock_ip_filter.return_value.first = AsyncMock(return_value=None)
            mock_create.return_value = Mock()
            
            # 执行测试
            success, error = await service.add_monitor_item_ip(1, "*************")
            
            # 验证结果
            assert success is True
            assert error is None
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_monitor_item_ip_duplicate(self, service):
        """测试添加重复的监控项IP关联"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_item_filter, \
             patch('models.custom_monitor.MonitorItemIP.filter') as mock_ip_filter:
            
            # 设置模拟
            mock_item = Mock()
            mock_item.name = "test_monitor"
            mock_item_filter.return_value.first = AsyncMock(return_value=mock_item)
            
            # 返回已存在的关联
            existing_relation = Mock()
            mock_ip_filter.return_value.first = AsyncMock(return_value=existing_relation)
            
            # 执行测试
            success, error = await service.add_monitor_item_ip(1, "*************")
            
            # 验证结果
            assert success is False
            assert "已关联IP地址" in error
    
    @pytest.mark.asyncio
    async def test_save_monitor_data_success(self, service):
        """测试成功保存监控数据"""
        with patch('models.custom_monitor.MonitorItem.filter') as mock_filter, \
             patch('models.custom_monitor.MonitorData.create_data') as mock_create:
            
            # 设置模拟
            mock_item = Mock()
            mock_filter.return_value.first = AsyncMock(return_value=mock_item)
            mock_create.return_value = Mock()
            
            # 执行测试
            success, error = await service.save_monitor_data(
                item_id=1,
                ip="*************",
                value="test_value",
                status=0
            )
            
            # 验证结果
            assert success is True
            assert error is None
            mock_create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_monitor_statistics(self, service):
        """测试获取监控统计信息"""
        with patch('models.custom_monitor.MonitorData.filter') as mock_filter:
            # 设置模拟数据
            mock_data = [
                Mock(status=0),  # 成功
                Mock(status=0),  # 成功
                Mock(status=1),  # 警告
                Mock(status=2),  # 错误
            ]
            mock_filter.return_value.all = AsyncMock(return_value=mock_data)
            
            # 执行测试
            stats = await service.get_monitor_statistics(1, 24)
            
            # 验证结果
            assert stats['total_count'] == 4
            assert stats['success_count'] == 2
            assert stats['warning_count'] == 1
            assert stats['error_count'] == 1
            assert stats['success_rate'] == 50.0
    
    @pytest.mark.asyncio
    async def test_batch_enable_monitor_items(self, service):
        """测试批量启用监控项"""
        with patch.object(service, 'enable_monitor_item') as mock_enable:
            # 设置模拟 - 第一个成功，第二个失败
            mock_enable.side_effect = [
                (True, None),
                (False, "Item not found")
            ]
            
            # 执行测试
            results = await service.batch_enable_monitor_items([1, 2])
            
            # 验证结果
            assert len(results['success']) == 1
            assert len(results['failed']) == 1
            assert results['success'][0] == 1
            assert results['failed'][0]['id'] == 2
            assert results['failed'][0]['error'] == "Item not found"
