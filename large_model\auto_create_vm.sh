#!/bin/bash

# 检查是否以root权限运行
if [ "$(id -u)" -ne 0 ]; then
   echo "此脚本需要root权限，请使用sudo运行"
   echo "示例：sudo $0"
   exit 1
fi

# 默认参数
VM_NAME="ubuntu-vm-nat"
VM_VCPUS=2
VM_RAM=2048
VM_DISK_SIZE=20
USERNAME="ubuntu"
PASSWORD="ubuntu"
SSH_PUB_KEY=""
CLOUD_IMAGE_URL="https://cloud-images.ubuntu.com/releases/22.04/release/ubuntu-22.04-server-cloudimg-amd64.img"
IMAGES_DIR="/var/lib/libvirt/images"
NETWORK_NAME="default"

# 显示帮助信息
show_help() {
  echo "用法: $0 [选项...]"
  echo "选项:"
  echo "  --name NAME         设置虚拟机名称 (默认: $VM_NAME)"
  echo "  --vcpus N           设置虚拟CPU数量 (默认: $VM_VCPUS)"
  echo "  --ram MB            设置内存大小，单位MB (默认: $VM_RAM)"
  echo "  --disk GB           设置磁盘大小，单位GB (默认: $VM_DISK_SIZE)"
  echo "  --user USERNAME     设置用户名 (默认: $USERNAME)"
  echo "  --password PASSWORD 设置密码 (默认: $PASSWORD)"
  echo "  --ssh-key FILE      SSH公钥文件路径"
  echo "  --network NAME      设置网络名称 (默认: $NETWORK_NAME)"
  echo "  --help              显示此帮助信息"
  exit 0
}

# 参数解析
while [ $# -gt 0 ]; do
  case "$1" in
    --name)
      VM_NAME="$2"
      shift 2
      ;;
    --vcpus)
      VM_VCPUS="$2"
      shift 2
      ;;
    --ram)
      VM_RAM="$2"
      shift 2
      ;;
    --disk)
      VM_DISK_SIZE="$2"
      shift 2
      ;;
    --user)
      USERNAME="$2"
      shift 2
      ;;
    --password)
      PASSWORD="$2"
      shift 2
      ;;
    --ssh-key)
      if [ -f "$2" ]; then
        SSH_PUB_KEY=$(cat "$2")
      else
        echo "错误: SSH公钥文件 '$2' 不存在"
        exit 1
      fi
      shift 2
      ;;
    --network)
      NETWORK_NAME="$2"
      shift 2
      ;;
    --help)
      show_help
      ;;
    *)
      echo "未知参数: $1"
      show_help
      ;;
  esac
done

# 检查并安装必要的软件包
echo "检查必要的软件包..."
PACKAGES="qemu-utils libvirt-daemon-system virtinst cloud-image-utils"
for pkg in $PACKAGES; do
  if ! dpkg -l | grep -q $pkg; then
    echo "安装 $pkg 中..."
    apt-get update && apt-get install -y $pkg
  fi
done

# 设置路径
DISK_PATH="${IMAGES_DIR}/${VM_NAME}.qcow2"
CLOUD_IMAGE="${IMAGES_DIR}/$(basename ${CLOUD_IMAGE_URL})"
USER_DATA="${IMAGES_DIR}/${VM_NAME}-user-data"
META_DATA="${IMAGES_DIR}/${VM_NAME}-meta-data"
CLOUD_INIT_ISO="${IMAGES_DIR}/${VM_NAME}-cloud-init.iso"

# 确保目录存在
mkdir -p ${IMAGES_DIR}
chmod 755 ${IMAGES_DIR}

# 下载cloud镜像
download_cloud_image() {
  if [ ! -f "${CLOUD_IMAGE}" ]; then
    echo "下载Ubuntu Cloud镜像中..."
    wget -O "${CLOUD_IMAGE}" "${CLOUD_IMAGE_URL}" || { echo "下载镜像失败，退出"; exit 1; }
  else
    echo "使用已存在的镜像: ${CLOUD_IMAGE}"
  fi
}

# 创建cloud-init配置
create_cloud_init_config() {
  echo "创建cloud-init配置..."
  
  # 创建user-data文件
  cat > "${USER_DATA}" <<EOF
#cloud-config
hostname: ${VM_NAME}
fqdn: ${VM_NAME}.local
manage_etc_hosts: true
users:
  - name: ${USERNAME}
    sudo: ALL=(ALL) NOPASSWD:ALL
    groups: users, admin
    home: /home/<USER>
    shell: /bin/bash
    lock_passwd: false
    passwd: $(openssl passwd -6 ${PASSWORD})
$([ ! -z "${SSH_PUB_KEY}" ] && echo "    ssh-authorized-keys:" || echo "")
$([ ! -z "${SSH_PUB_KEY}" ] && echo "      - ${SSH_PUB_KEY}" || echo "")
package_upgrade: true
packages:
  - qemu-guest-agent
runcmd:
  - systemctl enable qemu-guest-agent
  - systemctl start qemu-guest-agent
power_state:
  mode: reboot
  timeout: 30
  condition: True
EOF

  # 创建meta-data文件
  cat > "${META_DATA}" <<EOF
instance-id: ${VM_NAME}
local-hostname: ${VM_NAME}
EOF

  # 创建ISO
  cloud-localds "${CLOUD_INIT_ISO}" "${USER_DATA}" "${META_DATA}" || { echo "创建cloud-init ISO失败"; exit 1; }
}

# 创建虚拟机
create_vm() {
  echo "创建虚拟机磁盘..."
  qemu-img create -f qcow2 -o backing_file="${CLOUD_IMAGE}" "${DISK_PATH}" ${VM_DISK_SIZE}G || { echo "创建磁盘失败，退出"; exit 1; }
  
  echo "调整磁盘大小..."
  qemu-img resize "${DISK_PATH}" ${VM_DISK_SIZE}G || { echo "调整磁盘大小失败，退出"; exit 1; }
  
  echo "创建并启动虚拟机..."
  virt-install \
    --name ${VM_NAME} \
    --ram ${VM_RAM} \
    --vcpus ${VM_VCPUS} \
    --disk path=${DISK_PATH},format=qcow2 \
    --disk path=${CLOUD_INIT_ISO},device=cdrom \
    --os-variant ubuntu22.04 \
    --virt-type kvm \
    --graphics none \
    --network network=${NETWORK_NAME} \
    --import \
    --noautoconsole \
    --quit || { echo "虚拟机创建失败，请检查错误信息"; exit 1; }
}

# 等待虚拟机启动和配置完成
wait_for_vm() {
  echo "等待虚拟机启动和配置..."
  
  # 等待虚拟机获取IP地址
  max_attempts=30
  attempts=0
  ip_address=""
  
  while [ $attempts -lt $max_attempts ]; do
    sleep 10
    attempts=$((attempts+1))
    
    echo "尝试获取虚拟机IP地址 (尝试 $attempts/$max_attempts)..."
    ip_address=$(virsh domifaddr ${VM_NAME} | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -n 1)
    
    if [ ! -z "$ip_address" ]; then
      echo "虚拟机IP地址: $ip_address"
      break
    fi
  done
  
  if [ -z "$ip_address" ]; then
    echo "警告: 无法获取虚拟机IP地址，请稍后手动检查"
  fi
}

# 主要流程
main() {
  echo "开始创建虚拟机: ${VM_NAME}"
  
  # 检查虚拟机是否已存在
  if virsh dominfo "${VM_NAME}" &>/dev/null; then
    echo "错误: 虚拟机 '${VM_NAME}' 已存在"
    echo "请选择一个不同的名称或使用 'virsh undefine ${VM_NAME}' 删除现有虚拟机"
    exit 1
  fi
  
  download_cloud_image
  create_cloud_init_config
  create_vm
  wait_for_vm
  
  echo "虚拟机创建完成!"
  echo "虚拟机名称: ${VM_NAME}"
  if [ ! -z "$ip_address" ]; then
    echo "虚拟机IP: $ip_address"
    echo "SSH连接命令: ssh ${USERNAME}@${ip_address}"
  fi
  echo "用户名: ${USERNAME}"
  echo "密码: ${PASSWORD}"
  echo ""
  echo "管理命令:"
  echo "  查看虚拟机列表: virsh list --all"
  echo "  连接虚拟机控制台: virsh console ${VM_NAME}"
  echo "  启动虚拟机: virsh start ${VM_NAME}"
  echo "  关闭虚拟机: virsh shutdown ${VM_NAME}"
  echo "  强制关闭虚拟机: virsh destroy ${VM_NAME}"
  echo "  删除虚拟机: virsh undefine ${VM_NAME} --remove-all-storage"
}

# 执行主流程
main