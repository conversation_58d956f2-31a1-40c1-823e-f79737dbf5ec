# 自定义监控项 API 文档

## 概述

自定义监控项模块允许用户创建、管理和执行自定义的监控命令，支持多种数据类型和安全验证机制。

## 基础信息

- **基础路径**: `/monitor`
- **认证方式**: API Key 或 Session Token
- **数据格式**: JSON
- **时区**: UTC (响应中包含时区信息)

## 数据模型

### MonitorItem (监控项)

```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "cat /proc/loadavg",
  "description": "获取系统负载信息",
  "data_type": "string",
  "timeout": 30,
  "retry_count": 2,
  "enabled": true,
  "category": "system",
  "security_level": "normal",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### MonitorData (监控数据)

```json
{
  "id": 1,
  "monitor_item_id": 1,
  "ip": "*************",
  "value": "0.15 0.12 0.08",
  "status": 0,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## API 端点

### 1. 监控项管理

#### 创建监控项

```http
POST /monitor/items
```

**请求体:**
```json
{
  "name": "监控项名称",
  "command": "监控命令",
  "description": "描述信息",
  "data_type": "string",
  "timeout": 30,
  "retry_count": 2,
  "category": "system",
  "security_level": "normal"
}
```

**响应:**
```json
{
  "id": 1,
  "name": "监控项名称",
  "message": "监控项创建成功"
}
```

#### 获取监控项列表

```http
GET /monitor/items?category=system&enabled=true&limit=10&offset=0
```

**查询参数:**
- `category` (可选): 分类过滤
- `enabled` (可选): 启用状态过滤
- `limit` (可选): 限制数量，默认100
- `offset` (可选): 偏移量，默认0

**响应:**
```json
[
  {
    "id": 1,
    "name": "CPU使用率",
    "command": "cat /proc/loadavg",
    "description": "获取系统负载信息",
    "data_type": "string",
    "timeout": 30,
    "retry_count": 2,
    "enabled": true,
    "category": "system",
    "security_level": "normal",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 获取单个监控项

```http
GET /monitor/items/{item_id}
```

**响应:**
```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "cat /proc/loadavg",
  "description": "获取系统负载信息",
  "data_type": "string",
  "timeout": 30,
  "retry_count": 2,
  "enabled": true,
  "category": "system",
  "security_level": "normal",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 更新监控项

```http
PUT /monitor/items/{item_id}
```

**请求体:**
```json
{
  "name": "新的监控项名称",
  "description": "新的描述",
  "enabled": false
}
```

**响应:**
```json
{
  "message": "监控项更新成功"
}
```

#### 删除监控项

```http
DELETE /monitor/items/{item_id}
```

**响应:**
```json
{
  "message": "监控项删除成功"
}
```

### 2. 监控项与IP关联

#### 添加IP关联

```http
POST /monitor/items/{item_id}/ips
```

**请求体:**
```json
{
  "ip_address": "*************"
}
```

**响应:**
```json
{
  "message": "IP关联添加成功"
}
```

#### 移除IP关联

```http
DELETE /monitor/items/{item_id}/ips/{ip_address}
```

**响应:**
```json
{
  "message": "IP关联移除成功"
}
```

#### 获取监控项的IP列表

```http
GET /monitor/items/{item_id}/ips
```

**响应:**
```json
{
  "ips": ["*************", "*************"]
}
```

### 3. 监控数据查询

#### 获取监控数据

```http
GET /monitor/data?item_id=1&ip=*************&start_time=2024-01-01T00:00:00Z&end_time=2024-01-01T23:59:59Z&limit=100
```

**查询参数:**
- `item_id` (可选): 监控项ID过滤
- `ip` (可选): IP地址过滤
- `start_time` (可选): 开始时间过滤
- `end_time` (可选): 结束时间过滤
- `limit` (可选): 限制数量，默认100
- `offset` (可选): 偏移量，默认0

**响应:**
```json
[
  {
    "id": 1,
    "monitor_item_id": 1,
    "monitor_item_name": "CPU使用率",
    "ip": "*************",
    "value": "0.15 0.12 0.08",
    "status": 0,
    "timestamp": "2024-01-01T12:00:00Z"
  }
]
```

### 4. 监控测试

#### 测试监控命令

```http
POST /monitor/test
```

**请求体:**
```json
{
  "command": "echo 'test'",
  "ip": "*************",
  "username": "root",
  "password": "password",
  "use_ssh_key": false,
  "timeout": 30,
  "data_type": "string"
}
```

**响应:**
```json
{
  "success": true,
  "error": null,
  "execution_time": 1.5,
  "output_sample": "test",
  "security_level": "low",
  "recommendations": [
    "建议使用只读命令以确保系统安全"
  ]
}
```

#### 验证监控配置

```http
POST /monitor/validate
```

**请求体:**
```json
{
  "name": "测试监控项",
  "command": "cat /proc/cpuinfo",
  "data_type": "string",
  "timeout": 30,
  "category": "system"
}
```

**响应:**
```json
{
  "is_valid": true,
  "messages": [
    "命令安全验证通过",
    "配置验证通过"
  ],
  "security_level": "low",
  "suggestions": [
    {
      "category": "system",
      "name": "CPU信息",
      "command": "cat /proc/cpuinfo",
      "data_type": "string",
      "description": "获取CPU详细信息"
    }
  ]
}
```

#### 获取命令建议

```http
GET /monitor/suggestions?category=system
```

**响应:**
```json
{
  "suggestions": [
    {
      "category": "system",
      "name": "CPU使用率",
      "command": "cat /proc/loadavg",
      "data_type": "string",
      "description": "获取系统负载信息"
    },
    {
      "category": "system",
      "name": "内存使用率",
      "command": "free -m",
      "data_type": "string",
      "description": "获取内存使用情况"
    }
  ]
}
```

### 5. 批量操作

#### 批量操作监控项

```http
POST /monitor/batch
```

**请求体:**
```json
{
  "item_ids": [1, 2, 3],
  "operation": "enable"
}
```

**操作类型:**
- `enable`: 启用监控项
- `disable`: 禁用监控项
- `delete`: 删除监控项

**响应:**
```json
{
  "success_count": 2,
  "failed_count": 1,
  "success_items": [1, 2],
  "failed_items": [
    {
      "id": 3,
      "error": "监控项不存在"
    }
  ]
}
```

### 6. 监控执行

#### 立即执行监控项

```http
POST /monitor/execute
```

**请求体:**
```json
{
  "monitor_item_id": 1,
  "server_ips": ["*************", "*************"]
}
```

**响应:**
```json
{
  "success": true,
  "message": "执行成功",
  "results": {
    "*************": {
      "success": true,
      "error": null,
      "data": "0.15 0.12 0.08"
    },
    "*************": {
      "success": false,
      "error": "连接超时",
      "data": null
    }
  }
}
```

### 7. 状态和统计

#### 获取监控状态概览

```http
GET /monitor/status
```

**响应:**
```json
{
  "global_stats": {
    "total_servers": 10,
    "online_servers": 8,
    "offline_servers": 2,
    "total_monitor_items": 25,
    "healthy_items": 20,
    "warning_items": 3,
    "error_items": 2,
    "overall_health_score": 80.0,
    "last_update_time": "2024-01-01T12:00:00Z"
  },
  "problematic_servers_count": 2,
  "problematic_servers": [
    {
      "server_ip": "*************",
      "server_status": "offline",
      "health_score": 0.0,
      "error_items": 5,
      "total_items": 5
    }
  ]
}
```

#### 获取服务器监控状态

```http
GET /monitor/status/server/{server_ip}
```

**响应:**
```json
{
  "server_ip": "*************",
  "server_status": "online",
  "last_check_time": "2024-01-01T12:00:00Z",
  "total_monitor_items": 5,
  "healthy_items": 4,
  "warning_items": 1,
  "error_items": 0,
  "overall_health_score": 80.0,
  "problematic_items": [
    {
      "monitor_item_id": 1,
      "monitor_item_name": "CPU使用率",
      "status": "warning",
      "consecutive_failures": 2,
      "success_rate": 85.0,
      "last_error": "执行超时",
      "last_check_time": "2024-01-01T12:00:00Z"
    }
  ]
}
```

#### 获取监控统计信息

```http
GET /monitor/stats
```

**响应:**
```json
{
  "scheduler_stats": {
    "running": true,
    "total_cycles": 100,
    "successful_cycles": 95,
    "failed_cycles": 5,
    "total_executions": 1000,
    "successful_executions": 950,
    "failed_executions": 50,
    "average_cycle_time": 45.5,
    "last_cycle_time": "2024-01-01T12:00:00Z"
  },
  "error_stats": {
    "total_errors": 50,
    "errors_by_category": {
      "connection": 20,
      "timeout": 15,
      "command_execution": 10,
      "data_parsing": 5
    },
    "errors_by_level": {
      "low": 10,
      "medium": 25,
      "high": 10,
      "critical": 5
    }
  },
  "system_health": {
    "scheduler_running": true,
    "success_rate": 95.0,
    "average_cycle_time": 45.5,
    "total_errors": 50
  }
}
```

#### 获取监控项统计

```http
GET /monitor/items/{item_id}/statistics?hours=24
```

**响应:**
```json
{
  "total_count": 100,
  "success_count": 95,
  "warning_count": 3,
  "error_count": 2,
  "success_rate": 95.0,
  "avg_execution_time": 1.5,
  "last_execution": "2024-01-01T12:00:00Z"
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复名称） |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 数据类型

支持的监控数据类型：

- `string`: 字符串类型
- `number`: 数字类型（整数或浮点数）
- `json`: JSON对象
- `boolean`: 布尔类型
- `array`: 数组类型

## 安全级别

- `low`: 低风险命令（只读操作）
- `medium`: 中等风险命令
- `high`: 高风险命令
- `critical`: 严重风险命令（禁止执行）

## 状态码

监控数据状态：
- `0`: 正常
- `1`: 警告
- `2`: 错误

监控项状态：
- `unknown`: 未知
- `healthy`: 健康
- `warning`: 警告
- `error`: 错误
- `timeout`: 超时
- `disabled`: 已禁用

服务器状态：
- `unknown`: 未知
- `online`: 在线
- `offline`: 离线
- `unreachable`: 不可达
- `maintenance`: 维护中
