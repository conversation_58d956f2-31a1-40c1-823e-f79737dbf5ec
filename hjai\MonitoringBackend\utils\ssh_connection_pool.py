"""
SSH连接池管理器

提供SSH连接的池化管理，支持连接复用、健康检查、超时管理等功能
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import paramiko
import threading

from utils.ssh_utils import SSHConnectionManager

logger = logging.getLogger(__name__)


@dataclass
class SSHConnectionInfo:
    """SSH连接信息"""
    client: paramiko.SSHClient
    server_key: str  # 服务器唯一标识
    created_at: float
    last_used: float
    use_count: int = 0
    is_healthy: bool = True
    lock: threading.Lock = field(default_factory=threading.Lock)
    
    def __post_init__(self):
        self.last_used = self.created_at
    
    def update_usage(self):
        """更新使用信息"""
        self.last_used = time.time()
        self.use_count += 1
    
    def is_expired(self, max_idle_time: int = 300, max_lifetime: int = 3600) -> bool:
        """检查连接是否过期"""
        now = time.time()
        idle_time = now - self.last_used
        lifetime = now - self.created_at
        
        return idle_time > max_idle_time or lifetime > max_lifetime
    
    def is_active(self) -> bool:
        """检查连接是否活跃"""
        try:
            transport = self.client.get_transport()
            return transport and transport.is_active()
        except Exception:
            return False


class SSHConnectionPool:
    """SSH连接池"""
    
    def __init__(
        self,
        max_connections_per_server: int = 5,
        max_total_connections: int = 50,
        max_idle_time: int = 300,  # 5分钟
        max_lifetime: int = 3600,  # 1小时
        health_check_interval: int = 60,  # 1分钟
        connection_timeout: int = 15
    ):
        self.max_connections_per_server = max_connections_per_server
        self.max_total_connections = max_total_connections
        self.max_idle_time = max_idle_time
        self.max_lifetime = max_lifetime
        self.health_check_interval = health_check_interval
        self.connection_timeout = connection_timeout
        
        # 连接池存储 {server_key: [SSHConnectionInfo]}
        self.pools: Dict[str, List[SSHConnectionInfo]] = {}
        self.pool_lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'pool_hits': 0,
            'pool_misses': 0,
            'connection_errors': 0,
            'health_check_failures': 0
        }
        
        # 健康检查任务
        self._health_check_task = None
        self._running = False
    
    def _generate_server_key(self, server: Dict[str, Any]) -> str:
        """生成服务器唯一标识"""
        return f"{server['ip']}:{server.get('username', 'root')}"
    
    async def start(self):
        """启动连接池"""
        if self._running:
            return
        
        self._running = True
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info("SSH连接池已启动")
    
    async def stop(self):
        """停止连接池"""
        self._running = False
        
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        async with self.pool_lock:
            for server_key, connections in self.pools.items():
                for conn_info in connections:
                    try:
                        conn_info.client.close()
                    except Exception as e:
                        logger.debug(f"关闭连接时出错: {e}")
            
            self.pools.clear()
            self.stats['total_connections'] = 0
            self.stats['active_connections'] = 0
        
        logger.info("SSH连接池已停止")
    
    @asynccontextmanager
    async def get_connection(self, server: Dict[str, Any]):
        """
        获取SSH连接的上下文管理器
        
        Args:
            server: 服务器信息
            
        Yields:
            paramiko.SSHClient: SSH客户端连接
        """
        server_key = self._generate_server_key(server)
        conn_info = None
        
        try:
            # 从连接池获取连接
            conn_info = await self._get_pooled_connection(server_key, server)
            
            if conn_info:
                yield conn_info.client
            else:
                raise Exception("无法获取SSH连接")
                
        finally:
            # 归还连接到池中
            if conn_info:
                await self._return_connection(server_key, conn_info)
    
    async def _get_pooled_connection(
        self, 
        server_key: str, 
        server: Dict[str, Any]
    ) -> Optional[SSHConnectionInfo]:
        """从连接池获取连接"""
        async with self.pool_lock:
            # 尝试从池中获取可用连接
            if server_key in self.pools:
                for conn_info in self.pools[server_key][:]:  # 创建副本以安全迭代
                    if conn_info.lock.acquire(blocking=False):
                        try:
                            # 检查连接是否可用
                            if self._is_connection_usable(conn_info):
                                conn_info.update_usage()
                                self.stats['pool_hits'] += 1
                                logger.debug(f"从连接池获取连接: {server_key}")
                                return conn_info
                            else:
                                # 移除无效连接
                                self.pools[server_key].remove(conn_info)
                                self.stats['total_connections'] -= 1
                                try:
                                    conn_info.client.close()
                                except Exception:
                                    pass
                        finally:
                            conn_info.lock.release()
            
            # 池中没有可用连接，创建新连接
            self.stats['pool_misses'] += 1
            return await self._create_new_connection(server_key, server)
    
    def _is_connection_usable(self, conn_info: SSHConnectionInfo) -> bool:
        """检查连接是否可用"""
        if conn_info.is_expired(self.max_idle_time, self.max_lifetime):
            return False
        
        if not conn_info.is_active():
            return False
        
        return conn_info.is_healthy
    
    async def _create_new_connection(
        self, 
        server_key: str, 
        server: Dict[str, Any]
    ) -> Optional[SSHConnectionInfo]:
        """创建新的SSH连接"""
        # 检查连接数限制
        if self.stats['total_connections'] >= self.max_total_connections:
            logger.warning("已达到最大连接数限制")
            return None
        
        server_connections = len(self.pools.get(server_key, []))
        if server_connections >= self.max_connections_per_server:
            logger.warning(f"服务器 {server_key} 已达到最大连接数限制")
            return None
        
        try:
            # 在线程池中创建SSH连接
            def create_connection():
                return SSHConnectionManager.connect_to_server(server, self.connection_timeout)
            
            ssh_client, connected = await asyncio.get_event_loop().run_in_executor(
                None, create_connection
            )
            
            if not connected or not ssh_client:
                self.stats['connection_errors'] += 1
                return None
            
            # 创建连接信息
            current_time = time.time()
            conn_info = SSHConnectionInfo(
                client=ssh_client,
                server_key=server_key,
                created_at=current_time,
                last_used=current_time
            )
            
            # 获取锁并添加到池中
            conn_info.lock.acquire()
            
            # 添加到连接池
            if server_key not in self.pools:
                self.pools[server_key] = []
            
            self.pools[server_key].append(conn_info)
            self.stats['total_connections'] += 1
            self.stats['active_connections'] += 1
            
            logger.debug(f"创建新SSH连接: {server_key}")
            return conn_info
            
        except Exception as e:
            logger.error(f"创建SSH连接失败: {e}")
            self.stats['connection_errors'] += 1
            return None
    
    async def _return_connection(self, server_key: str, conn_info: SSHConnectionInfo):
        """归还连接到池中"""
        try:
            # 检查连接是否仍然有效
            if self._is_connection_usable(conn_info):
                self.stats['active_connections'] -= 1
                logger.debug(f"归还连接到池: {server_key}")
            else:
                # 移除无效连接
                async with self.pool_lock:
                    if server_key in self.pools and conn_info in self.pools[server_key]:
                        self.pools[server_key].remove(conn_info)
                        self.stats['total_connections'] -= 1
                
                try:
                    conn_info.client.close()
                except Exception:
                    pass
                
                logger.debug(f"移除无效连接: {server_key}")
        
        finally:
            # 释放锁
            if conn_info.lock.locked():
                conn_info.lock.release()

    async def _health_check_loop(self):
        """健康检查循环"""
        while self._running:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")

    async def _perform_health_check(self):
        """执行健康检查"""
        async with self.pool_lock:
            for server_key, connections in list(self.pools.items()):
                for conn_info in connections[:]:  # 创建副本以安全迭代
                    if conn_info.lock.acquire(blocking=False):
                        try:
                            # 检查连接健康状态
                            if not self._check_connection_health(conn_info):
                                # 移除不健康的连接
                                connections.remove(conn_info)
                                self.stats['total_connections'] -= 1
                                self.stats['health_check_failures'] += 1

                                try:
                                    conn_info.client.close()
                                except Exception:
                                    pass

                                logger.debug(f"移除不健康连接: {server_key}")
                        finally:
                            conn_info.lock.release()

                # 清理空的服务器池
                if not connections:
                    del self.pools[server_key]

    def _check_connection_health(self, conn_info: SSHConnectionInfo) -> bool:
        """检查单个连接的健康状态"""
        try:
            # 检查连接是否过期
            if conn_info.is_expired(self.max_idle_time, self.max_lifetime):
                return False

            # 检查连接是否活跃
            if not conn_info.is_active():
                return False

            # 执行简单的健康检查命令
            stdin, stdout, stderr = conn_info.client.exec_command('echo "health_check"', timeout=5)
            output = stdout.read().decode('utf-8').strip()
            exit_status = stdout.channel.recv_exit_status()

            if exit_status == 0 and output == "health_check":
                conn_info.is_healthy = True
                return True
            else:
                conn_info.is_healthy = False
                return False

        except Exception as e:
            logger.debug(f"连接健康检查失败: {e}")
            conn_info.is_healthy = False
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        pool_info = {}
        for server_key, connections in self.pools.items():
            pool_info[server_key] = {
                'connection_count': len(connections),
                'healthy_connections': sum(1 for c in connections if c.is_healthy),
                'average_age': sum(time.time() - c.created_at for c in connections) / len(connections) if connections else 0,
                'total_usage': sum(c.use_count for c in connections)
            }

        return {
            **self.stats,
            'pool_info': pool_info,
            'total_servers': len(self.pools)
        }

    async def clear_expired_connections(self):
        """清理过期连接"""
        async with self.pool_lock:
            for server_key, connections in list(self.pools.items()):
                for conn_info in connections[:]:
                    if conn_info.is_expired(self.max_idle_time, self.max_lifetime):
                        if conn_info.lock.acquire(blocking=False):
                            try:
                                connections.remove(conn_info)
                                self.stats['total_connections'] -= 1
                                conn_info.client.close()
                                logger.debug(f"清理过期连接: {server_key}")
                            except Exception as e:
                                logger.debug(f"清理连接时出错: {e}")
                            finally:
                                conn_info.lock.release()

                # 清理空的服务器池
                if not connections:
                    del self.pools[server_key]

    async def warm_up_connections(self, servers: List[Dict[str, Any]], connections_per_server: int = 2):
        """预热连接池"""
        logger.info(f"开始预热连接池，目标服务器数: {len(servers)}")

        for server in servers:
            server_key = self._generate_server_key(server)

            for i in range(min(connections_per_server, self.max_connections_per_server)):
                try:
                    conn_info = await self._create_new_connection(server_key, server)
                    if conn_info:
                        # 立即归还连接
                        await self._return_connection(server_key, conn_info)
                        logger.debug(f"预热连接成功: {server_key} ({i+1}/{connections_per_server})")
                    else:
                        logger.warning(f"预热连接失败: {server_key}")
                        break
                except Exception as e:
                    logger.error(f"预热连接异常: {server_key} - {e}")
                    break

        logger.info("连接池预热完成")


# 全局连接池实例
_connection_pool: Optional[SSHConnectionPool] = None


def get_connection_pool() -> SSHConnectionPool:
    """获取全局连接池实例"""
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = SSHConnectionPool()
    return _connection_pool


async def initialize_connection_pool(**kwargs):
    """初始化连接池"""
    global _connection_pool
    _connection_pool = SSHConnectionPool(**kwargs)
    await _connection_pool.start()


async def shutdown_connection_pool():
    """关闭连接池"""
    global _connection_pool
    if _connection_pool:
        await _connection_pool.stop()
        _connection_pool = None
