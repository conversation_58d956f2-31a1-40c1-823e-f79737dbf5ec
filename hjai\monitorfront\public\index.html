<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <style>
      /* 添加一个简单的加载动画 */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100vw;
        background-color: #f0f2f5;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
      }
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(64, 158, 255, 0.2);
        border-radius: 50%;
        border-top-color: #409EFF;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    </style>
    <script>
      // 检查当前路径，如果不是admin页面，则自动重定向
      window.addEventListener('load', function() {
        if (window.location.hash !== '#/admin') {
          console.log('自动重定向到管理页面');
          window.location.href = '#/admin';
        }
      });
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div class="loading" id="loading">
      <div class="loading-spinner"></div>
    </div>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script>
      // 在Vue应用挂载后隐藏加载动画
      document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          var loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>
