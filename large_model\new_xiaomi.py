# 使用更底层的方法尝试加载模型
import os
import torch
from transformers import AutoConfig, AutoTokenizer
from transformers.modeling_utils import PreTrainedModel

model_dir = "/mnt/workspace/XiaomiMiMo/MiMo-7B-RL-0530"

# 先加载配置
config = AutoConfig.from_pretrained(model_dir, trust_remote_code=True)
tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)

# 尝试直接从本地文件加载权重
try:
    print("尝试方法1：使用自定义加载方式")
    # 获取模型类
    model_cls = getattr(__import__(f"transformers.models.mimo.modeling_mimo", fromlist=["MiMoForCausalLM"]), "MiMoForCausalLM")
    model = model_cls(config)
    
    # 从safetensors或bin加载权重
    if os.path.exists(os.path.join(model_dir, "pytorch_model.bin")):
        state_dict = torch.load(os.path.join(model_dir, "pytorch_model.bin"), map_location="cpu")
        model.load_state_dict(state_dict, strict=False)
        print("已从pytorch_model.bin加载权重")
    elif os.path.exists(os.path.join(model_dir, "model.safetensors")):
        from safetensors.torch import load_file
        state_dict = load_file(os.path.join(model_dir, "model.safetensors"))
        model.load_state_dict(state_dict, strict=False)
        print("已从model.safetensors加载权重")
    else:
        print("未找到模型权重文件")
except Exception as e:
    print(f"方法1失败：{e}")
    try:
        print("\n尝试方法2：使用from_pretrained但禁用post_init")
        # 尝试临时修补transformers库的问题
        import types
        import transformers.modeling_utils
        
        # 保存原始post_init方法
        original_post_init = transformers.modeling_utils.PreTrainedModel.post_init
        
        # 创建一个不做任何事情的post_init方法
        def dummy_post_init(self):
            print("使用了自定义的post_init方法")
            pass
        
        # 替换post_init方法
        transformers.modeling_utils.PreTrainedModel.post_init = dummy_post_init
        
        # 使用常规方法加载
        from transformers import AutoModelForCausalLM
        model = AutoModelForCausalLM.from_pretrained(
            model_dir,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 恢复原始post_init方法
        transformers.modeling_utils.PreTrainedModel.post_init = original_post_init
        
    except Exception as e:
        print(f"方法2失败：{e}")

# 测试模型
if 'model' in locals():
    model = model.to("cuda")
    
    # 定义提示词
    prompt = "你好，请介绍下你自己。"
    message = [{"role":"system","content":"You are a helpful assistant system"},{"role":"user","content":prompt}]
    text = tokenizer.apply_chat_template(message, tokenize=False, add_generation_prompt=True)
    
    # 将处理后的文本令牌化并转换为模型的输入张量
    model_inputs = tokenizer([text], return_tensors="pt").to("cuda")
    
    # 将数据输入模型得到输出
    with torch.no_grad():
        response = model.generate(model_inputs.input_ids, max_new_tokens=512)
    
    # 对输出的内容进行解码还原
    response_text = tokenizer.batch_decode(response, skip_special_tokens=True)
    print("\n生成的回答:")
    print(response_text)
else:
    print("模型加载失败，无法进行测试") 