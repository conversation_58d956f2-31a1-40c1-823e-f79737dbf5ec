"""
应用程序配置模块
"""

from datetime import datetime
import pytz

# 导入我们自己的时区工具
from config.timezone import now, SHANGHAI_TZ

# 时区设置
TIMEZONE = SHANGHAI_TZ

# 获取当前上海时间
def get_shanghai_time() -> datetime:
    """
    获取上海时区的当前时间
    """
    return now()

# 格式化时间
def format_time(dt: datetime = None, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化时间为指定格式的字符串
    
    Args:
        dt: 要格式化的datetime对象，默认为当前上海时间
        fmt: 日期格式字符串
    
    Returns:
        格式化后的时间字符串
    """
    if dt is None:
        dt = get_shanghai_time()
    return dt.strftime(fmt)
