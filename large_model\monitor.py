# -*- coding: utf-8 -*-
import os
import time
import psutil
import json
from fastapi import FastAPI, WebSocket, Request, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from typing import List
import asyncio

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    print("警告: 未安装GPUtil库，无法监控GPU。安装请使用: pip install gputil")

# HTML模板定义 - 使用纯ASCII字符
with open(os.path.join(os.path.dirname(__file__), 'monitor_template.html'), 'w', encoding='utf-8') as f:
    f.write("""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控</title>
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --background-color: #f8f9fa;
            --card-bg: #ffffff;
            --text-color: #212529;
            --text-secondary: #6c757d;
            --border-color: #dee2e6;
            --success-color: #4cc9f0;
            --warning-color: #f72585;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
            --transition: all 0.2s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        h1 {
            margin: 0;
            color: var(--primary-color);
            font-size: 2rem;
            font-weight: 600;
        }
        
        h2 {
            margin: 0 0 15px 0;
            color: var(--primary-color);
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        h2 i {
            margin-right: 8px;
            opacity: 0.8;
        }
        
        .settings {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            background-color: rgba(67, 97, 238, 0.05);
            padding: 12px;
            border-radius: 8px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            padding: 20px;
            margin-bottom: 24px;
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }
        
        .card:hover {
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .progress-container {
            margin: 12px 0;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .progress {
            height: 10px;
            background-color: #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 8px;
            transition: width 1s ease;
        }
        
        .cpu-cores-container {
            max-height: 320px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            background-color: rgba(248, 249, 250, 0.5);
        }
        
        .cpu-core {
            margin-bottom: 15px;
        }
        
        .cpu-core:last-child {
            margin-bottom: 0;
        }
        
        .core-label {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            margin-bottom: 6px;
            color: var(--text-secondary);
        }
        
        .network-stats {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
            min-width: 120px;
            background-color: rgba(248, 249, 250, 0.7);
            padding: 15px 10px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }
        
        .stat-item:hover {
            background-color: rgba(248, 249, 250, 1);
            transform: translateY(-3px);
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }
        
        .disk-container, .gpu-container {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 5px;
        }
        
        .gpu-item, .disk-item {
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: rgba(248, 249, 250, 0.5);
            transition: var(--transition);
        }
        
        .gpu-item:hover, .disk-item:hover {
            background-color: rgba(248, 249, 250, 0.8);
        }
        
        .gpu-item h3, .disk-item h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--secondary-color);
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .input-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        input[type="number"] {
            width: 70px;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.95rem;
        }
        
        input[type="number"]:focus {
            border-color: var(--accent-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        .mode-switch {
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }
        
        .mode-switch label {
            padding: 8px 12px;
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.9rem;
        }
        
        .mode-switch input[type="radio"] {
            display: none;
        }
        
        .mode-switch input[type="radio"]:checked + span {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        .mode-switch span {
            padding: 8px 12px;
            display: inline-block;
        }
        
        .timestamp {
            font-size: 0.9rem;
            color: var(--text-secondary);
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            background-color: rgba(248, 249, 250, 0.7);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .error-message {
            color: var(--warning-color);
            font-style: italic;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 8px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 8px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .settings {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .stat-item {
                min-width: 100px;
            }
            
            .mode-switch {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>系统资源监控面板</h1>
            <div class="settings">
                <div class="input-group">
                    <label for="intervalInput">刷新间隔:</label>
                    <input type="number" id="intervalInput" min="1" max="60" value="2">
                    <span>秒</span>
                    <button id="setIntervalBtn">应用</button>
                </div>
                <div class="mode-switch">
                    <label>
                        <input type="radio" name="mode" value="websocket" checked>
                        <span>WebSocket</span>
                    </label>
                    <label>
                        <input type="radio" name="mode" value="polling">
                        <span>HTTP轮询</span>
                    </label>
                </div>
            </div>
        </div>
        
        <div class="grid">
            <div class="card">
                <h2><i>[CPU]</i> CPU 信息</h2>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>总体使用率</span>
                        <span id="cpuTotalValue">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" id="cpuTotalBar" style="width: 0%"></div>
                    </div>
                </div>
                <div id="cpuTotalText">加载中...</div>
                <div class="cpu-cores-container">
                    <div id="cpuCores"></div>
                </div>
            </div>
            
            <div class="card">
                <h2><i>[MEM]</i> 内存信息</h2>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>内存使用率</span>
                        <span id="memoryValue">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" id="memBar" style="width: 0%"></div>
                    </div>
                </div>
                <div id="memText">加载中...</div>
            </div>
        </div>
        
        <div class="card">
            <h2><i>[NET]</i> 网络信息</h2>
            <div class="network-stats">
                <div class="stat-item">
                    <div class="stat-value" id="downloadSpeed">0.00</div>
                    <div class="stat-label">下载速度 (MB/s)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="uploadSpeed">0.00</div>
                    <div class="stat-label">上传速度 (MB/s)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalRecv">0.00</div>
                    <div class="stat-label">总下载流量 (GB)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalSent">0.00</div>
                    <div class="stat-label">总上传流量 (GB)</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2><i>[DISK]</i> 磁盘信息</h2>
            <div class="disk-container">
                <div id="diskInfo">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2><i>[GPU]</i> GPU 信息</h2>
            <div class="gpu-container">
                <div id="gpuInfo">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        
        <div class="timestamp" id="updateTime">最后更新时间: -</div>
    </div>
    
    <script>
        let ws;
        let pollInterval;
        let reconnectInterval = 1000; // 重连间隔(ms)
        let updateIntervalSeconds = 2; // 默认更新间隔(秒)
        let mode = 'websocket'; // 默认模式
        
        // 更新模式切换监听
        document.querySelectorAll('input[name="mode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    const newMode = this.value;
                    if (newMode !== mode) {
                        mode = newMode;
                        stopCurrentMode();
                        startDataCollection();
                    }
                }
            });
        });
        
        function stopCurrentMode() {
            // 停止当前模式的数据收集
            if (ws) {
                try {
                    ws.close();
                } catch (e) {
                    console.error('关闭旧连接出错:', e);
                }
                ws = null;
            }
            
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }
        
        function startDataCollection() {
            if (mode === 'websocket') {
                connectWebSocket();
            } else {
                startPolling();
            }
        }
        
        function connectWebSocket() {
            if (ws) {
                try {
                    ws.close();
                } catch (e) {
                    console.error('关闭旧连接出错:', e);
                }
                ws = null;
            }
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
                
                let connectionTimeout = setTimeout(() => {
                    console.error('WebSocket连接超时，切换到HTTP轮询模式');
                    document.querySelector('input[value="polling"]').checked = true;
                    mode = 'polling';
                    stopCurrentMode();
                    startPolling();
                }, 5000); // 5秒连接超时
                
                ws.onopen = () => {
                    clearTimeout(connectionTimeout);
                    console.log('WebSocket连接已建立');
                    document.getElementById('updateTime').innerText = '已连接到服务器...';
                };
                
                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        updateUI(data);
                    } catch (e) {
                        console.error('解析数据失败:', e);
                    }
                };
                
                ws.onclose = (event) => {
                    clearTimeout(connectionTimeout);
                    console.log(`WebSocket连接已关闭，代码: ${event.code}, 原因: ${event.reason}`);
                    
                    // 只有在websocket模式下才尝试重连
                    if (mode === 'websocket') {
                        document.getElementById('updateTime').innerText = '连接断开，尝试重连中...';
                        
                        // 避免无限重连导致浏览器报错
                        setTimeout(() => {
                            if (mode === 'websocket') {
                                connectWebSocket();
                            }
                        }, reconnectInterval);
                    }
                };
                
                ws.onerror = (error) => {
                    clearTimeout(connectionTimeout);
                    console.error('WebSocket错误，切换到HTTP轮询模式');
                    document.querySelector('input[value="polling"]').checked = true;
                    mode = 'polling';
                    stopCurrentMode();
                    startPolling();
                };
            } catch (e) {
                console.error('创建WebSocket失败:', e);
                document.querySelector('input[value="polling"]').checked = true;
                mode = 'polling';
                startPolling();
            }
        }
        
        function startPolling() {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
            
            document.getElementById('updateTime').innerText = '使用HTTP轮询模式...';
            fetchData(); // 立即获取一次
            
            pollInterval = setInterval(() => {
                fetchData();
            }, updateIntervalSeconds * 1000);
        }
        
        function fetchData() {
            fetch('/api/system_info')
                .then(response => response.json())
                .then(data => {
                    updateUI(data);
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                    document.getElementById('updateTime').innerText = '获取数据失败，请刷新页面';
                });
        }
        
        function updateUI(data) {
            // 更新时间戳
            document.getElementById('updateTime').innerText = '最后更新时间: ' + data.timestamp;
            
            // 更新CPU信息
            const cpuInfo = data.cpu;
            document.getElementById('cpuTotalValue').innerText = `${cpuInfo.total}%`;
            document.getElementById('cpuTotalText').innerText = `CPU总体使用率: ${cpuInfo.total}%`;
            document.getElementById('cpuTotalBar').style.width = `${cpuInfo.total}%`;
            
            let coresHTML = '';
            cpuInfo.cores.forEach(core => {
                coresHTML += `
                    <div class="cpu-core">
                        <div class="core-label">
                            <span>核心 ${core.id}</span>
                            <span>${core.percent}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: ${core.percent}%"></div>
                        </div>
                    </div>
                `;
            });
            document.getElementById('cpuCores').innerHTML = coresHTML;
            
            // 更新内存信息
            const memInfo = data.memory;
            document.getElementById('memoryValue').innerText = `${memInfo.percent}%`;
            document.getElementById('memText').innerHTML = `
                内存总量: ${memInfo.total} GB<br>
                已使用: ${memInfo.used} GB (${memInfo.percent}%)<br>
                可用: ${memInfo.available} GB
            `;
            document.getElementById('memBar').style.width = `${memInfo.percent}%`;
            
            // 更新网络信息
            const netInfo = data.network;
            document.getElementById('downloadSpeed').innerText = netInfo.download.toFixed(2);
            document.getElementById('uploadSpeed').innerText = netInfo.upload.toFixed(2);
            document.getElementById('totalRecv').innerText = netInfo.total_recv.toFixed(2);
            document.getElementById('totalSent').innerText = netInfo.total_sent.toFixed(2);
            
            // 更新磁盘信息
            const diskInfo = data.disk;
            let diskHTML = '';
            
            if (diskInfo.length === 0) {
                diskHTML = '<p>无法获取磁盘信息</p>';
            } else {
                diskInfo.forEach(disk => {
                    diskHTML += `
                        <div class="disk-item">
                            <h3>${disk.device} (${disk.fstype})</h3>
                            <p>挂载点: ${disk.mountpoint}</p>
                            <div class="progress-container">
                                <div class="progress-label">
                                    <span>使用率</span>
                                    <span>${disk.percent}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: ${disk.percent}%"></div>
                                </div>
                            </div>
                            <p>总容量: ${disk.total} GB | 已用: ${disk.used} GB | 可用: ${disk.free} GB</p>
                        </div>
                    `;
                });
            }
            document.getElementById('diskInfo').innerHTML = diskHTML;
            
            // 更新GPU信息
            const gpuInfo = data.gpu;
            let gpuHTML = '';
            
            if (!gpuInfo.available) {
                gpuHTML = `<p class="error-message">${gpuInfo.message || 'GPU信息不可用'}</p>`;
            } else if (gpuInfo.data.length === 0) {
                gpuHTML = '<p>未检测到GPU设备</p>';
            } else {
                gpuInfo.data.forEach(gpu => {
                    gpuHTML += `
                        <div class="gpu-item">
                            <h3>GPU ${gpu.id}: ${gpu.name}</h3>
                            <div class="progress-container">
                                <div class="progress-label">
                                    <span>GPU负载</span>
                                    <span>${gpu.load}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: ${gpu.load}%"></div>
                                </div>
                            </div>
                            <div class="progress-container">
                                <div class="progress-label">
                                    <span>显存使用</span>
                                    <span>${gpu.memoryPercent}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: ${gpu.memoryPercent}%"></div>
                                </div>
                            </div>
                            <p>显存: ${gpu.memoryUsed}/${gpu.memoryTotal} MB | 温度: ${gpu.temperature}°C</p>
                        </div>
                    `;
                });
            }
            document.getElementById('gpuInfo').innerHTML = gpuHTML;
        }
        
        document.getElementById('setIntervalBtn').addEventListener('click', () => {
            const newInterval = parseInt(document.getElementById('intervalInput').value);
            if (newInterval && newInterval > 0 && newInterval <= 60) {
                updateIntervalSeconds = newInterval;
                
                fetch('/api/set_interval', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({interval: newInterval})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert(`刷新间隔已设置为 ${data.interval} 秒`);
                        
                        // 如果是轮询模式，重启轮询
                        if (mode === 'polling' && pollInterval) {
                            clearInterval(pollInterval);
                            pollInterval = setInterval(fetchData, updateIntervalSeconds * 1000);
                        }
                    } else {
                        alert('设置失败');
                    }
                });
            } else {
                alert('请输入1-60之间的数值');
            }
        });
        
        // 初始化数据收集
        startDataCollection();
    </script>
</body>
</html>
""")

# 读取模板文件
def load_html_template():
    template_path = os.path.join(os.path.dirname(__file__), 'monitor_template.html')
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        raise FileNotFoundError(f"模板文件不存在: {template_path}")

HTML_TEMPLATE = ""

try:
    HTML_TEMPLATE = load_html_template()
except Exception as e:
    HTML_TEMPLATE = """<!DOCTYPE html><html><head><title>系统监控</title></head><body><h1>模板加载失败</h1><p>错误信息: """ + str(e) + """</p></body></html>"""

class SystemMonitor:
    def __init__(self, interval=1):
        """初始化系统监控器
        
        Args:
            interval: 监控间隔时间（秒）
        """
        self.interval = interval
        self.prev_net_io = psutil.net_io_counters()
        self.prev_time = time.time()
    
    def get_gpu_info(self):
        """获取GPU信息"""
        if not GPU_AVAILABLE:
            return {"available": False, "message": "GPU监控不可用", "data": []}
        
        result = []
        try:
            gpus = GPUtil.getGPUs()
            for i, gpu in enumerate(gpus):
                result.append({
                    "id": i,
                    "name": gpu.name,
                    "load": round(gpu.load*100, 1),
                    "memoryUsed": gpu.memoryUsed,
                    "memoryTotal": gpu.memoryTotal,
                    "memoryPercent": round(gpu.memoryUtil*100, 1),
                    "temperature": gpu.temperature
                })
            return {"available": True, "data": result}
        except Exception as e:
            return {"available": False, "message": f"获取GPU信息失败: {e}", "data": []}
    
    def get_cpu_info(self):
        """获取CPU信息"""
        total = psutil.cpu_percent()
        per_cpu = psutil.cpu_percent(percpu=True)
        
        return {
            "total": total,
            "cores": [{"id": i, "percent": percent} for i, percent in enumerate(per_cpu)]
        }
    
    def get_memory_info(self):
        """获取内存信息"""
        mem = psutil.virtual_memory()
        return {
            "total": round(mem.total / (1024**3), 2),
            "used": round(mem.used / (1024**3), 2),
            "percent": mem.percent,
            "available": round(mem.available / (1024**3), 2)
        }
    
    def get_disk_info(self):
        """获取磁盘信息"""
        partitions = psutil.disk_partitions()
        disk_info = []
        
        for partition in partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_info.append({
                    "device": partition.device,
                    "mountpoint": partition.mountpoint,
                    "fstype": partition.fstype,
                    "total": round(usage.total / (1024**3), 2),
                    "used": round(usage.used / (1024**3), 2),
                    "free": round(usage.free / (1024**3), 2),
                    "percent": usage.percent
                })
            except:
                # 某些挂载点可能无法访问
                pass
                
        return disk_info
    
    def get_network_info(self):
        """获取网络带宽信息"""
        current_net_io = psutil.net_io_counters()
        current_time = time.time()
        
        # 计算时间差
        time_diff = current_time - self.prev_time
        
        # 计算下载和上传速度 (bytes/s)
        download_speed = (current_net_io.bytes_recv - self.prev_net_io.bytes_recv) / time_diff
        upload_speed = (current_net_io.bytes_sent - self.prev_net_io.bytes_sent) / time_diff
        
        # 更新之前的数据
        self.prev_net_io = current_net_io
        self.prev_time = current_time
        
        return {
            "download": round(download_speed / (1024**2), 2),
            "upload": round(upload_speed / (1024**2), 2),
            "total_sent": round(current_net_io.bytes_sent / (1024**3), 2),
            "total_recv": round(current_net_io.bytes_recv / (1024**3), 2)
        }
    
    def get_all_info(self):
        """获取所有系统信息"""
        return {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "gpu": self.get_gpu_info(),
            "cpu": self.get_cpu_info(),
            "memory": self.get_memory_info(),
            "network": self.get_network_info(),
            "disk": self.get_disk_info()
        }
    
    def monitor(self):
        """监控系统各项指标 (命令行版本)"""
        try:
            while True:
                os.system('cls' if os.name == 'nt' else 'clear')
                print("\n" + "="*50)
                print("系统监控 - " + time.strftime("%Y-%m-%d %H:%M:%S"))
                print("="*50)
                
                # 显示GPU信息
                print("\n[GPU信息]")
                gpu_info = self.get_gpu_info()
                if not gpu_info["available"]:
                    print(gpu_info["message"])
                else:
                    for gpu in gpu_info["data"]:
                        print(f"GPU {gpu['id']}: {gpu['name']}")
                        print(f"  负载: {gpu['load']}%")
                        print(f"  内存使用: {gpu['memoryUsed']}/{gpu['memoryTotal']} MB ({gpu['memoryPercent']}%)")
                        print(f"  温度: {gpu['temperature']}°C")
                
                # 显示CPU信息
                print("\n[CPU信息]")
                cpu_info = self.get_cpu_info()
                print(f"CPU总体使用率: {cpu_info['total']}%")
                for core in cpu_info["cores"]:
                    print(f"  核心 {core['id']}: {core['percent']}%")
                
                # 显示内存信息
                print("\n[内存信息]")
                mem_info = self.get_memory_info()
                print(f"内存总量: {mem_info['total']} GB")
                print(f"已使用: {mem_info['used']} GB ({mem_info['percent']}%)")
                print(f"可用: {mem_info['available']} GB")
                
                # 显示磁盘信息
                print("\n[磁盘信息]")
                disk_info = self.get_disk_info()
                for disk in disk_info:
                    print(f"设备: {disk['device']} ({disk['fstype']})")
                    print(f"  挂载点: {disk['mountpoint']}")
                    print(f"  总容量: {disk['total']} GB")
                    print(f"  已用: {disk['used']} GB ({disk['percent']}%)")
                    print(f"  可用: {disk['free']} GB")
                
                # 显示网络信息
                print("\n[网络信息]")
                net_info = self.get_network_info()
                print(f"网络下载速度: {net_info['download']} MB/s")
                print(f"网络上传速度: {net_info['upload']} MB/s")
                print(f"总下载流量: {net_info['total_recv']} GB")
                print(f"总上传流量: {net_info['total_sent']} GB")
                
                print("\n" + "="*50)
                print(f"按Ctrl+C停止监控 (每{self.interval}秒更新一次)")
                
                time.sleep(self.interval)
        except KeyboardInterrupt:
            print("\n监控已停止")

# FastAPI应用
app = FastAPI(title="系统监控")

# 添加CORS中间件，解决跨域问题
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

monitor = SystemMonitor(interval=2)

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.update_interval = 2  # 默认更新间隔
        self.running = False
        self.broadcast_task = None

    async def connect(self, websocket: WebSocket):
        try:
            await websocket.accept()
            self.active_connections.append(websocket)
            if not self.running:
                self.running = True
                # 创建广播任务
                if self.broadcast_task is None or self.broadcast_task.done():
                    self.broadcast_task = asyncio.create_task(self.broadcast_info())
        except Exception as e:
            print(f"接受WebSocket连接时出错: {e}")
            try:
                await websocket.close(code=1011, reason=f"服务器错误: {str(e)}")
            except:
                pass

    def disconnect(self, websocket: WebSocket):
        try:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
        except:
            pass
            
        if not self.active_connections:
            self.running = False
            # 取消广播任务
            if self.broadcast_task and not self.broadcast_task.done():
                self.broadcast_task.cancel()

    async def broadcast_info(self):
        """广播系统信息到所有连接的客户端"""
        retry_count = 0
        max_retries = 3
        
        while self.active_connections and self.running:
            try:
                # 获取系统信息
                info = monitor.get_all_info()
                
                # 创建要删除的连接列表
                to_remove = []
                
                # 发送到每个连接
                for connection in self.active_connections:
                    try:
                        await connection.send_json(info)
                    except Exception as e:
                        print(f"向客户端发送数据时出错: {e}")
                        to_remove.append(connection)
                
                # 移除失败的连接
                for connection in to_remove:
                    try:
                        self.active_connections.remove(connection)
                    except:
                        pass
                
                # 重置重试计数
                retry_count = 0
                
                # 等待下一次更新
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                # 任务被取消时退出循环
                break
            except Exception as e:
                retry_count += 1
                print(f"广播信息时发生错误 (尝试 {retry_count}/{max_retries}): {e}")
                
                if retry_count >= max_retries:
                    print("达到最大重试次数，停止广播")
                    self.running = False
                    break
                    
                # 出错时等待1秒再试
                await asyncio.sleep(1)
        
        self.running = False
    
    def set_update_interval(self, seconds: int):
        """设置更新间隔"""
        self.update_interval = max(1, min(seconds, 60))  # 限制在1-60秒之间
        return {"status": "success", "interval": self.update_interval}

# 创建连接管理器
manager = ConnectionManager()

# HTML页面
@app.get("/", response_class=HTMLResponse)
async def get_html():
    return HTML_TEMPLATE

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    try:
        await manager.connect(websocket)
        try:
            while True:
                # 等待客户端消息
                data = await websocket.receive_text()
                # 如果需要处理客户端消息，可以在这里添加代码
        except Exception as e:
            print(f"WebSocket连接中断: {e}")
        finally:
            manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket连接异常: {e}")
        try:
            await websocket.close(code=1011, reason=f"服务器错误: {str(e)}")
        except:
            pass

# REST API端点，用于HTTP轮询模式
@app.get("/api/system_info")
async def get_system_info():
    try:
        return monitor.get_all_info()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# 设置刷新间隔
@app.post("/api/set_interval")
async def set_interval(request: Request):
    try:
        data = await request.json()
        interval = data.get("interval", 2)
        return manager.set_update_interval(interval)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"设置间隔失败: {str(e)}")

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='系统监控')
    parser.add_argument('--web', action='store_true', help='启动Web界面')
    parser.add_argument('--port', type=int, default=8000, help='Web服务端口')
    parser.add_argument('--silent', action='store_true', help='静默模式，减少日志输出')
    args = parser.parse_args()
    
    if args.web:
        print(f"启动Web监控服务，访问地址:")
        print(f"  - 本地访问: http://localhost:{args.port}")
        print(f"  - 局域网访问: http://<本机IP>:{args.port}")
        print("按Ctrl+C停止服务")
        log_level = "error" if args.silent else "info"
        uvicorn.run(app, host="0.0.0.0", port=args.port, log_level=log_level)
    else:
        print("启动系统监控...")
        monitor = SystemMonitor(interval=2)  # 每2秒更新一次
        monitor.monitor() 
