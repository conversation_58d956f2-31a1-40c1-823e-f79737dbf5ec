# 自定义监控项故障排查指南

## 概述

本指南提供了自定义监控项模块常见问题的诊断和解决方法，帮助您快速识别和解决监控系统中的问题。

## 故障分类

### 1. 连接问题
- SSH连接失败
- 网络超时
- 认证失败

### 2. 命令执行问题
- 命令不存在
- 权限不足
- 语法错误

### 3. 数据处理问题
- 数据解析失败
- 格式不匹配
- 编码问题

### 4. 系统性能问题
- 执行超时
- 资源不足
- 并发限制

## 诊断工具

### 1. 状态检查命令

#### 检查监控系统整体状态

```bash
# 获取系统状态概览
curl "http://your-server/monitor/status" | jq '.'

# 检查调度器运行状态
curl "http://your-server/monitor/stats" | jq '.scheduler_stats.running'

# 查看错误统计
curl "http://your-server/monitor/stats" | jq '.error_stats'
```

#### 检查特定服务器状态

```bash
# 查看服务器监控状态
curl "http://your-server/monitor/status/server/*************" | jq '.'

# 查看有问题的监控项
curl "http://your-server/monitor/status/server/*************" | jq '.problematic_items'
```

### 2. 日志查看

#### 应用日志

```bash
# 查看最新日志
tail -f /var/log/monitoring/custom_monitor.log

# 查看错误日志
grep "ERROR" /var/log/monitoring/custom_monitor.log | tail -20

# 查看特定IP的日志
grep "*************" /var/log/monitoring/custom_monitor.log

# 查看特定时间段的日志
grep "2024-01-01 12:" /var/log/monitoring/custom_monitor.log
```

#### 系统日志

```bash
# 查看系统日志
journalctl -u monitoring-service -f

# 查看SSH相关日志
grep "ssh" /var/log/auth.log | tail -20
```

### 3. 网络诊断

#### 连接测试

```bash
# 测试SSH连接
ssh -o ConnectTimeout=10 user@target-server "echo 'Connection OK'"

# 测试网络连通性
ping -c 4 target-server

# 检查端口可达性
telnet target-server 22

# 使用nmap检查端口
nmap -p 22 target-server
```

## 常见问题及解决方案

### 1. SSH连接问题

#### 问题1：SSH连接超时

**症状：**
```
ERROR: SSH连接失败: 无法连接到 *************
```

**诊断步骤：**

1. **检查网络连通性**
```bash
ping -c 4 *************
```

2. **检查SSH服务状态**
```bash
ssh -o ConnectTimeout=10 user@************* "echo 'test'"
```

3. **检查防火墙设置**
```bash
# 在目标服务器上检查
sudo ufw status
sudo iptables -L | grep 22
```

**解决方案：**

- **网络问题**：检查网络配置，确保路由正确
- **SSH服务未运行**：在目标服务器启动SSH服务
  ```bash
  sudo systemctl start ssh
  sudo systemctl enable ssh
  ```
- **防火墙阻止**：开放SSH端口
  ```bash
  sudo ufw allow 22/tcp
  ```

#### 问题2：SSH认证失败

**症状：**
```
ERROR: SSH连接失败: authentication failed
```

**诊断步骤：**

1. **测试用户名密码**
```bash
ssh user@*************
```

2. **检查SSH密钥**
```bash
ssh -i /path/to/key user@*************
```

3. **查看SSH日志**
```bash
# 在目标服务器上
sudo tail -f /var/log/auth.log
```

**解决方案：**

- **密码错误**：确认用户名和密码正确
- **密钥问题**：检查密钥权限和路径
  ```bash
  chmod 600 ~/.ssh/id_rsa
  chmod 644 ~/.ssh/id_rsa.pub
  ```
- **用户不存在**：创建用户或使用正确的用户名

#### 问题3：SSH密钥权限问题

**症状：**
```
ERROR: SSH连接失败: Permission denied (publickey)
```

**解决方案：**

1. **检查密钥权限**
```bash
ls -la ~/.ssh/
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
```

2. **检查authorized_keys**
```bash
# 在目标服务器上
chmod 600 ~/.ssh/authorized_keys
cat ~/.ssh/authorized_keys  # 确认公钥存在
```

3. **重新生成密钥对**
```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/monitoring_key
ssh-copy-id -i ~/.ssh/monitoring_key.pub user@target-server
```

### 2. 命令执行问题

#### 问题1：命令不存在

**症状：**
```
ERROR: 命令执行失败: command not found
```

**诊断步骤：**

1. **在目标服务器手动测试**
```bash
ssh user@target-server "which command_name"
ssh user@target-server "echo \$PATH"
```

2. **检查命令路径**
```bash
ssh user@target-server "find /usr -name 'command_name' 2>/dev/null"
```

**解决方案：**

- **使用完整路径**：将命令改为完整路径
  ```json
  {
    "command": "/usr/bin/free -m"
  }
  ```
- **安装缺失的软件**：在目标服务器安装所需软件
- **修改PATH环境变量**：确保命令在PATH中

#### 问题2：权限不足

**症状：**
```
ERROR: 命令执行失败: Permission denied
```

**解决方案：**

1. **检查文件权限**
```bash
ssh user@target-server "ls -la /path/to/file"
```

2. **配置sudo权限**
```bash
# 编辑sudoers文件
sudo visudo

# 添加用户权限
monitor ALL=(ALL) NOPASSWD: /specific/command
```

3. **使用sudo执行**
```json
{
  "command": "sudo /usr/bin/systemctl status nginx"
}
```

#### 问题3：命令语法错误

**症状：**
```
ERROR: 命令执行失败: syntax error
```

**解决方案：**

1. **在目标服务器测试命令**
```bash
ssh user@target-server "your-command"
```

2. **检查特殊字符转义**
```json
{
  "command": "echo 'hello world' | grep hello"
}
```

3. **使用脚本文件**
```bash
# 创建脚本文件
cat > /usr/local/bin/monitor_script.sh << 'EOF'
#!/bin/bash
complex command here
EOF
chmod +x /usr/local/bin/monitor_script.sh

# 在监控项中调用
{
  "command": "/usr/local/bin/monitor_script.sh"
}
```

### 3. 数据处理问题

#### 问题1：JSON解析失败

**症状：**
```
ERROR: 数据解析失败: JSON解析失败: Expecting value
```

**诊断步骤：**

1. **检查命令输出**
```bash
ssh user@target-server "your-command"
```

2. **验证JSON格式**
```bash
ssh user@target-server "your-command" | jq '.'
```

**解决方案：**

1. **修复JSON格式**
```bash
# 使用jq格式化输出
"command": "your-command | jq -c '.'"
```

2. **处理非JSON输出**
```bash
# 将输出转换为JSON
"command": "echo '{\"value\":\"'$(your-command)'\"}'"
```

3. **更改数据类型**
```json
{
  "data_type": "string"  // 改为字符串类型
}
```

#### 问题2：数据编码问题

**症状：**
```
ERROR: 数据解析失败: 'utf-8' codec can't decode
```

**解决方案：**

1. **指定编码**
```bash
"command": "your-command | iconv -f gbk -t utf-8"
```

2. **过滤非ASCII字符**
```bash
"command": "your-command | tr -cd '[:print:]'"
```

### 4. 性能问题

#### 问题1：执行超时

**症状：**
```
ERROR: 命令执行超时 (30秒)
```

**解决方案：**

1. **增加超时时间**
```json
{
  "timeout": 60  // 增加到60秒
}
```

2. **优化命令**
```bash
# 原命令
"find / -name '*.log' 2>/dev/null"

# 优化后
"find /var/log -name '*.log' -maxdepth 2"
```

3. **使用后台执行**
```bash
"command": "timeout 30 your-long-running-command"
```

#### 问题2：并发限制

**症状：**
```
ERROR: 已达到最大连接数限制
```

**解决方案：**

1. **调整连接池配置**
```python
# 增加连接池大小
CONNECTION_POOL_CONFIG = {
    "max_connections_per_server": 5,
    "max_total_connections": 100
}
```

2. **减少并发执行**
```python
# 减少批量大小
BATCH_SIZE = 5
MAX_CONCURRENT = 10
```

#### 问题3：内存不足

**症状：**
```
ERROR: 内存不足，无法执行命令
```

**解决方案：**

1. **优化命令输出**
```bash
# 限制输出大小
"command": "your-command | head -1000"
```

2. **分批处理**
```bash
# 分批获取数据
"command": "your-command | split -l 100"
```

## 监控和预防

### 1. 健康检查

#### 定期健康检查脚本

```bash
#!/bin/bash
# 监控系统健康检查

echo "=== 监控系统健康检查 ==="

# 检查服务状态
echo "1. 检查服务状态..."
systemctl is-active monitoring-service

# 检查连接池状态
echo "2. 检查连接池状态..."
curl -s "http://localhost/monitor/stats" | jq '.scheduler_stats.connection_pool_stats'

# 检查错误率
echo "3. 检查错误率..."
curl -s "http://localhost/monitor/stats" | jq '.scheduler_stats | {success_rate: .successful_executions / .total_executions * 100}'

# 检查磁盘空间
echo "4. 检查磁盘空间..."
df -h /var/log

# 检查内存使用
echo "5. 检查内存使用..."
free -m
```

### 2. 告警配置

#### 配置监控告警

```python
# 告警阈值配置
ALERT_THRESHOLDS = {
    "error_rate": 10,           # 错误率超过10%
    "connection_failures": 5,    # 连接失败超过5次/小时
    "execution_timeout": 3,      # 执行超时超过3次/小时
    "disk_usage": 80,           # 磁盘使用率超过80%
    "memory_usage": 90          # 内存使用率超过90%
}

def check_alerts():
    """检查告警条件"""
    stats = get_monitor_stats()
    
    # 检查错误率
    error_rate = (stats['failed_executions'] / stats['total_executions']) * 100
    if error_rate > ALERT_THRESHOLDS['error_rate']:
        send_alert(f"监控错误率过高: {error_rate:.1f}%")
    
    # 检查连接失败
    connection_failures = get_connection_failures_last_hour()
    if connection_failures > ALERT_THRESHOLDS['connection_failures']:
        send_alert(f"连接失败次数过多: {connection_failures}")
```

### 3. 性能优化

#### 性能监控脚本

```python
#!/usr/bin/env python3
# 性能监控脚本

import time
import psutil
import requests

def monitor_performance():
    """监控系统性能"""
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用率
    memory = psutil.virtual_memory()
    memory_percent = memory.percent
    
    # 磁盘使用率
    disk = psutil.disk_usage('/var/log')
    disk_percent = (disk.used / disk.total) * 100
    
    # 网络连接数
    connections = len(psutil.net_connections())
    
    # 监控系统统计
    try:
        response = requests.get('http://localhost/monitor/stats', timeout=5)
        monitor_stats = response.json()
        success_rate = monitor_stats.get('scheduler_stats', {}).get('success_rate', 0)
    except:
        success_rate = 0
    
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"CPU: {cpu_percent:.1f}%")
    print(f"内存: {memory_percent:.1f}%")
    print(f"磁盘: {disk_percent:.1f}%")
    print(f"连接数: {connections}")
    print(f"监控成功率: {success_rate:.1f}%")
    print("-" * 40)

if __name__ == "__main__":
    while True:
        monitor_performance()
        time.sleep(60)  # 每分钟检查一次
```

## 应急处理

### 1. 紧急停止

```bash
#!/bin/bash
# 紧急停止监控系统

echo "紧急停止监控系统..."

# 停止调度器
curl -X POST http://localhost/admin/stop-scheduler

# 停止所有监控任务
pkill -f "monitor_scheduler"

# 关闭连接池
curl -X POST http://localhost/admin/close-connections

echo "监控系统已紧急停止"
```

### 2. 快速恢复

```bash
#!/bin/bash
# 快速恢复监控系统

echo "开始恢复监控系统..."

# 检查系统状态
systemctl status monitoring-service

# 重启服务
systemctl restart monitoring-service

# 等待服务启动
sleep 10

# 验证服务状态
curl -f http://localhost/monitor/status || {
    echo "服务启动失败，请检查日志"
    exit 1
}

echo "监控系统恢复完成"
```

## 总结

通过本故障排查指南，您可以：

1. **快速诊断问题**：使用提供的诊断工具和命令
2. **解决常见问题**：参考详细的解决方案
3. **预防问题发生**：实施监控和告警机制
4. **应急处理**：在紧急情况下快速响应

记住，故障排查是一个系统性的过程：

1. **收集信息**：查看日志、状态、错误信息
2. **分析问题**：确定问题类型和根本原因
3. **制定方案**：选择合适的解决方法
4. **实施解决**：执行解决方案
5. **验证结果**：确认问题已解决
6. **预防复发**：改进系统和流程

如果遇到本指南未涵盖的问题，请记录详细的错误信息和环境情况，以便进一步分析和解决。
