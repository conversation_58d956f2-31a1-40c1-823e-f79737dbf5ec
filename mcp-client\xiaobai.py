from openai import OpenAI
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

print(os.getenv("OPENAI_API_KEY"))
print(os.getenv("BASE_URL"))
print(os.getenv("MODEL"))

# 初始化客户端
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url="http://10.128.33.1:8003/v1"
)

# 调用模型
response = client.chat.completions.create(
    model=os.getenv("MODEL"),
    messages=[{"role": "user", "content": "你好，请介绍一下自己"}]
)

# 打印结果
print(response.choices[0].message.content)


