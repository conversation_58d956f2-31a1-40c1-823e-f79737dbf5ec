import { getIpList, getIpUserList, addIp, deleteIp } from '@/api/ip/index'

const state = {
  ipList: [],
  currentIp: '',
  ipUserList: [], // 用户详细列表
  pagination: {
    total: 0,
    page: 1,
    pageSize: 10,
    pages: 0
  },
  loading: false,
  filterOptions: {
    isDeleted: false,
    isConnectable: true
  }
}

const mutations = {
  SET_IP_LIST: (state, ipList) => {
    state.ipList = ipList
  },
  SET_CURRENT_IP: (state, ip) => {
    state.currentIp = ip
  },
  SET_IP_USER_LIST: (state, ipUserList) => {
    state.ipUserList = ipUserList
  },
  SET_PAGINATION: (state, pagination) => {
    state.pagination = pagination
  },
  SET_LOADING: (state, loading) => {
    state.loading = loading
  },
  SET_FILTER_OPTIONS: (state, options) => {
    state.filterOptions = { ...state.filterOptions, ...options }
  }
}

const actions = {
  // 获取IP列表
  fetchIpList ({ commit }) {
    return new Promise((resolve, reject) => {
      getIpList()
        .then(response => {
          const ipList = response || []
          commit('SET_IP_LIST', ipList)

          // 如果有IP数据，则设置第一个为当前选择的IP
          if (ipList.length > 0) {
            commit('SET_CURRENT_IP', ipList[0])
          }

          resolve(ipList)
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 设置当前选择的IP
  setCurrentIp ({ commit }, ip) {
    commit('SET_CURRENT_IP', ip)
  },

  // 获取IP用户详细列表（分页）
  fetchIpUserList ({ commit, state }) {
    commit('SET_LOADING', true)

    const params = {
      page: state.pagination.page,
      page_size: state.pagination.pageSize,
      is_deleted: state.filterOptions.isDeleted
    }

    // 只有当filterConnectable为true时，才添加is_connectable参数
    if (state.filterOptions.isConnectable) {
      params.is_connectable = true
    }

    return new Promise((resolve, reject) => {
      getIpUserList(params)
        .then(response => {
          const { data, total, page, page_size: pageSize, pages } = response || { data: [], total: 0, page: 1, page_size: 10, pages: 0 }

          commit('SET_IP_USER_LIST', data || [])
          commit('SET_PAGINATION', {
            total,
            page,
            pageSize,
            pages
          })

          commit('SET_LOADING', false)
          resolve(data)
        })
        .catch(error => {
          commit('SET_LOADING', false)
          reject(error)
        })
    })
  },

  // 设置分页信息
  setPage ({ commit, dispatch }, page) {
    commit('SET_PAGINATION', { ...state.pagination, page })
    return dispatch('fetchIpUserList')
  },

  // 设置过滤选项
  setFilterOptions ({ commit, dispatch }, options) {
    commit('SET_FILTER_OPTIONS', options)
    // 重置到第一页
    commit('SET_PAGINATION', { ...state.pagination, page: 1 })
    return dispatch('fetchIpUserList')
  },

  // 添加IP
  addIp ({ dispatch }, ipData) {
    // 转换参数名称，确保与API一致
    const apiData = { ...ipData }
    if (apiData.username) {
      apiData.user = apiData.username
      delete apiData.username
    }

    return new Promise((resolve, reject) => {
      addIp(apiData)
        .then(response => {
          // 添加成功后刷新列表
          dispatch('fetchIpUserList')
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 删除IP
  deleteIp ({ dispatch }, id) {
    return new Promise((resolve, reject) => {
      deleteIp(id)
        .then(response => {
          // 删除成功后刷新列表
          dispatch('fetchIpUserList')
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}

const getters = {
  ipList: state => state.ipList,
  currentIp: state => state.currentIp,
  ipUserList: state => state.ipUserList,
  pagination: state => state.pagination,
  loading: state => state.loading,
  filterOptions: state => state.filterOptions
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
