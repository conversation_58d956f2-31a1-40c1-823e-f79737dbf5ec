for i in 50
do
    sudo scp -r /opt/ddn/ kgzs@10.102.11.$i:/tmp/  # 先复制到临时目录
done 


 
sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@************




for ip in 9 10 {33..56}
do
    echo -e "\033[31mIP: 10.102.11.$ip\033[0m"  
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'df -h' | \
    awk '
    BEGIN {printf "%-75s %8s %8s %8s %4s %s\n", "Filesystem", "Size", "Used", "Avail", "Use%", "Mounted on"}
    /10\.11\.116\.1@o2ib/ {printf "%-75s %8s %8s %8s %4s %s\n", $1, $2, $3, $4, $5, $6}'
    echo "----------------------------------------"
done



11.116.1@o2ib:***********@o2ib:***********@o2ib:***********@o2ib:/hjfs /lustre lustre defaults,_defaults,_netdev,nofail,x-systemd.mount,xsystemd.requires=lnet.service 0 0


sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@************


for ip in 9 10 {33..56}
do
    echo -e "\n\033[32m==================== IP: 10.102.11.$ip ====================\033[0m\n"
    sudo sshpass -p 'Kgzs@2024' ssh -o StrictHostKeyChecking=no kgzs@10.102.11.$ip 'echo "Kgzs@2024" | sudo -S cat /etc/fstab'
    echo -e "\n\033[32m========================================================\033[0m\n\n"
done