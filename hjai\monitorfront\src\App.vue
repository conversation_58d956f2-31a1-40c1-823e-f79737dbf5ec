<template>
  <div id="app">
    <div class="global-theme-switch">
      <theme-switcher />
    </div>
    <router-view />
  </div>
</template>

<script>
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'

export default {
  name: 'App',
  components: {
    ThemeSwitcher
  }
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
}

.global-theme-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}
</style>
