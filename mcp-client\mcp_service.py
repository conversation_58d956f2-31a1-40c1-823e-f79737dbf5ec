import json
import httpx
from typing import Any
from mcp.server.fastmcp import FastMCP
import os
# 初始化 MCP 服务器
mcp = FastMCP("getdir")

def getdir() -> str:
    """获取桌面上的所有文件和目录"""
    # 获取桌面路径
    desktop = os.path.join(os.path.expanduser("~"), "Desktop")
    
    # 存储所有文件和目录
    items = []
    
    try:
        # 遍历桌面目录
        for item in os.listdir(desktop):
            item_path = os.path.join(desktop, item)
            # 判断是文件还是目录
            if os.path.isfile(item_path):
                items.append(f"文件: {item}")
            elif os.path.isdir(item_path):
                items.append(f"目录: {item}")
                
        # 将所有项目用换行符连接成字符串
        return "\n".join(items) if items else "桌面上没有文件或目录"
        
    except Exception as e:
        return f"获取桌面内容时出错: {str(e)}"


@mcp.tool()
async def get_dir() -> str:
    """获取桌面上的所有文件和目录"""
    return getdir()


if __name__ == "__main__":
    mcp.run(transport='stdio')