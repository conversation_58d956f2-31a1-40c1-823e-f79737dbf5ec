package main

import (
    "errors"
    "fmt"
    "strconv"
)

// 错误域
type ErrorDomain string

const (
    DomainAuth   ErrorDomain = "AUTH"   // 认证域
    DomainAPI    ErrorDomain = "API"    // API域
    DomainDB     ErrorDomain = "DB"     // 数据库域
    DomainConfig ErrorDomain = "CONFIG" // 配置域
)

// 领域特定错误
type DomainError struct {
    Domain  ErrorDomain // 错误所属域
    Code    int         // 错误码
    Message string      // 错误信息
    Details string      // 详细信息
    Err     error       // 原始错误
}

// 实现error接口
func (e *DomainError) Error() string {
    return fmt.Sprintf("[%s-%04d] %s", e.Domain, e.Code, e.Message)
}

// 获取错误详情
func (e *DomainError) ErrorDetails() string {
    if e.Details != "" {
        if e.Err != nil {
            return fmt.Sprintf("%s: %v", e.Details, e.Err)
        }
        return e.Details
    }
    if e.Err != nil {
        return e.Err.Error()
    }
    return ""
}

// 实现Unwrap方法
func (e *DomainError) Unwrap() error {
    return e.Err
}

// 错误码定义
var (
    // 认证错误
    ErrInvalidCredentials = &DomainError{
        Domain:  DomainAuth,
        Code:    1001,
        Message: "无效的凭证",
    }
    ErrTokenExpired = &DomainError{
        Domain:  DomainAuth,
        Code:    1002,
        Message: "令牌已过期",
    }
    
    // API错误
    ErrRateLimitExceeded = &DomainError{
        Domain:  DomainAPI,
        Code:    2001,
        Message: "超出请求限制",
    }
    
    // 数据库错误
    ErrDatabaseConnection = &DomainError{
        Domain:  DomainDB,
        Code:    3001,
        Message: "数据库连接失败",
    }
    ErrRecordNotFound = &DomainError{
        Domain:  DomainDB,
        Code:    3002,
        Message: "记录不存在",
    }
)

// 使用错误码创建具体错误
func NewDomainError(baseError *DomainError, details string, err error) *DomainError {
    return &DomainError{
        Domain:  baseError.Domain,
        Code:    baseError.Code,
        Message: baseError.Message,
        Details: details,
        Err:     err,
    }
}

// 错误处理函数
func handleDomainError(err error) {
    var domainErr *DomainError
    if !errors.As(err, &domainErr) {
        fmt.Println("未知错误:", err)
        return
    }
    
    // 提取错误信息
    errorCode := string(domainErr.Domain) + "-" + strconv.Itoa(domainErr.Code)
    
    fmt.Printf("错误类型: %s\n", domainErr.Domain)
    fmt.Printf("错误码: %s\n", errorCode)
    fmt.Printf("错误信息: %s\n", domainErr.Message)
    
    if details := domainErr.ErrorDetails(); details != "" {
        fmt.Printf("详细信息: %s\n", details)
    }
    
    // 根据错误域处理
    switch domainErr.Domain {
    case DomainAuth:
        fmt.Println("认证失败，请重新登录")
    case DomainAPI:
        fmt.Println("API调用受限，请稍后再试")
    case DomainDB:
        fmt.Println("数据操作失败，请检查数据是否存在")
    }
}

// 模拟用户登录
func login(username, password string) error {
    if username == "" || password == "" {
        return NewDomainError(
            ErrInvalidCredentials,
            fmt.Sprintf("用户名或密码为空 (用户名:'%s')", username),
            nil,
        )
    }
    
    if username == "expired" {
        return NewDomainError(
            ErrTokenExpired,
            "用户会话已过期",
            errors.New("会话已于2023-01-01 12:00:00过期"),
        )
    }
    
    if username != "admin" || password != "password" {
        return NewDomainError(
            ErrInvalidCredentials,
            fmt.Sprintf("用户名或密码不正确 (用户名:'%s')", username),
            nil,
        )
    }
    
    return nil
}

// 模拟数据库查询
func queryUser(id int) error {
    if id <= 0 {
        return NewDomainError(
            ErrRecordNotFound,
            fmt.Sprintf("用户ID %d 不存在", id),
            nil,
        )
    }
    
    if id > 1000 {
        dbErr := errors.New("连接超时: 192.168.1.100:3306")
        return NewDomainError(
            ErrDatabaseConnection,
            "查询用户数据失败",
            dbErr,
        )
    }
    
    return nil
}

func main() {
    // 测试登录错误
    fmt.Println("测试登录空用户名:")
    err := login("", "password")
    if err != nil {
        handleDomainError(err)
    }
    
    fmt.Println("\n测试登录过期会话:")
    err = login("expired", "password")
    if err != nil {
        handleDomainError(err)
    }
    
    // 测试数据库错误
    fmt.Println("\n测试查询不存在用户:")
    err = queryUser(0)
    if err != nil {
        handleDomainError(err)
    }
    
    fmt.Println("\n测试数据库连接错误:")
    err = queryUser(1001)
    if err != nil {
        handleDomainError(err)
    }
    
    // 测试成功场景
    fmt.Println("\n测试正常登录:")
    err = login("admin", "password")
    if err != nil {
        handleDomainError(err)
    } else {
        fmt.Println("登录成功")
    }
}

