import Sprite from '../base/sprite';
import { ASSETS } from '../config/gameConfig';

/**
 * 骰子类
 * 负责骰子的渲染和动画
 */
export default class Dice {
  // 骰子图片
  diceImages = [];
  // 当前骰子值
  value = 1;
  // 是否正在投掷
  isRolling = false;
  // 投掷动画帧计数
  rollFrames = 0;
  // 投掷动画总帧数
  totalRollFrames = 30;
  // 骰子位置
  x = 0;
  y = 0;
  // 骰子大小
  width = 60;
  height = 60;
  // 骰子精灵
  sprite = null;
  // 动画回调
  rollCallback = null;
  // 是否可点击
  isClickable = false;
  // 是否使用Canvas绘制
  useCanvas = false;

  /**
   * 构造函数
   * @param {number} x - 骰子X坐标
   * @param {number} y - 骰子Y坐标
   * @param {number} size - 骰子大小
   */
  constructor(x, y, size = 60) {
    this.x = x;
    this.y = y;
    this.width = size;
    this.height = size;
    
    // 检查是否有有效的图片路径
    const hasValidImages = ASSETS.dice.some(src => src && src.length > 0);
    
    if (!hasValidImages) {
      // 如果没有有效的图片路径，直接使用Canvas绘制
      this.useCanvas = true;
      console.log('使用Canvas绘制骰子');
    } else {
      // 尝试加载骰子图片
      try {
        this.loadDiceImages();
      } catch (e) {
        console.log('无法加载骰子图片，将使用Canvas绘制');
        this.useCanvas = true;
      }
      
      // 创建骰子精灵
      if (!this.useCanvas) {
        this.sprite = new Sprite(
          ASSETS.dice[0],
          this.width,
          this.height,
          this.x,
          this.y
        );
      }
    }
  }
  
  /**
   * 加载骰子图片
   */
  loadDiceImages() {
    // 检查是否有有效的图片路径
    const hasValidImages = ASSETS.dice.some(src => src && src.length > 0);
    
    if (!hasValidImages) {
      this.useCanvas = true;
      throw new Error('No valid dice images');
    }
    
    this.diceImages = ASSETS.dice.map(src => {
      if (!src) {
        return null; // 返回null而不是抛出错误
      }
      const img = wx.createImage();
      img.src = src;
      return img;
    }).filter(Boolean); // 过滤掉null值
    
    // 如果没有有效的图片，使用Canvas绘制
    if (this.diceImages.length === 0) {
      this.useCanvas = true;
    }
  }
  
  /**
   * 设置骰子位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
    if (this.sprite) {
      this.sprite.x = x;
      this.sprite.y = y;
    }
  }
  
  /**
   * 设置骰子大小
   * @param {number} size - 骰子大小
   */
  setSize(size) {
    this.width = size;
    this.height = size;
    if (this.sprite) {
      this.sprite.width = size;
      this.sprite.height = size;
    }
  }
  
  /**
   * 设置是否可点击
   * @param {boolean} clickable - 是否可点击
   */
  setClickable(clickable) {
    this.isClickable = clickable;
  }
  
  /**
   * 投掷骰子
   * @param {number} forcedValue - 强制骰子值 (用于网络对战)
   * @param {Function} callback - 投掷完成回调
   */
  roll(forcedValue = null, callback = null) {
    if (this.isRolling) return;
    
    this.isRolling = true;
    this.rollFrames = 0;
    this.rollCallback = callback;
    this.currentAnimationFrame = 0;
    
    // 如果有强制值，使用强制值
    if (forcedValue !== null && forcedValue >= 1 && forcedValue <= 6) {
      this.value = forcedValue;
    } else {
      // 否则随机生成1-6的值
      this.value = Math.floor(Math.random() * 6) + 1;
    }
    
    // 初始化动画参数
    this.rotationX = 0;
    this.rotationY = 0;
    this.rotationZ = 0;
    this.scale = 1;
    this.bounceHeight = 0;
    
    // 播放骰子音效
    try {
      this.playRollSound();
    } catch (e) {
      console.log('无法播放骰子音效', e);
    }
  }
  
  /**
   * 播放骰子音效
   */
  playRollSound() {
    try {
      if (!ASSETS.audio.diceRoll) return;
      
      const audio = wx.createInnerAudioContext();
      audio.src = ASSETS.audio.diceRoll;
      audio.play();
    } catch (e) {
      console.error('播放骰子音效失败', e);
    }
  }
  
  /**
   * 更新骰子状态
   */
  update() {
    if (this.isRolling) {
      this.rollFrames++;
      this.currentAnimationFrame++;
      
      // 在投掷动画中随机显示不同的骰子面
      if (this.rollFrames < this.totalRollFrames) {
        const randomValue = Math.floor(Math.random() * 6);
        
        if (!this.useCanvas && this.sprite && this.diceImages[randomValue]) {
          this.sprite.img = this.diceImages[randomValue];
        }
        
        // 计算动画进度
        const progress = this.rollFrames / this.totalRollFrames;
        
        // 更新旋转动画
        this.updateRotationAnimation(progress);
        
        // 更新弹跳动画
        this.updateBounceAnimation(progress);
        
        // 更新缩放动画
        this.updateScaleAnimation(progress);
        
        // 添加缩放和旋转效果
        const scale = 1 + Math.sin(progress * Math.PI) * 0.3;
        
        if (this.sprite) {
          this.sprite.width = this.width * scale;
          this.sprite.height = this.height * scale;
          
          // 居中显示
          this.sprite.x = this.x - (this.sprite.width - this.width) / 2;
          this.sprite.y = this.y - (this.sprite.height - this.height) / 2;
        }
      } else {
        // 投掷结束，显示最终结果
        this.completeRoll();
      }
    }
    
    // 更新精灵
    if (this.sprite) {
      this.sprite.update && this.sprite.update();
    }
  }
  
  /**
   * 更新旋转动画
   */
  updateRotationAnimation(progress) {
    // 快速旋转，然后逐渐减速
    const rotationSpeed = (1 - progress) * 20;
    this.rotationX = (this.rotationX || 0) + rotationSpeed;
    this.rotationY = (this.rotationY || 0) + rotationSpeed * 0.7;
    this.rotationZ = (this.rotationZ || 0) + rotationSpeed * 0.5;
  }
  
  /**
   * 更新弹跳动画
   */
  updateBounceAnimation(progress) {
    // 弹跳效果
    if (progress < 0.8) {
      this.bounceHeight = Math.sin(progress * Math.PI * 4) * 30 * (1 - progress);
    } else {
      this.bounceHeight = 0;
    }
  }
  
  /**
   * 更新缩放动画
   */
  updateScaleAnimation(progress) {
    if (progress < 0.1) {
      // 开始时稍微放大
      this.scale = 1 + progress * 2;
    } else if (progress > 0.9) {
      // 结束时放大显示结果
      this.scale = 1 + (progress - 0.9) * 5;
    } else {
      this.scale = 1;
    }
  }
  
  /**
   * 完成投掷
   */
  completeRoll() {
    this.isRolling = false;
    this.rotationX = 0;
    this.rotationY = 0;
    this.rotationZ = 0;
    this.scale = 1.2; // 结果显示时稍微放大
    this.bounceHeight = 0;
    
    if (!this.useCanvas && this.sprite && this.diceImages[this.value - 1]) {
      this.sprite.img = this.diceImages[this.value - 1];
      this.sprite.width = this.width;
      this.sprite.height = this.height;
      this.sprite.x = this.x;
      this.sprite.y = this.y;
    }
    
    // 延迟恢复正常大小
    setTimeout(() => {
      this.scale = 1;
    }, 500);
    
    // 调用回调函数
    if (this.rollCallback) {
      this.rollCallback(this.value);
    }
  }
  
  /**
   * 渲染骰子
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    ctx.save();
    
    // 应用变换
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2 - (this.bounceHeight || 0);
    
    ctx.translate(centerX, centerY);
    ctx.scale(this.scale || 1, this.scale || 1);
    
    // 绘制阴影
    if (this.isRolling || (this.bounceHeight && this.bounceHeight > 0)) {
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = 10;
      ctx.shadowOffsetX = 5;
      ctx.shadowOffsetY = 5 + (this.bounceHeight || 0) * 0.3;
    }
    
    if (this.useCanvas) {
      // 使用Canvas绘制骰子
      ctx.translate(-this.width / 2, -this.height / 2);
      this.drawDice(ctx);
    } else if (this.sprite) {
      // 渲染骰子精灵
      ctx.translate(-this.width / 2, -this.height / 2);
      this.sprite.render(ctx);
    }
    
    ctx.restore();
    
    // 绘制地面阴影
    if (this.bounceHeight && this.bounceHeight > 0) {
      this.renderGroundShadow(ctx);
    }
    
    // 如果可点击且不在投掷中，绘制提示效果
    if (this.isClickable && !this.isRolling) {
      // 绘制点击提示
      ctx.beginPath();
      ctx.arc(
        this.x + this.width / 2,
        this.y + this.height / 2,
        this.width / 2 + 5,
        0,
        2 * Math.PI
      );
      ctx.fillStyle = 'rgba(255, 255, 0, 0.2)';
      ctx.fill();
      
      // 绘制"点击投掷"文字
      ctx.fillStyle = '#000000';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.fillText(
        '点击投掷',
        this.x + this.width / 2,
        this.y - 5
      );
    }
  }
  
  /**
   * 绘制地面阴影
   */
  renderGroundShadow(ctx) {
    ctx.save();
    
    const shadowX = this.x + this.width / 2;
    const shadowY = this.y + this.height;
    const shadowSize = this.width * 0.8 * (1 - (this.bounceHeight || 0) / 100);
    
    ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
    ctx.beginPath();
    ctx.ellipse(shadowX, shadowY, shadowSize / 2, shadowSize / 4, 0, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.restore();
  }
  
  /**
   * 使用Canvas绘制骰子
   * @param {Object} ctx - Canvas上下文
   */
  drawDice(ctx) {
    // 保存上下文
    ctx.save();
    
    const size = Math.min(this.width, this.height);
    const halfSize = size / 2;
    
    // 绘制骰子主体
    ctx.fillStyle = '#FFFFFF';
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 2;
    
    // 绘制立方体效果
    this.drawCube(ctx, halfSize);
    
    // 确定要绘制的点数
    let value = this.value;
    if (this.isRolling && this.rollFrames >= 10) {
      value = Math.floor(Math.random() * 6) + 1;
    }
    
    // 绘制点数
    this.drawDots(ctx, value, halfSize);
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 绘制立方体
   */
  drawCube(ctx, size) {
    // 绘制阴影
    ctx.save();
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 3;
    ctx.shadowOffsetY = 3;
    
    // 正面渐变
    const frontGradient = ctx.createLinearGradient(-size, -size, size, size);
    frontGradient.addColorStop(0, '#FFFFFF');
    frontGradient.addColorStop(1, '#F8F9FA');
    
    ctx.fillStyle = frontGradient;
    this.drawRoundedRect(ctx, -size, -size, size * 2, size * 2, size * 0.1);
    
    ctx.strokeStyle = '#2C3E50';
    ctx.lineWidth = 3;
    this.strokeRoundedRect(ctx, -size, -size, size * 2, size * 2, size * 0.1);
    ctx.restore();
    
    // 内部边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.lineWidth = 1;
    this.strokeRoundedRect(ctx, -size + 2, -size + 2, size * 2 - 4, size * 2 - 4, size * 0.08);
    
    // 顶面（3D效果）
    const topGradient = ctx.createLinearGradient(-size * 0.7, -size * 1.3, size * 0.7, -size);
    topGradient.addColorStop(0, '#E8E9EA');
    topGradient.addColorStop(1, '#D5DBDB');
    
    ctx.fillStyle = topGradient;
    ctx.beginPath();
    ctx.moveTo(-size, -size);
    ctx.lineTo(-size * 0.7, -size * 1.3);
    ctx.lineTo(size * 0.7, -size * 1.3);
    ctx.lineTo(size, -size);
    ctx.closePath();
    ctx.fill();
    
    ctx.strokeStyle = '#2C3E50';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 右面（3D效果）
    const rightGradient = ctx.createLinearGradient(size, -size, size * 1.3, size);
    rightGradient.addColorStop(0, '#D5DBDB');
    rightGradient.addColorStop(1, '#BDC3C7');
    
    ctx.fillStyle = rightGradient;
    ctx.beginPath();
    ctx.moveTo(size, -size);
    ctx.lineTo(size * 1.3, -size * 0.7);
    ctx.lineTo(size * 1.3, size * 0.7);
    ctx.lineTo(size, size);
    ctx.closePath();
    ctx.fill();
    
    ctx.strokeStyle = '#2C3E50';
    ctx.lineWidth = 2;
    ctx.stroke();
  }
  
  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.fill();
  }
  
  /**
   * 描边圆角矩形
   */
  strokeRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    ctx.stroke();
  }
  
  /**
   * 绘制骰子点数
   */
  drawDots(ctx, value, size) {
    const dotSize = size * 0.12;
    const offset = size * 0.35;
    
    switch (value) {
      case 1:
        this.drawDot(ctx, 0, 0, dotSize);
        break;
      case 2:
        this.drawDot(ctx, -offset, -offset, dotSize);
        this.drawDot(ctx, offset, offset, dotSize);
        break;
      case 3:
        this.drawDot(ctx, -offset, -offset, dotSize);
        this.drawDot(ctx, 0, 0, dotSize);
        this.drawDot(ctx, offset, offset, dotSize);
        break;
      case 4:
        this.drawDot(ctx, -offset, -offset, dotSize);
        this.drawDot(ctx, offset, -offset, dotSize);
        this.drawDot(ctx, -offset, offset, dotSize);
        this.drawDot(ctx, offset, offset, dotSize);
        break;
      case 5:
        this.drawDot(ctx, -offset, -offset, dotSize);
        this.drawDot(ctx, offset, -offset, dotSize);
        this.drawDot(ctx, 0, 0, dotSize);
        this.drawDot(ctx, -offset, offset, dotSize);
        this.drawDot(ctx, offset, offset, dotSize);
        break;
      case 6:
        this.drawDot(ctx, -offset, -offset, dotSize);
        this.drawDot(ctx, offset, -offset, dotSize);
        this.drawDot(ctx, -offset, 0, dotSize);
        this.drawDot(ctx, offset, 0, dotSize);
        this.drawDot(ctx, -offset, offset, dotSize);
        this.drawDot(ctx, offset, offset, dotSize);
        break;
    }
  }
  
  /**
   * 绘制骰子上的点
   * @param {Object} ctx - Canvas上下文
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @param {number} size - 点的大小
   */
  drawDot(ctx, x, y, size) {
    ctx.save();
    
    // 绘制点的阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
    ctx.shadowBlur = 3;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 2;
    
    // 绘制点的渐变
    const dotGradient = ctx.createRadialGradient(x - size * 0.3, y - size * 0.3, 0, x, y, size);
    dotGradient.addColorStop(0, '#2C3E50');
    dotGradient.addColorStop(0.7, '#34495E');
    dotGradient.addColorStop(1, '#1B2631');
    
    ctx.fillStyle = dotGradient;
    ctx.beginPath();
    ctx.arc(x, y, size, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.restore();
    
    // 绘制点的高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.beginPath();
    ctx.arc(x - size * 0.3, y - size * 0.3, size * 0.4, 0, 2 * Math.PI);
    ctx.fill();
  }
  
  /**
   * 检查点击是否命中骰子
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否命中
   */
  checkClick(x, y) {
    if (!this.isClickable || this.isRolling) return false;
    
    // 计算点击点到骰子中心的距离
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    const distance = Math.sqrt(
      Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
    );
    
    // 如果距离小于骰子半径，则命中
    return distance <= this.width / 2;
  }
  
  /**
   * 获取骰子值
   * @returns {number} 骰子值
   */
  getValue() {
    return this.value;
  }
  
  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 左上角X坐标
   * @param {number} y - 左上角Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundRect(ctx, x, y, width, height, radius) {
    if (width < 2 * radius) radius = width / 2;
    if (height < 2 * radius) radius = height / 2;
    
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.arcTo(x + width, y, x + width, y + height, radius);
    ctx.arcTo(x + width, y + height, x, y + height, radius);
    ctx.arcTo(x, y + height, x, y, radius);
    ctx.arcTo(x, y, x + width, y, radius);
    ctx.closePath();
  }
}