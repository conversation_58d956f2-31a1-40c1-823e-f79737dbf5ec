from fastapi import APIRouter, HTTPException
from typing import List, Dict, Optional
from datetime import datetime
from models.gpu_stats import (
    GPUStatsSummary, GPUStatsSummary_Pydantic, 
    GPUStatsResponse, GPUStatsRequest,
    GPUCountRequest, GPUCountResponse, GPUStatsDetail
)
from models.ip_user import IPUser
from collections import Counter
import math
from pydantic import BaseModel, field_validator


router = APIRouter(
    prefix="/gpu",
    tags=["GPU监控"]
)


@router.post("/stats", response_model=List[GPUStatsResponse])
async def get_gpu_stats(request: GPUStatsRequest):
    """
    根据IP地址、时间范围和GPU索引查询GPU监控数据
    
    参数:
        request: 包含ip、时间范围和gpu_index的请求
            - gpu_index: 指定查询的GPU索引，"all"表示所有GPU，0-7表示特定GPU
    
    返回:
        精简的数据格式，只包含时间、IP和监控指标
        如果IP在ip_user表中不存在，返回404错误
        如果IP存在但已标记为删除，返回404错误
        其他情况下，返回时间段内的数据或空列表[]
    """
    # 首先查询ip_user表，判断IP是否存在
    ip_user = await IPUser.filter(ip=request.ip).first()
    
    # 如果IP在ip_user表中不存在，返回404错误
    if not ip_user:
        raise HTTPException(status_code=404, detail=f"IP地址为 {request.ip} 的不存在")
    
    # 如果IP存在但已标记为删除
    if ip_user.is_deleted:
        raise HTTPException(status_code=404, detail=f"IP地址 {request.ip} 的监控数据已被删除")
    
    # 根据GPU索引参数处理查询逻辑
    if request.gpu_index == "all":
        # 查询所有GPU的汇总数据
        stats = await GPUStatsSummary.filter(
            ip=request.ip,
            timestamp__gte=request.start_time,
            timestamp__lte=request.end_time,
            is_deleted=False
        ).order_by('timestamp').all()
        
        # 如果在指定时间范围内没有数据，返回空列表
        if not stats:
            return []
        
        # 转换为精简的响应格式
        return [
            GPUStatsResponse(
                ip=stat.ip,
                timestamp=stat.timestamp,
                avg_usage=stat.avg_usage,
                total_memory_used=stat.total_memory_used,
                total_memory_total=stat.total_memory_total,
                memory_usage_percent=stat.memory_usage_percent,
                avg_temperature=stat.avg_temperature,
                gpu_index=None,  # None表示汇总数据
                is_summary=True  # True表示汇总数据
            )
            for stat in stats
        ]
    else:
        # 查询特定GPU索引的数据
        # 首先获取符合时间范围的所有汇总记录ID
        summary_ids = await GPUStatsSummary.filter(
            ip=request.ip,
            timestamp__gte=request.start_time,
            timestamp__lte=request.end_time,
            is_deleted=False
        ).order_by('timestamp').values_list('id', 'timestamp')
        
        if not summary_ids:
            # 如果在指定时间范围内没有数据，返回空列表
            return []
        
        # 构建ID到时间戳的映射
        id_to_timestamp = {id: ts for id, ts in summary_ids}
        summary_id_list = list(id_to_timestamp.keys())
        
        # 获取特定GPU索引的详情数据
        gpu_details = await GPUStatsDetail.filter(
            summary_id__in=summary_id_list,
            gpu_index=request.gpu_index,
            is_deleted=False
        ).all()
        
        if not gpu_details:
            # 如果没有找到特定GPU的数据，返回空列表
            return []
        
        # 整理数据：根据summary_id获取时间戳，并计算各个指标
        gpu_stats = []
        for detail in gpu_details:
            timestamp = id_to_timestamp[detail.summary_id]
            gpu_stats.append({
                'ip': request.ip,
                'timestamp': timestamp,
                'avg_usage': detail.gpu_usage,
                'total_memory_used': detail.gpu_memory_used,
                'total_memory_total': detail.gpu_memory_total,
                'memory_usage_percent': detail.gpu_memory_percent,
                'avg_temperature': detail.gpu_temperature,
                'gpu_index': detail.gpu_index,  # 设置特定GPU索引
                'is_summary': False  # False表示单个GPU数据
            })
        
        # 按时间戳排序
        gpu_stats.sort(key=lambda x: x['timestamp'])
        
        # 返回所有数据，不进行采样
        
        # 转换为响应格式
        return [
            GPUStatsResponse(
                ip=stat['ip'],
                timestamp=stat['timestamp'],
                avg_usage=stat['avg_usage'],
                total_memory_used=stat['total_memory_used'],
                total_memory_total=stat['total_memory_total'],
                memory_usage_percent=stat['memory_usage_percent'],
                avg_temperature=stat['avg_temperature'],
                gpu_index=stat['gpu_index'],  # 设置特定GPU索引
                is_summary=stat['is_summary']  # False表示单个GPU数据
            )
            for stat in gpu_stats
        ]

@router.post("/count", response_model=GPUCountResponse)
async def get_gpu_count(request: GPUCountRequest):
    """
    获取指定IP服务器上各GPU索引(0-7)的数量统计
    
    参数:
        request: 包含ip和gpu_count字段的请求
        
    返回:
        GPU索引计数信息
    """
    # 首先检查请求是否有效
    if not request.gpu_count:
        raise HTTPException(status_code=400, detail="请求必须包含gpu_count=true")
    
    # 检查数据库中是否存在该IP地址的记录
    ip_exists = await GPUStatsSummary.filter(ip=request.ip, is_deleted=False).exists()
    if not ip_exists:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {request.ip} 的监控数据")
    
    # 查询该IP的最新监控数据
    latest_summary = await GPUStatsSummary.filter(
        ip=request.ip, 
        is_deleted=False
    ).order_by('-timestamp').first()
    
    if not latest_summary:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {request.ip} 的有效监控数据")
    
    # 获取该记录对应的所有GPU详情
    gpu_details = await GPUStatsDetail.filter(
        summary_id=latest_summary.id,
        is_deleted=False
    ).all()
    
    if not gpu_details:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {request.ip} 的GPU详情数据")
    
    # 统计各GPU索引的数量
    gpu_indices = [detail.gpu_index for detail in gpu_details]
    counts = Counter(gpu_indices)
    
    # 确保0-7的所有索引都在结果中，不存在的设为0
    # 修改返回格式，将计数数字改为"gpu索引"的格式
    gpu_counts = {}
    for i in range(8):
        count = counts.get(i, 0)
        if count > 0:
            gpu_counts[str(i)] = f"gpu{i}"
        else:
            gpu_counts[str(i)] = ""
    
    # 返回结果
    return GPUCountResponse(
        ip=request.ip,
        gpu_counts=gpu_counts
    )

class GPUCountGetResponse(GPUCountResponse):
    """
    GPU数量GET请求响应模型
    """
    count: int  # 添加一个count字段，直接表示GPU的数量


@router.get("/count/{ip}", response_model=GPUCountGetResponse)
async def get_gpu_count_by_ip(ip: str):
    """
    获取指定IP地址服务器最后一次监控记录中的GPU数量
    
    参数:
        ip: 服务器IP地址
        
    返回:
        GPU数量信息，包括各GPU索引(0-7)的统计和总数
        如果IP不存在或已删除，返回404错误
    """
    # 检查IP是否存在且未被删除
    ip_user = await IPUser.filter(ip=ip).first()
    
    # 如果IP在ip_user表中不存在，返回404错误
    if not ip_user:
        raise HTTPException(status_code=404, detail=f"IP地址为 {ip} 的服务器不存在")
    
    # 如果IP存在但已标记为删除
    if ip_user.is_deleted:
        raise HTTPException(status_code=404, detail=f"IP地址 {ip} 的监控数据已被删除")
    
    # 查询该IP的最新监控数据
    latest_summary = await GPUStatsSummary.filter(
        ip=ip, 
        is_deleted=False
    ).order_by('-timestamp').first()
    
    if not latest_summary:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {ip} 的有效监控数据")
    
    # 获取该记录对应的所有GPU详情
    gpu_details = await GPUStatsDetail.filter(
        summary_id=latest_summary.id,
        is_deleted=False
    ).all()
    
    if not gpu_details:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {ip} 的GPU详情数据")
    
    # 统计各GPU索引的数量
    gpu_indices = [detail.gpu_index for detail in gpu_details]
    counts = Counter(gpu_indices)
    
    # 计算实际的GPU数量（有多少个不同的GPU索引）
    gpu_count = len(counts)
    
    # 确保0-7的所有索引都在结果中，不存在的设为空字符串
    gpu_counts = {}
    for i in range(8):
        count = counts.get(i, 0)
        if count > 0:
            gpu_counts[str(i)] = f"gpu{i}"
        else:
            gpu_counts[str(i)] = ""
    
    # 返回结果
    return GPUCountGetResponse(
        ip=ip,
        gpu_counts=gpu_counts,
        count=gpu_count
    )

class GPUTemperatureResponse(BaseModel):
    """
    GPU温度响应模型
    """
    ip: str
    timestamp: str  # 改为字符串类型，便于直接处理时区格式
    avg_temperature: float  # 平均温度(℃)
    detail_temperatures: Dict[str, float] = {}  # 每个GPU的温度 {"0": 温度值, "1": 温度值, ...}


@router.get("/temperature/{ip}", response_model=GPUTemperatureResponse)
async def get_gpu_temperature(ip: str):
    """
    获取指定IP地址服务器最后一次监控记录中的GPU平均温度
    
    参数:
        ip: 服务器IP地址
        
    返回:
        GPU平均温度信息，包括汇总的平均温度和每个GPU的详细温度
        如果IP不存在或已删除，返回404错误
    """
    # 检查IP是否存在且未被删除
    ip_user = await IPUser.filter(ip=ip).first()
    
    # 如果IP在ip_user表中不存在，返回404错误
    if not ip_user:
        raise HTTPException(status_code=404, detail=f"IP地址为 {ip} 的服务器不存在")
    
    # 如果IP存在但已标记为删除
    if ip_user.is_deleted:
        raise HTTPException(status_code=404, detail=f"IP地址 {ip} 的监控数据已被删除")
    
    # 查询该IP的最新监控数据
    latest_summary = await GPUStatsSummary.filter(
        ip=ip, 
        is_deleted=False
    ).order_by('-timestamp').first()
    
    if not latest_summary:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {ip} 的有效监控数据")
    
    # 获取该记录对应的所有GPU详情
    gpu_details = await GPUStatsDetail.filter(
        summary_id=latest_summary.id,
        is_deleted=False
    ).all()
    
    if not gpu_details:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {ip} 的GPU详情数据")
    
    # 获取每个GPU的温度
    detail_temperatures = {}
    for detail in gpu_details:
        detail_temperatures[str(detail.gpu_index)] = detail.gpu_temperature
    
    # 使用新的时区工具格式化时间戳
    from config.timezone_utils import TZ
    timestamp_str = TZ.to_display_format(latest_summary.timestamp, source_tz=TZ.UTC)
    
    # 返回结果，使用摘要中已计算好的平均温度
    return GPUTemperatureResponse(
        ip=ip,
        timestamp=timestamp_str,
        avg_temperature=latest_summary.avg_temperature,
        detail_temperatures=detail_temperatures
    )

class GPUTemperatureRequest(BaseModel):
    """
    GPU温度查询请求模型
    """
    ip: str
    start_time: datetime
    end_time: datetime
    
    @field_validator('ip')
    @classmethod
    def validate_ip(cls, v):
        if not v:
            raise ValueError("IP地址不能为空")
        return v
    
    @field_validator('start_time')
    @classmethod
    def validate_start_time(cls, v):
        if not v:
            raise ValueError("开始时间不能为空")
        return v
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v, values):
        if not v:
            raise ValueError("结束时间不能为空")
        
        # 检查开始时间和结束时间的逻辑关系
        if 'start_time' in values.data and values.data['start_time'] and v <= values.data['start_time']:
            raise ValueError("结束时间必须晚于开始时间")
        
        return v


class GPUTemperatureDetailItem(BaseModel):
    """
    扁平化的GPU温度详情项
    """
    timestamp: str  # 改为字符串类型，便于直接处理时区格式
    gpu_id: str  # 格式为 "GPU0", "GPU1" 等
    temperature: float


@router.post("/temperature/detail", response_model=List[GPUTemperatureDetailItem])
async def get_gpu_temperature_detail(request: GPUTemperatureRequest):
    """
    获取指定IP地址服务器在特定时间段内的所有GPU温度数据，以扁平化列表形式返回
    
    参数:
        request: 包含ip、start_time和end_time的请求
        
    返回:
        扁平化的GPU温度数据列表，每项包含时间戳、GPU ID和温度值
        如果IP不存在或已删除，返回404错误
        如果在指定时间范围内没有数据，返回空列表[]
    """
    # 检查IP是否存在且未被删除
    ip_user = await IPUser.filter(ip=request.ip).first()
    
    # 如果IP在ip_user表中不存在，返回404错误
    if not ip_user:
        raise HTTPException(status_code=404, detail=f"IP地址为 {request.ip} 的服务器不存在")
    
    # 如果IP存在但已标记为删除
    if ip_user.is_deleted:
        raise HTTPException(status_code=404, detail=f"IP地址 {request.ip} 的监控数据已被删除")
    
    # 查询指定时间范围内的所有GPU监控摘要
    summaries = await GPUStatsSummary.filter(
        ip=request.ip,
        timestamp__gte=request.start_time,
        timestamp__lte=request.end_time,
        is_deleted=False
    ).order_by('timestamp').all()
    
    # 如果在指定时间范围内没有数据，返回空列表
    if not summaries:
        return []
    
    # 构建扁平化的温度数据列表
    flat_temperature_data = []
    
    # 对每个摘要记录获取对应的GPU详情数据
    for summary in summaries:
        # 获取该记录对应的所有GPU详情
        gpu_details = await GPUStatsDetail.filter(
            summary_id=summary.id,
            is_deleted=False
        ).all()
        
        # 如果没有找到GPU详情数据，跳过该记录
        if not gpu_details:
            continue
        
        # 使用新的时区工具格式化时间戳
        from config.timezone_utils import TZ
        timestamp_str = TZ.to_display_format(summary.timestamp)
        
        # 将每个GPU的温度数据添加到扁平化列表中
        for detail in gpu_details:
            flat_temperature_data.append(
                GPUTemperatureDetailItem(
                    timestamp=timestamp_str,
                    gpu_id=f"GPU{detail.gpu_index}",
                    temperature=detail.gpu_temperature
                )
            )
    
    # 返回扁平化的数据列表
    return flat_temperature_data 