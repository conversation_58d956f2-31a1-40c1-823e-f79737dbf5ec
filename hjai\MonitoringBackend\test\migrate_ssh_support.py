"""
数据库迁移脚本：添加SSH密钥认证支持
更新ip_users表结构以支持SSH密钥认证
"""
import asyncio
import logging
from tortoise import Tortoise
from config.db import TORTOISE_ORM

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def migrate_database():
    """执行数据库迁移"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 获取数据库连接
        connection = Tortoise.get_connection("default")
        
        logger.info("开始执行数据库迁移...")
        
        # 检查表是否存在
        table_exists_query = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = 'ip_users'
        """
        
        result = await connection.execute_query(table_exists_query)
        if result[1][0]['count'] == 0:
            logger.error("ip_users表不存在，请先运行初始化脚本")
            return False
        
        # 检查password字段是否允许NULL
        column_info_query = """
        SELECT COLUMN_NAME, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
        FROM information_schema.COLUMNS 
        WHERE table_schema = DATABASE() 
        AND table_name = 'ip_users' 
        AND column_name = 'password'
        """
        
        result = await connection.execute_query(column_info_query)
        if result[1]:
            column_info = result[1][0]
            if column_info['IS_NULLABLE'] == 'NO':
                logger.info("修改password字段允许NULL值...")
                await connection.execute_query("""
                    ALTER TABLE ip_users 
                    MODIFY COLUMN password VARCHAR(128) NULL 
                    COMMENT '密码（可为空，使用SSH密钥认证时）'
                """)
                logger.info("password字段修改完成")
            else:
                logger.info("password字段已经允许NULL值")
        
        # 检查是否存在SSH相关字段
        ssh_columns_query = """
        SELECT COLUMN_NAME 
        FROM information_schema.COLUMNS 
        WHERE table_schema = DATABASE() 
        AND table_name = 'ip_users' 
        AND column_name IN ('use_ssh_key', 'ssh_key_path')
        """
        
        result = await connection.execute_query(ssh_columns_query)
        existing_columns = [row['COLUMN_NAME'] for row in result[1]]
        
        # 添加use_ssh_key字段
        if 'use_ssh_key' not in existing_columns:
            logger.info("添加use_ssh_key字段...")
            await connection.execute_query("""
                ALTER TABLE ip_users 
                ADD COLUMN use_ssh_key TINYINT(1) NOT NULL DEFAULT 0 
                COMMENT '是否使用SSH密钥认证'
            """)
            logger.info("use_ssh_key字段添加完成")
        else:
            logger.info("use_ssh_key字段已存在")
        
        # 添加ssh_key_path字段
        if 'ssh_key_path' not in existing_columns:
            logger.info("添加ssh_key_path字段...")
            await connection.execute_query("""
                ALTER TABLE ip_users 
                ADD COLUMN ssh_key_path VARCHAR(255) NULL 
                COMMENT 'SSH私钥文件路径（可选，为空时使用默认路径）'
            """)
            logger.info("ssh_key_path字段添加完成")
        else:
            logger.info("ssh_key_path字段已存在")
        
        # 更新现有记录：如果密码为空，自动设置为使用SSH密钥认证
        logger.info("更新现有记录的认证方式...")
        update_query = """
        UPDATE ip_users 
        SET use_ssh_key = 1 
        WHERE (password IS NULL OR password = '') AND use_ssh_key = 0
        """
        result = await connection.execute_query(update_query)
        logger.info(f"已更新 {result[0]} 条记录为SSH密钥认证")
        
        # 显示迁移后的表结构
        logger.info("迁移完成，当前表结构:")
        structure_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM information_schema.COLUMNS 
        WHERE table_schema = DATABASE() AND table_name = 'ip_users'
        ORDER BY ORDINAL_POSITION
        """
        
        result = await connection.execute_query(structure_query)
        for column in result[1]:
            nullable = "NULL" if column['IS_NULLABLE'] == 'YES' else "NOT NULL"
            default = f"DEFAULT {column['COLUMN_DEFAULT']}" if column['COLUMN_DEFAULT'] else ""
            comment = f"COMMENT '{column['COLUMN_COMMENT']}'" if column['COLUMN_COMMENT'] else ""
            logger.info(f"  {column['COLUMN_NAME']}: {column['DATA_TYPE']} {nullable} {default} {comment}")
        
        logger.info("数据库迁移成功完成！")
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}")
        return False
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


async def rollback_migration():
    """回滚数据库迁移"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 获取数据库连接
        connection = Tortoise.get_connection("default")
        
        logger.info("开始回滚数据库迁移...")
        
        # 删除SSH相关字段
        try:
            await connection.execute_query("ALTER TABLE ip_users DROP COLUMN use_ssh_key")
            logger.info("已删除use_ssh_key字段")
        except Exception as e:
            logger.warning(f"删除use_ssh_key字段失败: {e}")
        
        try:
            await connection.execute_query("ALTER TABLE ip_users DROP COLUMN ssh_key_path")
            logger.info("已删除ssh_key_path字段")
        except Exception as e:
            logger.warning(f"删除ssh_key_path字段失败: {e}")
        
        # 恢复password字段为NOT NULL
        try:
            await connection.execute_query("""
                ALTER TABLE ip_users 
                MODIFY COLUMN password VARCHAR(128) NOT NULL 
                COMMENT '密码'
            """)
            logger.info("已恢复password字段为NOT NULL")
        except Exception as e:
            logger.warning(f"恢复password字段失败: {e}")
        
        logger.info("数据库迁移回滚完成！")
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移回滚失败: {str(e)}")
        return False
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        # 回滚迁移
        success = asyncio.run(rollback_migration())
    else:
        # 执行迁移
        success = asyncio.run(migrate_database())
    
    if success:
        print("操作成功完成！")
        sys.exit(0)
    else:
        print("操作失败！")
        sys.exit(1)
