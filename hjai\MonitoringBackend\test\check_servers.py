"""
检查服务器配置脚本

用于查询数据库中的服务器配置
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# 导入配置
from tortoise import Tortoise
from config.db import TORTOISE_ORM

# 导入模型
from models.ip_user import IPUser

async def check_servers():
    """检查数据库中的服务器配置"""
    logger.info("开始检查服务器配置")
    
    # 初始化ORM
    logger.info("初始化ORM...")
    await Tortoise.init(config=TORTOISE_ORM)
    
    try:
        # 查询服务器数据
        servers = await IPUser.all()
        logger.info(f"数据库中有 {len(servers)} 台服务器")
        
        if servers:
            for server in servers:
                logger.info(f"ID: {server.id}, IP: {server.ip}, 用户名: {server.username}, 可连接: {getattr(server, 'is_connectable', '未知')}")
        else:
            logger.warning("数据库中没有服务器配置，请先添加服务器")
            
            # 添加一个测试服务器
            add_test_server = input("是否添加一个本地测试服务器? (Y/N): ").strip().upper()
            if add_test_server == "Y":
                # 添加本地测试服务器
                server = await IPUser.create(
                    ip="127.0.0.1",
                    username="test",
                    password="test",
                    is_connectable=True
                )
                logger.info(f"已添加测试服务器: ID={server.id}, IP={server.ip}")
                
    except Exception as e:
        logger.error(f"检查过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭ORM连接
        await Tortoise.close_connections()
        logger.info("检查完成")

async def main():
    """主函数"""
    await check_servers()

if __name__ == "__main__":
    asyncio.run(main()) 