from fastapi import APIRouter, HTTPException
from typing import List, Optional, Dict, Any
from models.network_stats import NetworkStatsSummary, NetworkStatsDetail
from models.ip_user import IPUser
from pydantic import BaseModel, Field, field_validator
from datetime import datetime
import re
# 导入新的时区处理工具
from config.timezone_utils import TZ
# 保持向后兼容
from config.timezone import convert_to_shanghai, now, add_tz
from tortoise.expressions import Q


router = APIRouter(
    prefix="/network",
    tags=["网络监控"]
)


@router.get("/interfaces/{ip}", response_model=List[str])
async def get_network_interfaces(ip: str):
    """
    获取指定IP地址服务器的所有网卡名称
    
    参数:
        ip: 服务器IP地址
        
    返回:
        网卡名称列表
        如果IP不存在，返回404错误
    """
    # 首先检查IP是否存在于IP用户表中
    ip_user = await IPUser.filter(ip=ip).first()
    
    # 如果IP在ip_user表中不存在，返回404错误
    if not ip_user:
        raise HTTPException(status_code=404, detail=f"IP地址为 {ip} 的服务器不存在")
    
    # 如果IP存在但已标记为删除
    if ip_user.is_deleted:
        raise HTTPException(status_code=404, detail=f"IP地址 {ip} 的监控数据已被删除")
    
    # 获取最新的网络监控数据摘要
    latest_summary = await NetworkStatsSummary.filter(
        ip=ip, 
        is_deleted=False
    ).order_by('-timestamp').first()
    
    # 如果没有找到网络监控数据
    if not latest_summary:
        raise HTTPException(status_code=404, detail=f"未找到IP地址为 {ip} 的网络监控数据")
    
    # 获取该摘要下的所有网卡详情
    network_details = await NetworkStatsDetail.filter(
        summary_id=latest_summary.id,
        is_deleted=False
    ).all()
    
    # 如果没有找到网卡详情，返回空列表
    if not network_details:
        return []
    
    # 提取所有网卡名称并返回
    return [detail.interface_name for detail in network_details]


class NetworkStatsQueryRequest(BaseModel):
    """
    网络统计查询请求模型
    """
    ip: str = Field(..., description="服务器IP地址，可使用${node_ip}变量")
    interface: str = Field(..., description="网卡名称")
    start_time: datetime = Field(..., description="开始时间，ISO 8601格式")
    end_time: datetime = Field(..., description="结束时间，ISO 8601格式")
    
    # 验证IP地址格式
    @field_validator('ip')
    def validate_ip_format(cls, v):
        """验证IP地址是否符合IPv4格式或是${node_ip}变量"""
        # 如果是变量占位符，直接通过
        if v == "${node_ip}":
            return v
            
        # 否则验证IPv4格式
        ipv4_pattern = r"^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$"
        if not re.match(ipv4_pattern, v):
            raise ValueError('必须是有效的IPv4地址格式，例如：***********，或使用${node_ip}变量')
        return v
    
    # 验证时间关系
    @field_validator('end_time')
    def validate_end_time(cls, v, info):
        """验证结束时间必须晚于开始时间"""
        if hasattr(info.data, 'start_time') and v <= info.data.start_time:
            raise ValueError('结束时间必须晚于开始时间')
        return v
    
    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


class NetworkStatsQueryResponse(BaseModel):
    """
    网络统计查询响应模型
    """
    code: int
    message: str
    data: Optional[dict] = None
    
    class Config:
        """Pydantic配置 - 使用新的时区处理方式"""
        json_encoders = {
            datetime: lambda dt: TZ.to_display_format(dt)
        }


@router.post("/stats/data", response_model=NetworkStatsQueryResponse)
async def get_network_stats_data(request: NetworkStatsQueryRequest):
    """
    查询指定时间范围内的网络统计数据
    
    参数:
        request: 查询请求
            - ip: 服务器IP地址，支持${node_ip}变量
            - interface: 网卡名称
            - start_time: 开始时间
            - end_time: 结束时间
    
    返回:
        - 查询结果，包含指定时间范围内的网络统计数据
        - 如果验证失败，返回详细的错误信息
    """
    # 处理变量替换
    ip = request.ip
    if ip == "${node_ip}":
        # 在实际场景中，这里应该从请求上下文获取真实的node_ip
        # 例如从请求头或认证信息中获取
        return NetworkStatsQueryResponse(
            code=400,
            message="变量${node_ip}需要在请求发送前被替换为实际的IP地址"
        )
    
    # 1. 验证IP是否存在且未被删除
    ip_user = await IPUser.filter(ip=ip).first()
    if not ip_user:
        return NetworkStatsQueryResponse(
            code=404,
            message=f"IP地址 {ip} 不存在于系统中"
        )
    
    if ip_user.is_deleted:
        return NetworkStatsQueryResponse(
            code=404,
            message=f"IP地址 {ip} 已被标记为删除"
        )
    
    # 2. 验证网卡名称是否存在
    # 先检查是否有任何匹配该网卡名称的记录
    interface_exists = await NetworkStatsDetail.filter(
        interface_name=request.interface,
        is_deleted=False
    ).exists()
    
    if not interface_exists:
        return NetworkStatsQueryResponse(
            code=404,
            message=f"网卡名称 '{request.interface}' 不存在于系统中"
        )
    
    # 3. 验证时间区间（start_time和end_time的验证已通过Pydantic模型完成）
    # 移除时间区间限制检查
    
    # 4. 获取符合条件的网络统计数据摘要
    summaries = await NetworkStatsSummary.filter(
        ip=ip,
        is_deleted=False,
        timestamp__gte=request.start_time,
        timestamp__lte=request.end_time
    ).order_by('timestamp').all()
    
    # 当时间段内没有数据时，返回空列表而非错误
    if not summaries:
        return NetworkStatsQueryResponse(
            code=200,
            message=f"在指定时间范围内未找到数据",
            data={
                "query_params": {
                    "ip": ip,
                    "interface": request.interface,
                    "start_time": add_tz(request.start_time).isoformat(),
                    "end_time": add_tz(request.end_time).isoformat(),
                },
                "summary": {
                    "total_rx_bytes": 0,
                    "total_tx_bytes": 0,
                    "total_rx_mb": 0,
                    "total_tx_mb": 0,
                    "data_points_count": 0,
                    "interface": request.interface
                },
                "data_points": []
            }
        )
    
    # 5. 获取每个摘要对应的指定网卡的详细数据
    summary_ids = [summary.id for summary in summaries]
    network_details = await NetworkStatsDetail.filter(
        summary_id__in=summary_ids,
        interface_name=request.interface,
        is_deleted=False
    ).prefetch_related('summary').all()
    
    if not network_details:
        return NetworkStatsQueryResponse(
            code=404,
            message=f"在指定时间范围内未找到网卡 '{request.interface}' 的详细监控数据"
        )
    
    # 6. 构建响应数据
    # 创建时间序列数据点
    data_points = []
    for detail in network_details:
        data_point = {
            "timestamp": detail.summary.timestamp.isoformat(),
            "interface_name": detail.interface_name,
            "state": detail.state,
            "rx_bytes": detail.rx_bytes,
            "rx_mb": detail.rx_mb,
            "tx_bytes": detail.tx_bytes,
            "tx_mb": detail.tx_mb,
            "rx_packets": detail.rx_packets,
            "tx_packets": detail.tx_packets,
            "current_rx_speed": detail.current_rx_speed,
            "current_tx_speed": detail.current_tx_speed
        }
        data_points.append(data_point)
    
    # 按时间戳排序数据点
    data_points.sort(key=lambda x: x["timestamp"])
    
    # 计算一些统计摘要
    stats_summary = {}
    if data_points:
        # 计算接收/发送数据总量
        total_rx_bytes = sum(point["rx_bytes"] for point in data_points)
        total_tx_bytes = sum(point["tx_bytes"] for point in data_points)
        total_rx_mb = sum(point["rx_mb"] for point in data_points)
        total_tx_mb = sum(point["tx_mb"] for point in data_points)
        
        # 其他可能的统计信息
        stats_summary = {
            "total_rx_bytes": total_rx_bytes,
            "total_tx_bytes": total_tx_bytes,
            "total_rx_mb": total_rx_mb,
            "total_tx_mb": total_tx_mb,
            "data_points_count": len(data_points),
            "interface": request.interface,
        }
    
    # 返回查询结果
    return NetworkStatsQueryResponse(
        code=200,
        message="查询成功",
        data={
            "query_params": {
                "ip": ip,
                "interface": request.interface,
                "start_time": add_tz(request.start_time).isoformat(),
                "end_time": add_tz(request.end_time).isoformat(),
            },
            "summary": stats_summary,
            "data_points": data_points
        }
    ) 