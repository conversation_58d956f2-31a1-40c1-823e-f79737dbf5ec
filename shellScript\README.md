# GEMM 测试脚本使用说明

## 脚本说明

本项目包含以下脚本文件：

- `gemm_test_run.sh`: Linux/Ubuntu 环境下的 GEMM 测试脚本
- `gemm_test_run.bat`: Windows 环境下的 GEMM 测试脚本

这些脚本用于测试不同精度下的 GEMM (通用矩阵乘法) 性能，包括 FP8、INT8、FP16、BF16、TF32 和 FP32。

## 常见问题解决方案

### libcublasLt.so.12 库文件找不到

如果遇到 `libcublasLt.so.12: cannot open shared object file: No such file or directory` 错误，说明系统找不到 CUDA 库文件。解决方法：

1. **安装 CUDA 工具包**：
   ```bash
   sudo apt-get install cuda-toolkit
   # 或
   sudo apt install nvidia-cuda-toolkit
   ```

2. **手动设置 LD_LIBRARY_PATH**：
   ```bash
   export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH
   ```

3. **添加 CUDA 库路径到系统**：
   ```bash
   sudo sh -c 'echo "/usr/local/cuda/lib64" > /etc/ld.so.conf.d/cuda.conf'
   sudo ldconfig
   ```

### Windows 环境下的 CUDA 配置

在 Windows 环境下，确保：

1. 已安装 NVIDIA CUDA Toolkit
2. 系统环境变量 PATH 中包含 CUDA 的 bin 目录
3. 使用 `gemm_test_run.bat` 脚本运行测试

## 使用方法

### Linux/Ubuntu

```bash
# 赋予脚本执行权限
chmod +x gemm_test_run.sh

# 运行脚本
./gemm_test_run.sh
```

### Windows

```cmd
# 直接运行批处理文件
gemm_test_run.bat
```

## 测试结果

测试结果将保存在 `result.md` 文件中，并以表格形式显示各种精度下的 GEMM 性能（TFLOPS）。