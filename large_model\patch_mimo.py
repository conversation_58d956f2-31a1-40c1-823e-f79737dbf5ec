import os

# 定义ALL_PARALLEL_STYLES变量
patch_code = """
# 添加缺失的ALL_PARALLEL_STYLES变量
if not hasattr(transformers.modeling_utils, 'ALL_PARALLEL_STYLES'):
    transformers.modeling_utils.ALL_PARALLEL_STYLES = ["tensor", "shard", "pipeline", "expert"]
"""

# 找到问题文件并修补
def patch_model_file():
    mimo_path = "/root/.cache/huggingface/modules/transformers_modules/MiMo-7B-RL-0530/modeling_mimo.py"
    
    if not os.path.exists(mimo_path):
        print(f"模型文件不存在：{mimo_path}")
        return False
    
    # 读取现有文件
    with open(mimo_path, 'r') as f:
        content = f.read()
    
    # 在文件头部添加定义
    modified_content = "import transformers\n\n" + patch_code + "\n" + content
    
    # 写回文件
    try:
        with open(mimo_path, 'w') as f:
            f.write(modified_content)
        print(f"成功修补文件：{mimo_path}")
        return True
    except Exception as e:
        print(f"修补文件失败：{e}")
        return False

if __name__ == "__main__":
    if patch_model_file():
        print("修补完成，现在可以尝试重新运行xiaomi.py")
    else:
        print("修补失败") 