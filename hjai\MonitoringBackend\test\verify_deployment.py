"""
部署验证脚本

验证时区处理修复后的系统是否正常工作
"""

import os
import sys
import asyncio
import json
from datetime import datetime, timezone

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

def test_timezone_utils_import():
    """测试新时区工具导入"""
    try:
        from config.timezone_utils import TZ, ModernJSONEncoder, DatabaseTimeUtils
        print("✅ 新时区工具导入成功")
        return True
    except Exception as e:
        print(f"❌ 新时区工具导入失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        from config.timezone import now, utcnow, add_tz, DateTimeWithTZEncoder
        
        # 测试旧函数
        old_now = now()
        old_utc = utcnow()
        
        # 验证返回类型
        assert isinstance(old_now, datetime)
        assert isinstance(old_utc, datetime)
        
        print("✅ 向后兼容性测试通过")
        return True
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False

def test_models_import():
    """测试模型导入"""
    try:
        from models.base_model import BaseModel
        from models.cpu_stats import CPUStats
        from models.memory_stats import MemoryStats
        from models.network_stats import NetworkStatsSummary
        from models.custom_monitor import MonitorData
        
        print("✅ 所有模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def test_api_routers_import():
    """测试API路由导入"""
    try:
        from routers.memory_stats import router as memory_router
        from routers.custom_monitor import router as custom_router
        from routers.gpu_stats import router as gpu_router
        from routers.network_stats import router as network_router
        
        print("✅ 所有API路由导入成功")
        return True
    except Exception as e:
        print(f"❌ API路由导入失败: {e}")
        return False

def test_monitoring_scripts_import():
    """测试监控脚本导入"""
    try:
        from scripts.monitor_scheduler import MonitorScheduler
        from scripts.cpu_monitor import CPUMonitor
        from scripts.gpu_monitor import GPUMonitor
        from scripts.memory_monitor import MemoryMonitor
        from scripts.network_monitor import NetworkMonitor
        
        print("✅ 所有监控脚本导入成功")
        return True
    except Exception as e:
        print(f"❌ 监控脚本导入失败: {e}")
        return False

def test_timezone_functionality():
    """测试时区功能"""
    try:
        from config.timezone_utils import TZ
        
        # 测试基本功能
        utc_now = TZ.now_utc()
        shanghai_now = TZ.now_shanghai()
        
        # 测试转换
        utc_to_shanghai = TZ.to_shanghai(utc_now)
        shanghai_to_utc = TZ.to_utc(shanghai_now)
        
        # 测试显示格式
        display_format = TZ.to_display_format(utc_now)
        
        # 验证结果
        assert utc_now.tzinfo == timezone.utc
        assert "+08:00" in display_format
        
        print("✅ 时区功能测试通过")
        print(f"   UTC时间: {utc_now}")
        print(f"   上海时间: {shanghai_now}")
        print(f"   显示格式: {display_format}")
        return True
    except Exception as e:
        print(f"❌ 时区功能测试失败: {e}")
        return False

def test_json_encoding():
    """测试JSON编码"""
    try:
        from config.timezone_utils import ModernJSONEncoder, TZ
        
        # 创建测试数据
        test_data = {
            "timestamp": TZ.now_utc().replace(tzinfo=None),
            "value": 123.45,
            "status": "active"
        }
        
        # 测试编码
        json_str = json.dumps(test_data, cls=ModernJSONEncoder)
        
        # 验证结果
        assert "+08:00" in json_str
        
        print("✅ JSON编码测试通过")
        print(f"   编码结果: {json_str}")
        return True
    except Exception as e:
        print(f"❌ JSON编码测试失败: {e}")
        return False

def test_database_config():
    """测试数据库配置"""
    try:
        from config.db import TORTOISE_ORM
        
        # 验证配置
        assert "connections" in TORTOISE_ORM
        assert "default" in TORTOISE_ORM["connections"]
        assert TORTOISE_ORM["use_tz"] == True
        assert TORTOISE_ORM["timezone"] == "UTC"
        
        print("✅ 数据库配置验证通过")
        return True
    except Exception as e:
        print(f"❌ 数据库配置验证失败: {e}")
        return False

def test_main_app_import():
    """测试主应用导入"""
    try:
        # 只测试导入，不启动服务器
        import main
        
        print("✅ 主应用导入成功")
        return True
    except Exception as e:
        print(f"❌ 主应用导入失败: {e}")
        return False

def run_deployment_verification():
    """运行部署验证"""
    print("🚀 开始部署验证...")
    print("=" * 50)
    
    tests = [
        ("时区工具导入", test_timezone_utils_import),
        ("向后兼容性", test_backward_compatibility),
        ("模型导入", test_models_import),
        ("API路由导入", test_api_routers_import),
        ("监控脚本导入", test_monitoring_scripts_import),
        ("时区功能", test_timezone_functionality),
        ("JSON编码", test_json_encoding),
        ("数据库配置", test_database_config),
        ("主应用导入", test_main_app_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   测试失败")
        except Exception as e:
            print(f"   测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 部署验证全部通过！系统可以安全部署。")
        print("\n📝 部署建议:")
        print("1. 重启应用服务器以应用时区处理更改")
        print("2. 监控应用日志确保时区转换正常")
        print("3. 验证前端显示的时间格式正确")
        print("4. 检查数据库中新记录的时间戳格式")
        return True
    else:
        print("⚠️  部分验证失败，请检查并修复问题后再部署。")
        return False

def main():
    """主函数"""
    try:
        success = run_deployment_verification()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ 验证过程发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
