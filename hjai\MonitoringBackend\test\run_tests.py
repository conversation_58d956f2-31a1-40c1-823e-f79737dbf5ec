"""
测试运行脚本

运行所有自定义监控相关的测试
"""

import sys
import os
import pytest
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_all_tests():
    """运行所有测试"""
    test_files = [
        "test/test_custom_monitor_service.py",
        "test/test_monitor_test_service.py", 
        "test/test_security_validator.py",
        "test/test_ssh_connection_pool.py",
        "test/test_custom_monitor_executor.py"
    ]
    
    print("开始运行自定义监控模块测试...")
    print("=" * 60)
    
    # 运行测试
    exit_code = pytest.main([
        "-v",  # 详细输出
        "--tb=short",  # 简短的错误回溯
        "--color=yes",  # 彩色输出
        "--durations=10",  # 显示最慢的10个测试
        *test_files
    ])
    
    if exit_code == 0:
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！")
    else:
        print("\n" + "=" * 60)
        print("❌ 部分测试失败，请检查上面的错误信息")
    
    return exit_code


def run_specific_test(test_name):
    """运行特定的测试"""
    test_mapping = {
        "service": "test/test_custom_monitor_service.py",
        "test_service": "test/test_monitor_test_service.py",
        "security": "test/test_security_validator.py",
        "pool": "test/test_ssh_connection_pool.py",
        "executor": "test/test_custom_monitor_executor.py"
    }
    
    if test_name not in test_mapping:
        print(f"未知的测试名称: {test_name}")
        print(f"可用的测试: {', '.join(test_mapping.keys())}")
        return 1
    
    test_file = test_mapping[test_name]
    print(f"运行测试: {test_file}")
    
    exit_code = pytest.main([
        "-v",
        "--tb=short",
        "--color=yes",
        test_file
    ])
    
    return exit_code


def run_coverage_test():
    """运行带覆盖率的测试"""
    try:
        import pytest_cov
    except ImportError:
        print("需要安装 pytest-cov 来运行覆盖率测试")
        print("运行: pip install pytest-cov")
        return 1
    
    test_files = [
        "test/test_custom_monitor_service.py",
        "test/test_monitor_test_service.py", 
        "test/test_security_validator.py",
        "test/test_ssh_connection_pool.py",
        "test/test_custom_monitor_executor.py"
    ]
    
    print("运行带覆盖率的测试...")
    
    exit_code = pytest.main([
        "-v",
        "--cov=services",
        "--cov=utils",
        "--cov=core",
        "--cov-report=html",
        "--cov-report=term-missing",
        *test_files
    ])
    
    if exit_code == 0:
        print("\n覆盖率报告已生成到 htmlcov/ 目录")
    
    return exit_code


def setup_test_environment():
    """设置测试环境"""
    # 创建必要的目录
    test_dirs = ["logs", "temp"]
    for dir_name in test_dirs:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
    
    # 设置环境变量
    os.environ["TESTING"] = "1"
    os.environ["LOG_LEVEL"] = "DEBUG"
    
    print("测试环境设置完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="运行自定义监控模块测试")
    parser.add_argument(
        "--test", 
        choices=["service", "test_service", "security", "pool", "executor"],
        help="运行特定的测试模块"
    )
    parser.add_argument(
        "--coverage", 
        action="store_true",
        help="运行带覆盖率的测试"
    )
    parser.add_argument(
        "--setup-only", 
        action="store_true",
        help="只设置测试环境，不运行测试"
    )
    
    args = parser.parse_args()
    
    # 设置测试环境
    setup_test_environment()
    
    if args.setup_only:
        print("测试环境设置完成，退出")
        return 0
    
    if args.coverage:
        return run_coverage_test()
    elif args.test:
        return run_specific_test(args.test)
    else:
        return run_all_tests()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
