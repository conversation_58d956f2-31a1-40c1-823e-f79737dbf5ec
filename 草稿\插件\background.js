let refreshInterval;

chrome.storage.onChanged.addListener((changes) => {
  // 当interval或isActive状态改变时
  if (changes.interval || changes.isActive) {
    // 清除现有的定时器
    clearInterval(refreshInterval);
    
    // 获取当前的状态
    chrome.storage.sync.get(['interval', 'isActive'], (result) => {
      // 只有当isActive为true且interval有值时才设置定时器
      if (result.isActive && result.interval) {
        refreshInterval = setInterval(() => {
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) chrome.tabs.reload(tabs[0].id);
          });
        }, result.interval);
      }
    });
  }
});