# 监控后端API文档

## 项目概述

**项目名称**: 监控后端API  
**项目描述**: 航锦云监控系统  
**版本**: 1.0.0  
**基础URL**: `http://localhost:8000` (默认)  
**文档地址**: `/docs` (Swagger UI), `/redoc` (ReDoc)

### 技术栈
- **Web框架**: FastAPI
- **数据库ORM**: Tortoise ORM
- **时区处理**: Asia/Shanghai (UTC+8)
- **认证方式**: SSH密钥认证 / 密码认证

### 核心功能
- 服务器资源监控 (CPU、内存、GPU、网络)
- IP用户管理
- 自定义监控项配置
- 实时数据采集和查询
- 统一的时区处理和错误响应

---

## 目录

1. [通用响应格式](#通用响应格式)
2. [错误处理](#错误处理)
3. [API端点](#api端点)
   - [根路由](#根路由)
   - [IP用户管理](#ip用户管理)
   - [GPU监控](#gpu监控)
   - [内存监控](#内存监控)
   - [网络监控](#网络监控)
   - [自定义监控](#自定义监控)
4. [数据模型](#数据模型)
5. [时区处理](#时区处理)

---

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 分页响应
```json
{
  "data": [],
  "total": 100,
  "page": 1,
  "page_size": 10,
  "pages": 10
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述"
}
```

---

## 错误处理

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 422 | 请求验证失败 |
| 500 | 服务器内部错误 |

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 参数验证失败 |
| 404 | IP地址不存在或已删除 |
| 404 | 监控数据不存在 |

---

## API端点

### 根路由

#### GET /
获取API根信息

**响应示例**:
```json
{
  "message": "Hello World"
}
```

---

### IP用户管理

#### POST /ip/user-list
获取IP用户列表（分页）

**请求体**:
```json
{
  "page": 1,
  "page_size": 10,
  "is_deleted": false,
  "is_connectable": true
}
```

**参数说明**:
- `page`: 页码，从1开始
- `page_size`: 每页记录数，默认10
- `is_deleted`: 是否删除 (可选)
- `is_connectable`: 是否可连通 (可选)

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "ip": "*************",
      "username": "admin",
      "is_deleted": false,
      "is_connectable": true,
      "use_ssh_key": false,
      "ssh_key_path": null
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "pages": 1
}
```

#### POST /ip/add
添加新的IP用户

**请求体**:
```json
{
  "ip": "*************",
  "user": "admin",
  "password": "password123",
  "use_ssh_key": false,
  "ssh_key_path": null
}
```

**参数说明**:
- `ip`: IP地址 (必须是有效的IPv4格式)
- `user`: 用户名 (必填)
- `password`: 密码 (可选，为空时使用SSH密钥认证)
- `use_ssh_key`: 是否使用SSH密钥认证
- `ssh_key_path`: SSH私钥文件路径 (可选)

**响应示例**:
```json
{
  "code": 200,
  "message": "添加成功，认证方式: 密码认证"
}
```

---

### GPU监控

#### POST /gpu/stats
查询GPU监控数据

**请求体**:
```json
{
  "ip": "*************",
  "start_time": "2024-01-01T00:00:00",
  "end_time": "2024-01-01T23:59:59",
  "gpu_index": "all"
}
```

**参数说明**:
- `ip`: 服务器IP地址
- `start_time`: 开始时间
- `end_time`: 结束时间
- `gpu_index`: GPU索引 ("all"表示所有GPU，0-7表示特定GPU)

**响应示例**:
```json
[
  {
    "ip": "*************",
    "timestamp": "2024-01-01T10:00:00+08:00",
    "avg_usage": 75.5,
    "total_memory_used": 8192.0,
    "total_memory_total": 11264.0,
    "memory_usage_percent": 72.7,
    "avg_temperature": 65.0,
    "gpu_index": null,
    "is_summary": true
  }
]
```

#### GET /gpu/count/{ip}
获取指定IP的GPU数量信息

**路径参数**:
- `ip`: 服务器IP地址

**响应示例**:
```json
{
  "ip": "*************",
  "total_gpu_count": 8,
  "gpu_details": {
    "0": 1,
    "1": 1,
    "2": 1,
    "3": 1,
    "4": 1,
    "5": 1,
    "6": 1,
    "7": 1
  },
  "timestamp": "2024-01-01T10:00:00+08:00"
}
```

#### GET /gpu/temperature/{ip}
获取指定IP的GPU平均温度

**路径参数**:
- `ip`: 服务器IP地址

**响应示例**:
```json
{
  "ip": "*************",
  "avg_temperature": 65.0,
  "gpu_temperatures": [
    {"gpu_index": 0, "temperature": 64.0},
    {"gpu_index": 1, "temperature": 66.0}
  ],
  "timestamp": "2024-01-01T10:00:00+08:00"
}
```

---

### 内存监控

#### POST /memory/stats
查询内存监控数据

**请求体**:
```json
{
  "ip": "*************",
  "start_time": "2024-01-01T00:00:00",
  "end_time": "2024-01-01T23:59:59"
}
```

**响应示例**:
```json
[
  {
    "ip": "*************",
    "timestamp": "2024-01-01T10:00:00+08:00",
    "usage_percent": 75.5
  }
]
```

---

### 网络监控

#### GET /network/interfaces/{ip}
获取指定IP的网卡名称列表

**路径参数**:
- `ip`: 服务器IP地址

**响应示例**:
```json
["eth0", "eth1", "lo"]
```

#### POST /network/stats/data
查询网络统计数据

**请求体**:
```json
{
  "ip": "*************",
  "interface": "eth0",
  "start_time": "2024-01-01T00:00:00",
  "end_time": "2024-01-01T23:59:59"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "query_params": {
      "ip": "*************",
      "interface": "eth0",
      "start_time": "2024-01-01T00:00:00+08:00",
      "end_time": "2024-01-01T23:59:59+08:00"
    },
    "summary": {
      "total_rx_bytes": 1048576,
      "total_tx_bytes": 524288,
      "total_rx_mb": 1.0,
      "total_tx_mb": 0.5,
      "data_points_count": 10,
      "interface": "eth0"
    },
    "data_points": [
      {
        "timestamp": "2024-01-01T10:00:00+08:00",
        "rx_bytes": 104857,
        "tx_bytes": 52428,
        "rx_mb": 0.1,
        "tx_mb": 0.05
      }
    ]
  }
}
```

---

### 自定义监控

#### GET /monitor/items
获取所有监控项列表

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "CPU使用率",
    "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
    "description": "获取CPU使用率",
    "data_type": "number",
    "created_at": "2024-01-01T10:00:00+08:00"
  }
]
```

#### POST /monitor/items
创建新的监控项

**请求体**:
```json
{
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number"
}
```

**响应示例**:
```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number",
  "created_at": "2024-01-01T10:00:00+08:00"
}
```

#### PUT /monitor/items/{item_id}
更新监控项

**路径参数**:
- `item_id`: 监控项ID

**请求体**:
```json
{
  "name": "CPU使用率(更新)",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率(更新版本)",
  "data_type": "number"
}
```

#### DELETE /monitor/items/{item_id}
删除监控项

**路径参数**:
- `item_id`: 监控项ID

**响应示例**:
```json
{
  "code": 200,
  "message": "监控项删除成功"
}
```

#### GET /monitor/items/{item_id}/ips
获取监控项关联的IP列表

**路径参数**:
- `item_id`: 监控项ID

**响应示例**:
```json
[
  {
    "id": 1,
    "monitor_item_id": 1,
    "monitor_item_name": "CPU使用率",
    "ip_address": "*************",
    "created_at": "2024-01-01T10:00:00+08:00"
  }
]
```

#### POST /monitor/items/{item_id}/ips
为监控项添加IP关联

**路径参数**:
- `item_id`: 监控项ID

**请求体**:
```json
{
  "ip_address": "*************"
}
```

#### DELETE /monitor/items/{item_id}/ips/{ip_id}
删除监控项的IP关联

**路径参数**:
- `item_id`: 监控项ID
- `ip_id`: IP关联ID

#### POST /monitor/data
上报监控数据

**请求体**:
```json
{
  "monitor_item_id": 1,
  "ip": "*************",
  "value": "75.5",
  "status": 0
}
```

**参数说明**:
- `monitor_item_id`: 监控项ID
- `ip`: IP地址
- `value`: 监控值
- `status`: 状态 (0-正常, 1-警告, 2-错误)

**响应示例**:
```json
{
  "id": 1,
  "monitor_item_id": 1,
  "monitor_item_name": "CPU使用率",
  "ip": "*************",
  "timestamp": "2024-01-01T10:00:00+08:00",
  "value": "75.5",
  "status": 0
}
```

#### POST /monitor/data/ip
简化的监控数据上报

**路径参数**:
- `ip`: IP地址

**请求体**:
```json
{
  "monitor_item_id": 1,
  "value": "75.5",
  "status": 0
}
```

#### POST /monitor/data/query
查询监控数据

**请求体**:
```json
{
  "ip_address": "*************",
  "start_time": "2024-01-01T00:00:00",
  "end_time": "2024-01-01T23:59:59",
  "status": 0,
  "limit": 20,
  "offset": 0
}
```

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "monitor_item_id": 1,
      "monitor_item_name": "CPU使用率",
      "ip": "*************",
      "timestamp": "2024-01-01T10:00:00+08:00",
      "value": "75.5",
      "status": 0
    }
  ],
  "total": 1,
  "limit": 20,
  "offset": 0
}
```

---

## 数据模型

### 基础模型字段

所有模型都继承自基础模型，包含以下通用字段：

```json
{
  "id": 1,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### IPUser (IP用户)

```json
{
  "id": 1,
  "ip": "*************",
  "username": "admin",
  "password": "password123",
  "is_connectable": true,
  "use_ssh_key": false,
  "ssh_key_path": null,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### GPUStatsSummary (GPU统计汇总)

```json
{
  "id": 1,
  "ip": "*************",
  "total_gpu_count": 8,
  "avg_usage": 75.5,
  "total_memory_used": 8192.0,
  "total_memory_total": 11264.0,
  "memory_usage_percent": 72.7,
  "avg_temperature": 65.0,
  "timestamp": "2024-01-01T10:00:00+08:00",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### GPUStatsDetail (GPU详细统计)

```json
{
  "id": 1,
  "summary_id": 1,
  "gpu_index": 0,
  "gpu_name": "NVIDIA GeForce RTX 3080",
  "gpu_usage": 75.5,
  "gpu_memory_used": 1024.0,
  "gpu_memory_total": 1408.0,
  "gpu_memory_percent": 72.7,
  "gpu_temperature": 65.0,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### MemoryStats (内存统计)

```json
{
  "id": 1,
  "ip": "*************",
  "total": 16384,
  "used": 12288,
  "free": 4096,
  "available": 8192,
  "usage_percent": 75.0,
  "swap_total": 2048,
  "swap_used": 512,
  "timestamp": "2024-01-01T10:00:00+08:00",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### NetworkStatsSummary (网络统计汇总)

```json
{
  "id": 1,
  "ip": "*************",
  "total_interfaces": 3,
  "active_interfaces": 2,
  "timestamp": "2024-01-01T10:00:00+08:00",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### NetworkStatsDetail (网络详细统计)

```json
{
  "id": 1,
  "summary_id": 1,
  "interface_name": "eth0",
  "state": "up",
  "mac_address": "00:11:22:33:44:55",
  "speed": "1000Mb/s",
  "ip_address": "*************",
  "rx_bytes": 1048576,
  "rx_mb": 1.0,
  "rx_packets": 1024,
  "tx_bytes": 524288,
  "tx_mb": 0.5,
  "tx_packets": 512,
  "current_rx_speed": 10.5,
  "current_tx_speed": 5.2,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### CPUStats (CPU统计)

```json
{
  "id": 1,
  "ip": "*************",
  "model": "Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz",
  "cores": 8,
  "frequency": 3600.0,
  "usage_percent": 75.5,
  "user_percent": 45.2,
  "system_percent": 20.3,
  "iowait_percent": 5.0,
  "load_1min": 2.5,
  "load_5min": 2.2,
  "load_15min": 1.8,
  "process_count": 156,
  "timestamp": "2024-01-01T10:00:00+08:00",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### CPUTopProcess (CPU占用最高的进程)

```json
{
  "id": 1,
  "cpu_stats_id": 1,
  "pid": "1234",
  "user": "root",
  "cpu_percent": 25.5,
  "command": "python3 /app/main.py",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### MonitorItem (监控项)

```json
{
  "id": 1,
  "name": "CPU使用率",
  "command": "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
  "description": "获取CPU使用率",
  "data_type": "number",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### MonitorItemIP (监控项IP关联)

```json
{
  "id": 1,
  "monitor_item_id": 1,
  "ip_address": "*************",
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

### MonitorData (监控数据)

```json
{
  "id": 1,
  "monitor_item_id": 1,
  "ip": "*************",
  "timestamp": "2024-01-01T10:00:00+08:00",
  "value": "75.5",
  "status": 0,
  "created_at": "2024-01-01T10:00:00+08:00",
  "updated_at": "2024-01-01T10:00:00+08:00",
  "is_deleted": false
}
```

---

## 时区处理

### 时区策略

系统采用统一的时区处理策略：

1. **内部存储**: 所有时间数据以UTC格式存储在数据库中
2. **API响应**: 所有时间字段自动转换为上海时区 (Asia/Shanghai, UTC+8) 格式返回
3. **时间格式**: 使用ISO 8601格式，包含时区信息 (`YYYY-MM-DDTHH:MM:SS+08:00`)

### 时间字段说明

| 字段名 | 说明 | 格式示例 |
|--------|------|----------|
| `created_at` | 记录创建时间 | `2024-01-01T10:00:00+08:00` |
| `updated_at` | 记录更新时间 | `2024-01-01T10:00:00+08:00` |
| `timestamp` | 数据采集时间 | `2024-01-01T10:00:00+08:00` |

### 时区转换示例

**数据库存储** (UTC):
```
2024-01-01 02:00:00+00:00
```

**API响应** (上海时区):
```json
{
  "timestamp": "2024-01-01T10:00:00+08:00"
}
```

---

## 认证机制

### SSH密钥认证

当 `use_ssh_key` 为 `true` 时，系统使用SSH密钥进行认证：

1. **默认密钥路径**: `~/.ssh/id_rsa`
2. **自定义密钥路径**: 通过 `ssh_key_path` 字段指定
3. **密码字段**: 可以为空

### 密码认证

当 `use_ssh_key` 为 `false` 时，系统使用密码认证：

1. **密码字段**: 必须提供有效密码
2. **安全性**: 密码在数据库中加密存储

### 认证切换

系统支持认证方式的自动切换：

- 当密码字段为空时，自动使用SSH密钥认证
- 当密码字段不为空且 `use_ssh_key` 为 `false` 时，使用密码认证

---

## 使用示例

### 1. 添加服务器并查询GPU数据

```bash
# 1. 添加服务器
curl -X POST "http://localhost:8000/ip/add" \
  -H "Content-Type: application/json" \
  -d '{
    "ip": "*************",
    "user": "admin",
    "password": "password123",
    "use_ssh_key": false
  }'

# 2. 查询GPU监控数据
curl -X POST "http://localhost:8000/gpu/stats" \
  -H "Content-Type: application/json" \
  -d '{
    "ip": "*************",
    "start_time": "2024-01-01T00:00:00",
    "end_time": "2024-01-01T23:59:59",
    "gpu_index": "all"
  }'
```

### 2. 创建自定义监控项

```bash
# 1. 创建监控项
curl -X POST "http://localhost:8000/monitor/items" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "磁盘使用率",
    "command": "df -h / | awk '\''NR==2{print $5}'\'' | sed '\''s/%//'\''",
    "description": "获取根分区磁盘使用率",
    "data_type": "number"
  }'

# 2. 关联IP地址
curl -X POST "http://localhost:8000/monitor/items/1/ips" \
  -H "Content-Type: application/json" \
  -d '{
    "ip_address": "*************"
  }'

# 3. 上报监控数据
curl -X POST "http://localhost:8000/monitor/data" \
  -H "Content-Type: application/json" \
  -d '{
    "monitor_item_id": 1,
    "ip": "*************",
    "value": "75.5",
    "status": 0
  }'
```

---

## 常见问题

### Q: 如何处理时区问题？
A: 系统自动处理时区转换，所有API响应中的时间都是上海时区格式。请求时可以使用任何时区格式，系统会自动转换。

### Q: SSH密钥认证如何配置？
A: 设置 `use_ssh_key: true` 并确保SSH密钥文件存在。如果不指定 `ssh_key_path`，系统使用默认路径 `~/.ssh/id_rsa`。

### Q: 如何查看API文档？
A: 访问 `/docs` 获取Swagger UI文档，或访问 `/redoc` 获取ReDoc文档。

### Q: 监控数据的状态码含义？
A: `0` = 正常，`1` = 警告，`2` = 错误。可以根据业务需求自定义状态码含义。

---

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持GPU、内存、网络监控
- 实现IP用户管理
- 添加自定义监控功能
- 统一时区处理机制
- 支持SSH密钥和密码认证
```
```
