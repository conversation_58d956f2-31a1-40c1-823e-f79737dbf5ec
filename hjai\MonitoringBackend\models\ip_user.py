from tortoise import fields
from datetime import datetime
from models.base_model import BaseModel
from typing import Optional

class IPUser(BaseModel):
    """IP用户模型，存储IP地址和用户信息"""

    ip = fields.CharField(max_length=50, unique=True, description="服务器IP地址")
    username = fields.CharField(max_length=50, description="用户名")
    password = fields.CharField(max_length=128, null=True, description="密码（可为空，使用SSH密钥认证时）")
    is_connectable = fields.BooleanField(description="服务器是否可连通", default=True)

    # SSH密钥相关字段
    use_ssh_key = fields.BooleanField(description="是否使用SSH密钥认证", default=False)
    ssh_key_path = fields.CharField(max_length=255, null=True, description="SSH私钥文件路径（可选，为空时使用默认路径）")

    class Meta:
        table = "ip_users"  # 表名
        description = "IP用户信息表"

    def __str__(self):
        auth_method = "密钥" if self.use_ssh_key else "密码"
        return f"{self.username}@{self.ip} ({auth_method}认证)"

    def get_auth_method(self) -> str:
        """获取认证方式描述"""
        if self.use_ssh_key:
            return "SSH密钥认证"
        elif self.password:
            return "密码认证"
        else:
            return "未配置认证"

    def should_use_ssh_key(self) -> bool:
        """判断是否应该使用SSH密钥认证"""
        # 如果明确设置使用SSH密钥，则使用
        if self.use_ssh_key:
            return True
        # 如果密码为空或None，则自动使用SSH密钥认证
        if not self.password:
            return True
        return False