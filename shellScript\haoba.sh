#!/bin/bash

# 结果文件
RESULT_FILE="bench_results.txt"
FINAL_TABLE="result.md"

# 错误处理函数
error() {
    echo "错误: CUDA环境检查失败，无法运行测试。" | tee -a "$RESULT_FILE"
    echo "请确保CUDA工具包已正确安装，并且库文件路径已正确设置。" | tee -a "$RESULT_FILE"

    # 生成错误报告
    echo "| 测试类型 | 矩阵尺寸 (M×N×K) | 实测性能 (TFLOPS) |" > "$FINAL_TABLE"
    echo "|----------|-------------------|-------------------|" >> "$FINAL_TABLE"

    for t in "${TEST_TYPES[@]}"; do
        echo "| $t       | 环境错误            | N/A               |" >> "$FINAL_TABLE"
    done

    cat "$FINAL_TABLE"
    exit 1
}

# 生成报告函数
generate_report() {
    # 生成表格头
    echo "| 测试类型 | 矩阵尺寸 (M×N×K) | 实测性能 (TFLOPS) |" > "$FINAL_TABLE"
    echo "|----------|-------------------|-------------------|" >> "$FINAL_TABLE"

    # 提取结果数据并填充表格
    for t in "${TEST_TYPES[@]}"; do
        found=0
        dimensions="N/A"
        perf="N/A"
        
        # 根据测试类型设置矩阵尺寸
        if [ "$t" = "FP8" ]; then dimensions="15360×18176×8192"; fi
        if [ "$t" = "INT8" ]; then dimensions="40960×52548×16384"; fi
        if [ "$t" = "FP16" ]; then dimensions="15360×18176×8192"; fi
        if [ "$t" = "BF16" ]; then dimensions="15360×18176×16384"; fi
        if [ "$t" = "TF32" ]; then dimensions="15360×18176×4096"; fi
        if [ "$t" = "FP32" ]; then dimensions="15360×18176×4096"; fi
        
        # 查找性能结果
        current_type=""
        while IFS= read -r line; do
            if [[ $line == *"Running $t GEMM"* ]]; then
                current_type="$t"
            fi
            
            if [[ $line == *"CUDA : elapsed"* ]] && [[ "$current_type" == "$t" ]]; then
                gflops=$(echo "$line" | grep -o 'Gflops = [0-9.]*' | awk '{print $3}')
                if [ -n "$gflops" ]; then
                    tflops=$(echo "$gflops/1000" | bc -l | awk '{printf "%.3f", $0}')
                    perf="$tflops"
                    found=1
                    break
                fi
            fi
        done < "$RESULT_FILE"
        
        echo "| $t       | $dimensions      | $perf             |" >> "$FINAL_TABLE"
    done

    echo "测试结果已保存到 $FINAL_TABLE"
    cat "$FINAL_TABLE"
}

# 清空旧文件
> "$RESULT_FILE"
> "$FINAL_TABLE"

# 预定义测试类型顺序
TEST_TYPES=("FP8" "INT8" "FP16" "BF16" "TF32" "FP32")

echo "检查CUDA环境..." | tee -a "$RESULT_FILE"

# 检查CUDA环境
CUDA_PATH=""

# 检查常见CUDA安装路径
for path in "/usr/local/cuda" "/opt/cuda"; do
    if [ -d "$path" ]; then
        for lib in "$path/lib64/libcublas.so"* "$path/lib/libcublas.so"*; do
            if [ -f "$lib" ]; then
                CUDA_PATH="$path"
                echo "找到CUDA路径: $CUDA_PATH" | tee -a "$RESULT_FILE"
                break 2
            fi
        done
    fi
done

# 如果找到CUDA路径，设置环境变量
if [ -n "$CUDA_PATH" ]; then
    export LD_LIBRARY_PATH="$CUDA_PATH/lib64:$CUDA_PATH/lib:$LD_LIBRARY_PATH"
    export PATH="$CUDA_PATH/bin:$PATH"
    echo "已添加CUDA路径到系统PATH: $CUDA_PATH/bin" | tee -a "$RESULT_FILE"
    
    # 检查可执行文件是否存在
    if [ ! -f "./cublasMatmulBench" ] || [ ! -x "./cublasMatmulBench" ]; then
        echo "错误: cublasMatmulBench 可执行文件不存在或没有执行权限" | tee -a "$RESULT_FILE"
        exit 1
    fi
    
    echo "CUDA环境检查通过，开始运行测试..." | tee -a "$RESULT_FILE"
    
    # 运行所有测试
    echo "Running FP8 GEMM..." | tee -a "$RESULT_FILE"
    ./cublasMatmulBench -P=qqssq -m=15360 -n=18176 -k=8192 -T=1000 -ta=1 -B=0 -p=0 | tee -a "$RESULT_FILE"
    
    echo "Running INT8 GEMM..." | tee -a "$RESULT_FILE"
    ./cublasMatmulBench -P=bisb_imma -m=40960 -n=52548 -k=16384 -T=1000 -ta=1 -B=0 -p=0 | tee -a "$RESULT_FILE"
    
    echo "Running FP16 GEMM..." | tee -a "$RESULT_FILE"
    ./cublasMatmulBench -P=hsh -m=15360 -n=18176 -k=8192 -T=1000 -tb=1 -B=0 -p=0 | tee -a "$RESULT_FILE"
    
    echo "Running BF16 GEMM..." | tee -a "$RESULT_FILE"
    ./cublasMatmulBench -P=tst -m=15360 -n=18176 -k=16384 -T=1000 -tb=1 -B=0 -p=0 | tee -a "$RESULT_FILE"
    
    echo "Running TF32 GEMM..." | tee -a "$RESULT_FILE"
    ./cublasMatmulBench -P=sss_fast_tf32 -m=15360 -n=18176 -k=4096 -T=1000 -tb=1 -B=0 -p=0 | tee -a "$RESULT_FILE"
    
    echo "Running FP32 GEMM..." | tee -a "$RESULT_FILE"
    ./cublasMatmulBench -P=sss -m=15360 -n=18176 -k=4096 -T=1000 -tb=1 -B=0 -p=0 | tee -a "$RESULT_FILE"
    
    # 生成报告
    generate_report
else
    echo "错误: 找不到CUDA安装路径" | tee -a "$RESULT_FILE"
    exit 1
fi

exit 0